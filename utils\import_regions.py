#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
导入中国省市县数据到数据库
"""

import os
import sys
import json
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from models import db
from models.system import Region
from app import create_app

def import_regions():
    """导入中国省市县数据到数据库"""
    app = create_app()
    with app.app_context():
        # 检查是否已有数据
        existing_count = Region.query.count()
        if existing_count > 0:
            print(f"数据库中已存在 {existing_count} 条区域数据，跳过导入")
            return
        
        # 从静态文件读取省市县数据
        js_file_path = os.path.join(os.path.dirname(__file__), '..', 'static', 'js', 'china-area-data.js')
        
        if not os.path.exists(js_file_path):
            print(f"未找到区域数据文件: {js_file_path}")
            return
        
        print(f"正在从文件导入区域数据: {js_file_path}")
        
        # 读取JS文件内容
        with open(js_file_path, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # 提取JSON数据
        # 这里使用简单的字符串处理，实际项目中可能需要更复杂的解析
        provinces_start = js_content.find('provinces: [')
        provinces_end = js_content.find('],', provinces_start)
        provinces_json = js_content[provinces_start+11:provinces_end+1]
        
        cities_start = js_content.find('cities: {')
        cities_end = js_content.find('},', cities_start)
        cities_json = js_content[cities_start+8:cities_end+1]
        
        districts_start = js_content.find('districts: {')
        districts_end = js_content.find('}', districts_start)
        districts_json = js_content[districts_start+11:districts_end+1]
        
        # 将提取的字符串转换为Python对象
        try:
            # 处理省份数据
            provinces_str = provinces_json.replace('id:', '"id":').replace('name:', '"name":')
            provinces_data = json.loads(provinces_str)
            
            # 处理城市数据
            cities_str = cities_json.replace('id:', '"id":').replace('name:', '"name":')
            cities_str = cities_str.replace('"', '\\"').replace('\\\"', '\"')
            cities_str = cities_str.replace('"{', '{').replace('}"', '}')
            cities_str = cities_str.replace("'", '"')
            cities_data = json.loads('{' + cities_str + '}')
            
            # 处理区县数据
            districts_str = districts_json.replace('id:', '"id":').replace('name:', '"name":')
            districts_str = districts_str.replace('"', '\\"').replace('\\\"', '\"')
            districts_str = districts_str.replace('"{', '{').replace('}"', '}')
            districts_str = districts_str.replace("'", '"')
            districts_data = json.loads('{' + districts_str + '}')
            
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {str(e)}")
            return
        
        # 开始导入数据
        try:
            # 导入省份数据
            provinces = []
            for idx, province in enumerate(provinces_data):
                region = Region(
                    id=province['id'],
                    name=province['name'],
                    parent_id='0',
                    level=1,
                    sort_order=idx
                )
                provinces.append(region)
            
            db.session.add_all(provinces)
            db.session.commit()
            print(f"已导入 {len(provinces)} 个省份数据")
            
            # 导入城市数据
            cities = []
            for province_id, city_list in cities_data.items():
                for idx, city in enumerate(city_list):
                    region = Region(
                        id=city['id'],
                        name=city['name'],
                        parent_id=province_id,
                        level=2,
                        sort_order=idx
                    )
                    cities.append(region)
            
            db.session.add_all(cities)
            db.session.commit()
            print(f"已导入 {len(cities)} 个城市数据")
            
            # 导入区县数据
            districts = []
            for city_id, district_list in districts_data.items():
                for idx, district in enumerate(district_list):
                    region = Region(
                        id=district['id'],
                        name=district['name'],
                        parent_id=city_id,
                        level=3,
                        sort_order=idx
                    )
                    districts.append(region)
            
            db.session.add_all(districts)
            db.session.commit()
            print(f"已导入 {len(districts)} 个区县数据")
            
            print(f"数据导入完成，共导入 {len(provinces) + len(cities) + len(districts)} 条记录")
            
        except Exception as e:
            db.session.rollback()
            print(f"导入数据失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    import_regions() 