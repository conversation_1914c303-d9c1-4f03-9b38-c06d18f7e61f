{% extends "admin/base.html" %}

{% block title %}{{ title }} - 管理后台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ title }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('admin.quantity_discounts') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>返回列表
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.product_id.label(class="form-label") }}
                        {{ form.product_id(class="form-select") }}
                        {% if form.product_id.errors %}
                        <div class="text-danger small">
                            {% for error in form.product_id.errors %}
                            <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                        {% endif %}
                        <div class="form-text">选择要设置数量折扣的商品</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.min_quantity.label(class="form-label") }}
                                {{ form.min_quantity(class="form-control") }}
                                {% if form.min_quantity.errors %}
                                <div class="text-danger small">
                                    {% for error in form.min_quantity.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">折扣生效的最小数量</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.max_quantity.label(class="form-label") }}
                                {{ form.max_quantity(class="form-control") }}
                                {% if form.max_quantity.errors %}
                                <div class="text-danger small">
                                    {% for error in form.max_quantity.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">折扣生效的最大数量，留空表示无上限</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.discount_type.label(class="form-label") }}
                                {{ form.discount_type(class="form-select", id="discountType") }}
                                {% if form.discount_type.errors %}
                                <div class="text-danger small">
                                    {% for error in form.discount_type.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.discount_value.label(class="form-label") }}
                                <div class="input-group">
                                    <span class="input-group-text" id="discountPrefix">¥</span>
                                    {{ form.discount_value(class="form-control", step="0.01") }}
                                    <span class="input-group-text" id="discountSuffix" style="display: none;">%</span>
                                </div>
                                {% if form.discount_value.errors %}
                                <div class="text-danger small">
                                    {% for error in form.discount_value.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">折扣的具体数值</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">状态</label>
                        <div class="form-check">
                            {{ form.is_active(class="form-check-input") }}
                            {{ form.is_active.label(class="form-check-label") }}
                        </div>
                        {% if form.is_active.errors %}
                        <div class="text-danger small">
                            {% for error in form.is_active.errors %}
                            <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('admin.quantity_discounts') }}" class="btn btn-secondary">取消</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>帮助信息
                </h6>
            </div>
            <div class="card-body">
                <h6>数量折扣说明</h6>
                <ul class="small">
                    <li><strong>最小数量</strong>：达到此数量时开始享受折扣</li>
                    <li><strong>最大数量</strong>：超过此数量时折扣失效，留空表示无上限</li>
                    <li><strong>折扣类型</strong>：选择按固定金额还是百分比折扣</li>
                    <li><strong>折扣值</strong>：具体的折扣数值</li>
                </ul>
                
                <h6 class="mt-3">折扣类型</h6>
                <ul class="small">
                    <li><strong>固定金额</strong>：减少固定的金额，如减10元</li>
                    <li><strong>百分比</strong>：按总价的百分比减少，如减10%</li>
                </ul>
                
                <h6 class="mt-3">示例</h6>
                <div class="small">
                    <p><strong>购买10-49个</strong>：减5元</p>
                    <p><strong>购买50个以上</strong>：减10%</p>
                    <p><strong>购买100个以上</strong>：减20元</p>
                </div>
                
                <div class="alert alert-warning small mt-3">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    注意：数量区间不能重叠，系统会自动检查冲突。
                </div>
            </div>
        </div>
        
        {% if discount %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>当前折扣信息
                </h6>
            </div>
            <div class="card-body">
                <p><strong>商品</strong>：{{ discount.product.name }}</p>
                <p><strong>数量区间</strong>：{{ discount.min_quantity }}{% if discount.max_quantity %} - {{ discount.max_quantity }}{% else %}+{% endif %}</p>
                <p><strong>折扣</strong>：
                    {% if discount.discount_type == 'percentage' %}
                        {{ discount.discount_value }}%
                    {% else %}
                        ¥{{ "%.2f"|format(discount.discount_value) }}
                    {% endif %}
                </p>
                <p><strong>状态</strong>：
                    <span class="badge bg-{{ 'success' if discount.is_active else 'secondary' }}">
                        {{ '启用' if discount.is_active else '禁用' }}
                    </span>
                </p>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const discountType = document.getElementById('discountType');
    const discountPrefix = document.getElementById('discountPrefix');
    const discountSuffix = document.getElementById('discountSuffix');
    
    function updateDiscountDisplay() {
        if (discountType.value === 'percentage') {
            discountPrefix.style.display = 'none';
            discountSuffix.style.display = 'block';
        } else {
            discountPrefix.style.display = 'block';
            discountSuffix.style.display = 'none';
        }
    }
    
    // 初始化显示
    updateDiscountDisplay();
    
    // 监听变化
    discountType.addEventListener('change', updateDiscountDisplay);
});
</script>
{% endblock %}
