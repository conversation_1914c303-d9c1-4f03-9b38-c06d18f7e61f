#!/usr/bin/env python
# -*- coding: utf-8 -*-

from datetime import datetime, timezone, timedelta
from flask import current_app

# 中国时区 (UTC+8)
CHINA_TZ = timezone(timedelta(hours=8))

def utc_to_china(utc_datetime):
    """
    将UTC时间转换为中国时间
    
    Args:
        utc_datetime: UTC时间的datetime对象
        
    Returns:
        转换后的中国时间datetime对象
    """
    if utc_datetime is None:
        return None
    
    # 如果传入的datetime对象没有时区信息，假设它是UTC时间
    if utc_datetime.tzinfo is None:
        utc_datetime = utc_datetime.replace(tzinfo=timezone.utc)
    
    # 转换为中国时区
    china_time = utc_datetime.astimezone(CHINA_TZ)
    return china_time

def now_china():
    """
    获取当前中国时间
    
    Returns:
        当前中国时间的datetime对象
    """
    return datetime.now(CHINA_TZ)

def format_china_time(utc_datetime, format_str='%Y-%m-%d %H:%M:%S'):
    """
    将UTC时间转换为中国时间并格式化
    
    Args:
        utc_datetime: UTC时间的datetime对象
        format_str: 时间格式字符串
        
    Returns:
        格式化后的中国时间字符串
    """
    china_time = utc_to_china(utc_datetime)
    if china_time is None:
        return ''
    return china_time.strftime(format_str)

def format_china_date(utc_datetime, format_str='%Y年%m月%d日 %H:%M'):
    """
    将UTC时间转换为中国时间并格式化为中文日期
    
    Args:
        utc_datetime: UTC时间的datetime对象
        format_str: 时间格式字符串
        
    Returns:
        格式化后的中国时间字符串
    """
    return format_china_time(utc_datetime, format_str)

def format_china_datetime_short(utc_datetime):
    """
    将UTC时间转换为中国时间并格式化为短格式 (月-日 时:分)
    
    Args:
        utc_datetime: UTC时间的datetime对象
        
    Returns:
        格式化后的中国时间字符串
    """
    return format_china_time(utc_datetime, '%m-%d %H:%M')

def get_china_timestamp():
    """
    获取当前中国时间戳（用于文件名等）
    
    Returns:
        格式化的时间戳字符串 (YYYYMMDD_HHMMSS)
    """
    return now_china().strftime('%Y%m%d_%H%M%S') 