{% extends "admin/base.html" %}

{% block title %}商品管理 - 管理后台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">商品管理</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('admin.add_product') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>添加商品
        </a>
    </div>
</div>

<div class="row">
    <!-- 左侧分类筛选器 -->
    <div class="col-md-3">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-folder me-2"></i>商品分类
                </h6>
                <button class="btn btn-sm btn-outline-secondary" onclick="clearCategoryFilter()" title="清除筛选">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="card-body p-0">
                <!-- 全部商品选项 -->
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('admin.products', search=search) }}" 
                       class="list-group-item list-group-item-action d-flex justify-content-between align-items-center {% if current_category_id == 0 %}active{% endif %}">
                        <span>
                            <i class="fas fa-home me-2"></i>全部商品
                        </span>
                        <span class="badge bg-primary rounded-pill">{{ products.total }}</span>
                    </a>
                </div>

                <!-- 分类树 -->
                {% if category_tree %}
                <div class="category-tree" style="max-height: 500px; overflow-y: auto;">
                    {% macro render_category_tree(categories, level=0) %}
                        {% for item in categories %}
                            <div class="category-item" data-category-id="{{ item.category.id }}">
                                <div class="d-flex align-items-center category-link" 
                                     style="padding-left: {{ (level * 20 + 15) }}px; padding-top: 8px; padding-bottom: 8px; padding-right: 15px;">
                                    {% if item.has_children %}
                                    <button class="btn btn-sm p-0 me-2 toggle-btn" onclick="toggleCategory({{ item.category.id }})" style="width: 16px;">
                                        <i class="fas fa-chevron-right" id="icon-{{ item.category.id }}"></i>
                                    </button>
                                    {% else %}
                                    <span style="width: 24px; display: inline-block;"></span>
                                    {% endif %}
                                    
                                    <a href="{{ url_for('admin.products', category_id=item.category.id, search=search) }}" 
                                       class="text-decoration-none flex-grow-1 d-flex justify-content-between align-items-center {% if current_category_id == item.category.id %}text-primary fw-bold{% else %}text-dark{% endif %}">
                                        <span>
                                            <i class="fas fa-folder me-1"></i>{{ item.category.name }}
                                        </span>
                                        {% if item.product_count > 0 %}
                                        <span class="badge bg-light text-dark rounded-pill">{{ item.product_count }}</span>
                                        {% endif %}
                                    </a>
                                    
                                    <!-- 添加商品按钮 -->
                                    <a href="{{ url_for('admin.add_product', category_id=item.category.id) }}" 
                                       class="btn btn-sm btn-outline-success ms-2 add-product-btn" 
                                       title="为《{{ item.category.name }}》分类添加商品" 
                                       data-bs-toggle="tooltip">
                                        <i class="fas fa-plus"></i>
                                    </a>
                                </div>
                                
                                {% if item.has_children %}
                                <div class="category-children" id="children-{{ item.category.id }}" style="display: none;">
                                    {{ render_category_tree(item.children, level + 1) }}
                                </div>
                                {% endif %}
                            </div>
                        {% endfor %}
                    {% endmacro %}
                    
                    {{ render_category_tree(category_tree) }}
                </div>
                {% else %}
                <div class="p-3 text-center text-muted">
                    <i class="fas fa-folder-open fa-2x mb-2"></i>
                    <div>暂无分类</div>
                    <a href="{{ url_for('admin.categories') }}" class="btn btn-sm btn-outline-primary mt-2">
                        添加分类
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 右侧商品列表 -->
    <div class="col-md-9">
        <!-- 搜索和筛选条 -->
        <div class="card mb-3">
            <div class="card-body py-2">
                <form method="GET" class="d-flex align-items-center">
                    <input type="hidden" name="category_id" value="{{ current_category_id }}">
                    <div class="input-group me-3">
                        <input type="text" name="search" class="form-control" placeholder="搜索商品名称或描述..." 
                               value="{{ search }}" autocomplete="off">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    {% if search %}
                    <a href="{{ url_for('admin.products', category_id=current_category_id) }}" 
                       class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-times me-1"></i>清除搜索
                    </a>
                    {% endif %}
                </form>
            </div>
        </div>

        <!-- 当前状态显示 -->
        {% if current_category_id or search %}
        <div class="mb-3">
            <div class="d-flex align-items-center text-muted">
                <i class="fas fa-filter me-2"></i>
                <span>当前显示：</span>
                {% if current_category_id %}
                    {% for cat in category_tree %}
                        {% if cat.category.id == current_category_id %}
                        <span class="badge bg-primary mx-1">{{ cat.category.name }}</span>
                        {% endif %}
                    {% endfor %}
                {% else %}
                <span class="badge bg-secondary mx-1">全部分类</span>
                {% endif %}
                {% if search %}
                <span class="badge bg-info mx-1">搜索: "{{ search }}"</span>
                {% endif %}
                <span class="mx-2">共 {{ products.total }} 个商品</span>
            </div>
        </div>
        {% endif %}

        <!-- 商品列表 -->
<div class="card">
    <div class="card-body">
        {% if products.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>图片</th>
                        <th>商品名称</th>
                        <th>分类</th>
                        <th>基础价格</th>
                        <th>单位</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for product in products.items %}
                    <tr>
                        <td>{{ product.id }}</td>
                        <td>
                                    {% if product.image_url %}
                                    <img src="{{ product.image_url }}" alt="{{ product.name }}" 
                                         style="width: 50px; height: 50px; object-fit: cover; border-radius: 4px;">
                                    {% else %}
                                    <div class="text-muted">
                                        <i class="fas fa-image"></i>
                                    </div>
                                    {% endif %}
                        </td>
                        <td>
                            <strong>{{ product.name }}</strong>
                            {% if product.description %}
                                    <br><small class="text-muted">{{ product.description[:50] }}...</small>
                            {% endif %}
                        </td>
                                <td>{{ product.category.name if product.category else '-' }}</td>
                        <td>¥{{ "%.2f"|format(product.base_price) }}</td>
                        <td>{{ product.unit }}</td>
                        <td>
                                    <span class="badge bg-{% if product.is_active %}success{% else %}secondary{% endif %}">
                                        {% if product.is_active %}启用{% else %}禁用{% endif %}
                            </span>
                        </td>
                        <td>{{ product.created_at.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <a href="{{ url_for('admin.edit_product', id=product.id) }}" class="btn btn-sm btn-outline-primary">编辑</a>
                            <button type="button" class="btn btn-sm btn-outline-danger delete-btn"
                                    data-url="{{ url_for('admin.delete_product', id=product.id) }}"
                                    data-name="{{ product.name }}">删除</button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        {% if products.pages > 1 %}
        <nav aria-label="商品分页">
            <ul class="pagination justify-content-center">
                {% if products.has_prev %}
                <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.products', page=products.prev_num, category_id=current_category_id, search=search) }}">上一页</a>
                </li>
                {% endif %}

                {% for page_num in products.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != products.page %}
                        <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin.products', page=page_num, category_id=current_category_id, search=search) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if products.has_next %}
                <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.products', page=products.next_num, category_id=current_category_id, search=search) }}">下一页</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-box fa-3x text-muted mb-3"></i>
                    <h5>
                        {% if search or current_category_id %}
                        没有找到符合条件的商品
                        {% else %}
                        暂无商品
                        {% endif %}
                    </h5>
                    <p class="text-muted">
                        {% if search or current_category_id %}
                        请尝试调整搜索条件或分类筛选
                        {% else %}
                        还没有创建任何商品
                        {% endif %}
                    </p>
                    <div>
                        {% if search or current_category_id %}
                        <a href="{{ url_for('admin.products') }}" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-list me-2"></i>查看全部商品
                        </a>
                        {% endif %}
            <a href="{{ url_for('admin.add_product') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>添加商品
            </a>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除商品 "<span id="deleteName"></span>" 吗？</p>
                <p class="text-danger small">删除后无法恢复，请谨慎操作。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 分类树展开/折叠功能
function toggleCategory(categoryId) {
    const childrenDiv = document.getElementById('children-' + categoryId);
    const icon = document.getElementById('icon-' + categoryId);
    
    if (childrenDiv.style.display === 'none') {
        childrenDiv.style.display = 'block';
        icon.className = 'fas fa-chevron-down';
    } else {
        childrenDiv.style.display = 'none';
        icon.className = 'fas fa-chevron-right';
    }
}

// 清除分类筛选
function clearCategoryFilter() {
    const currentParams = new URLSearchParams(window.location.search);
    currentParams.delete('category_id');
    window.location.href = window.location.pathname + '?' + currentParams.toString();
}

// 自动展开当前选中分类的父级
document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    const currentCategoryId = {{ current_category_id }};
    if (currentCategoryId > 0) {
        // 找到当前选中分类的父级并展开
        const currentCategoryItem = document.querySelector('[data-category-id="' + currentCategoryId + '"]');
        if (currentCategoryItem) {
            let parent = currentCategoryItem.parentElement;
            while (parent) {
                if (parent.classList.contains('category-children')) {
                    parent.style.display = 'block';
                    const parentId = parent.id.replace('children-', '');
                    const icon = document.getElementById('icon-' + parentId);
                    if (icon) {
                        icon.className = 'fas fa-chevron-down';
                    }
                }
                parent = parent.parentElement;
            }
        }
    }

    // 为添加商品按钮添加点击效果
    document.querySelectorAll('.add-product-btn').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            // 添加点击反馈
            this.classList.add('clicked');
            setTimeout(() => {
                this.classList.remove('clicked');
            }, 200);
        });
    });
});

// 删除功能 - 简化版本
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 初始化产品页面删除功能 - 简化版');
    
    // 简化的删除功能初始化
    initSimpleDeleteFunction();
});

function initSimpleDeleteFunction() {
    const deleteModal = document.getElementById('deleteModal');
    const deleteNameSpan = document.getElementById('deleteName');
    const confirmDeleteBtn = document.getElementById('confirmDelete');
    
    if (!deleteModal) {
        console.error('删除模态框未找到');
        return;
    }
    
    let currentDeleteUrl = '';
    let currentDeleteName = '';
    
    // 绑定删除按钮事件 - 使用事件委托
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('delete-btn')) {
            e.preventDefault();
            e.stopPropagation();
            
            currentDeleteUrl = e.target.getAttribute('data-url');
            currentDeleteName = e.target.getAttribute('data-name');
            
            console.log('点击删除按钮:', currentDeleteName, currentDeleteUrl);
            
            if (deleteNameSpan) {
                deleteNameSpan.textContent = currentDeleteName;
            }
            
            // 使用Bootstrap 5的方式显示模态框
            const modal = new bootstrap.Modal(deleteModal);
            modal.show();
        }
    });
    
    // 确认删除事件
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            if (!currentDeleteUrl) {
                alert('删除URL无效');
                return;
            }
            
            // 禁用按钮，显示加载状态
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>删除中...';
            
            console.log('发送删除请求:', currentDeleteUrl);
            
            // 发送删除请求 - 简化版本，不使用CSRF token
            fetch(currentDeleteUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Accept': 'application/json'
                },
                credentials: 'same-origin',
                body: 'confirm=true'  // 简单的确认参数
            })
            .then(response => {
                console.log('响应状态:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('删除响应:', data);
                
                if (data.success) {
                    // 显示成功消息
                    alert('删除成功！页面即将刷新。');
                    // 立即刷新页面
                    window.location.reload();
                } else {
                    alert('删除失败: ' + (data.message || '未知错误'));
                    this.disabled = false;
                    this.innerHTML = '确认删除';
                }
            })
            .catch(error => {
                console.error('删除请求错误:', error);
                alert('删除失败: ' + error.message);
                this.disabled = false;
                this.innerHTML = '确认删除';
            });
            
            // 隐藏模态框
            const modal = bootstrap.Modal.getInstance(deleteModal);
            if (modal) {
                modal.hide();
            }
        });
    }
}
</script>
{% endblock %}
