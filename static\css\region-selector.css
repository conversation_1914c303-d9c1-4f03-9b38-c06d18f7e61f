/**
 * 省市县选择器样式
 */

/* 选择框样式 */
.region-selector select {
    display: block;
    width: 100%;
    padding: 0.5rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #333333; /* 更深的文字颜色 */
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23212529' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
}

.region-selector select:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.region-selector select:disabled {
    background-color: #f8f9fa;
    opacity: 0.7;
    cursor: not-allowed;
}

/* 加载中状态 */
.region-selector select.loading {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23212529' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2v1z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
    to { transform: rotate(360deg); }
}

/* 错误提示 */
.region-selector-error {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* 布局样式 */
.region-selector-row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -0.5rem;
    margin-left: -0.5rem;
}

.region-selector-col {
    flex: 1 0 0%;
    padding-right: 0.5rem;
    padding-left: 0.5rem;
    margin-bottom: 1rem;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .region-selector-row {
        flex-direction: column;
    }
    
    .region-selector-col {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* 标签样式 */
.region-selector label {
    display: inline-block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333333;
}

/* 必填标记 */
.region-selector .required-mark {
    color: #dc3545;
    margin-left: 0.25rem;
}

/* 选择器容器 */
.region-selector {
    margin-bottom: 1rem;
}

/* 选择器组 */
.region-selector-group {
    margin-bottom: 1.5rem;
}

/* 选择器组标题 */
.region-selector-group-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #333333;
}

/* 选择器组内容 */
.region-selector-group-content {
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    padding: 1rem;
}

/* 选中状态 */
.region-selector select option:checked {
    background-color: #0d6efd;
    color: white;
}