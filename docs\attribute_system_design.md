# 商品属性系统设计方案

## 📋 系统概述

设计一个灵活的商品属性系统，支持标准属性和用户自定义属性，以及复杂的价格计算机制。

## 🏗️ 属性类型分类

### 1. 标准属性（Standard Attributes）
预定义的选项，用户只能从中选择：

#### 1.1 单选属性
- **类型**: `radio` / `select`
- **用例**: 尺寸（A4/A3/A5）、材质（铜版纸/哑粉纸）
- **价格**: 固定价格调整或百分比

#### 1.2 多选属性
- **类型**: `checkbox`
- **用例**: 工艺选择（覆膜+UV+烫金）
- **价格**: 累加各选项价格

#### 1.3 数量选择属性
- **类型**: `quantity_select`
- **用例**: 印刷数量（100/500/1000/自定义）
- **价格**: 阶梯定价或公式计算

### 2. 自定义属性（Custom Attributes）
用户可以输入自定义内容：

#### 2.1 文本输入
- **类型**: `text_input`
- **用例**: 个性化文字、公司名称
- **价格**: 按字符数或固定价格

#### 2.2 数字输入
- **类型**: `number_input`
- **用例**: 自定义尺寸、自定义数量
- **价格**: 公式计算（如：长×宽×单价）

#### 2.3 文件上传
- **类型**: `file_upload`
- **用例**: 设计文件、LOGO
- **价格**: 固定价格或按文件大小

#### 2.4 长文本输入
- **类型**: `textarea`
- **用例**: 特殊要求、备注
- **价格**: 通常不影响价格

## 💰 价格计算机制

### 1. 基础价格类型
- **固定价格** (`fixed`): +¥10.00
- **百分比** (`percentage`): +20%
- **公式计算** (`formula`): quantity * 0.5 + base_price * 0.1

### 2. 高级价格计算
- **阶梯定价** (`tiered`): 不同数量区间不同价格
- **组合定价** (`combination`): 多个属性组合的特殊价格
- **动态定价** (`dynamic`): 基于实时因素的价格调整

### 3. 价格计算优先级
1. 基础商品价格
2. 标准属性价格调整
3. 自定义属性价格计算
4. 数量折扣
5. 组合优惠
6. 会员折扣

## 🔧 技术实现要点

### 1. 数据库扩展
```sql
-- 扩展 attributes 表
ALTER TABLE attributes ADD COLUMN attribute_type ENUM(
    'radio', 'select', 'checkbox', 'quantity_select',
    'text_input', 'number_input', 'file_upload', 'textarea'
) DEFAULT 'select';

-- 添加验证规则
ALTER TABLE attributes ADD COLUMN validation_rules JSON;
-- 例如: {"min_length": 1, "max_length": 50, "pattern": "^[a-zA-Z0-9]+$"}

-- 添加价格计算规则
ALTER TABLE attributes ADD COLUMN price_calculation_rules JSON;
-- 例如: {"type": "tiered", "tiers": [{"min": 1, "max": 100, "price": 0.5}]}
```

### 2. 前端组件设计
- **动态渲染**: 根据属性类型渲染不同的输入组件
- **实时计算**: 用户选择/输入时实时更新价格
- **验证反馈**: 实时验证用户输入并给出反馈

### 3. 后端API设计
- **属性获取**: `/api/products/{id}/attributes`
- **价格计算**: `/api/products/{id}/calculate-price`
- **属性验证**: `/api/attributes/validate`

## 📝 使用示例

### 示例1: 名片印刷
```json
{
  "standard_attributes": [
    {
      "group": "尺寸",
      "type": "radio",
      "options": [
        {"value": "90x54", "label": "标准名片(90×54mm)", "price_modifier": 0},
        {"value": "85x54", "label": "欧式名片(85×54mm)", "price_modifier": 2.0}
      ]
    },
    {
      "group": "材质",
      "type": "select",
      "options": [
        {"value": "300g_coated", "label": "300g铜版纸", "price_modifier": 0},
        {"value": "350g_art", "label": "350g艺术纸", "price_modifier": 5.0}
      ]
    }
  ],
  "custom_attributes": [
    {
      "group": "个性化",
      "type": "text_input",
      "label": "公司名称",
      "required": true,
      "validation": {"max_length": 50},
      "price_formula": "text_length * 0.1"
    },
    {
      "group": "数量",
      "type": "number_input",
      "label": "印刷数量",
      "min": 100,
      "max": 10000,
      "step": 100,
      "price_formula": "quantity < 500 ? quantity * 0.5 : quantity * 0.4"
    }
  ]
}
```

### 示例2: 海报印刷
```json
{
  "standard_attributes": [
    {
      "group": "工艺",
      "type": "checkbox",
      "multiple": true,
      "options": [
        {"value": "lamination", "label": "覆膜", "price_modifier": 10.0},
        {"value": "uv", "label": "UV工艺", "price_modifier": 15.0},
        {"value": "emboss", "label": "烫金", "price_modifier": 25.0}
      ]
    }
  ],
  "custom_attributes": [
    {
      "group": "尺寸",
      "type": "number_input",
      "label": "宽度(cm)",
      "min": 10,
      "max": 200,
      "price_variable": "width"
    },
    {
      "group": "尺寸", 
      "type": "number_input",
      "label": "高度(cm)",
      "min": 10,
      "max": 300,
      "price_variable": "height"
    },
    {
      "group": "设计",
      "type": "file_upload",
      "label": "设计文件",
      "accept": ".pdf,.ai,.psd",
      "max_size": "50MB",
      "price_modifier": 0
    }
  ],
  "price_formula": "base_price + (width * height * 0.01) + standard_attributes_total"
}
```

## 🎯 下一步实现计划

1. **扩展数据库模型** - 添加新的属性类型支持
2. **创建管理界面** - 让管理员可以配置各种属性
3. **实现前端组件** - 动态渲染和实时计算
4. **完善价格引擎** - 支持复杂的价格计算规则
5. **测试和优化** - 确保各种场景下的正确性

这个设计方案将为您提供一个非常灵活和强大的商品属性系统！
