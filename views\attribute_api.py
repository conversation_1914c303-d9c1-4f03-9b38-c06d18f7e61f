#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
商品属性API
提供属性管理和价格计算的API接口
"""

from flask import Blueprint, request, jsonify
from models import db
from models.product import Product, Attribute, AttributeGroup
from models.order import ProductAttribute
from decimal import Decimal
import json

attribute_api = Blueprint('attribute_api', __name__, url_prefix='/api/attributes')

@attribute_api.route('/product/<int:product_id>', methods=['GET'])
def get_product_attributes(product_id):
    """获取商品的所有属性组和属性"""
    try:
        product = Product.query.get_or_404(product_id)
        
        # 获取商品关联的属性组
        attribute_groups = db.session.query(AttributeGroup).join(
            Attribute, AttributeGroup.id == Attribute.group_id
        ).join(
            ProductAttribute, Attribute.id == ProductAttribute.attribute_id
        ).filter(
            ProductAttribute.product_id == product_id,
            AttributeGroup.is_active == True
        ).distinct().order_by(AttributeGroup.sort_order).all()
        
        result = []
        for group in attribute_groups:
            # 获取该组下的所有属性
            attributes = db.session.query(Attribute).join(
                ProductAttribute, Attribute.id == ProductAttribute.attribute_id
            ).filter(
                Attribute.group_id == group.id,
                ProductAttribute.product_id == product_id,
                Attribute.is_active == True
            ).order_by(Attribute.sort_order).all()
            
            group_data = {
                'id': group.id,
                'name': group.name,
                'description': group.description,
                'display_type': group.display_type,
                'attributes': []
            }
            
            for attr in attributes:
                attr_data = {
                    'id': attr.id,
                    'name': attr.name,
                    'value': attr.value,
                    'input_type': attr.input_type,
                    'attribute_category': getattr(attr, 'attribute_category', 'standard'),
                    'price_modifier': float(attr.price_modifier) if attr.price_modifier else 0,
                    'price_modifier_type': attr.price_modifier_type,
                    'is_quantity_based': attr.is_quantity_based,
                    'quantity_unit': attr.quantity_unit,
                    'min_quantity': attr.min_quantity,
                    'max_quantity': attr.max_quantity,
                    'is_custom_input': attr.is_custom_input,
                    'input_placeholder': attr.input_placeholder,
                    'default_value': attr.default_value,
                    'validation_pattern': attr.validation_pattern,
                    'validation_message': attr.validation_message
                }
                
                # 添加扩展字段（如果存在）
                if hasattr(attr, 'validation_rules') and attr.validation_rules:
                    attr_data['validation_rules'] = attr.validation_rules
                
                if hasattr(attr, 'price_calculation_rules') and attr.price_calculation_rules:
                    attr_data['price_calculation_rules'] = attr.price_calculation_rules
                
                if hasattr(attr, 'number_min') and attr.number_min is not None:
                    attr_data['number_min'] = float(attr.number_min)
                
                if hasattr(attr, 'number_max') and attr.number_max is not None:
                    attr_data['number_max'] = float(attr.number_max)
                
                if hasattr(attr, 'number_step') and attr.number_step is not None:
                    attr_data['number_step'] = float(attr.number_step)
                
                if hasattr(attr, 'is_multiple'):
                    attr_data['is_multiple'] = attr.is_multiple
                
                if hasattr(attr, 'max_selections'):
                    attr_data['max_selections'] = attr.max_selections
                
                group_data['attributes'].append(attr_data)
            
            if group_data['attributes']:  # 只返回有属性的组
                result.append(group_data)
        
        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取商品属性失败: {str(e)}'
        }), 500

@attribute_api.route('/calculate-price', methods=['POST'])
def calculate_price():
    """计算商品价格"""
    try:
        data = request.get_json()
        product_id = data.get('product_id')
        quantity = data.get('quantity', 1)
        selected_attributes = data.get('selected_attributes', [])  # 标准属性ID列表
        custom_attributes = data.get('custom_attributes', {})  # 自定义属性值 {attr_id: value}
        attribute_quantities = data.get('attribute_quantities', {})  # 属性数量 {attr_id: quantity}
        
        product = Product.query.get_or_404(product_id)
        base_price = float(product.base_price)
        
        # 计算结果
        calculation_result = {
            'base_price': base_price,
            'quantity': quantity,
            'attribute_details': [],
            'total_attribute_price': 0,
            'unit_price': base_price,
            'total_price': 0
        }
        
        total_attribute_modifier = 0
        
        # 处理标准属性
        for attr_id in selected_attributes:
            attr = Attribute.query.get(attr_id)
            if not attr:
                continue
                
            attr_price = 0
            attr_detail = {
                'id': attr_id,
                'name': attr.name,
                'value': attr.value,
                'type': 'standard',
                'calculation_type': attr.get_calculation_type()
            }
            
            if attr.is_quantity_based and attr.price_formula:
                # 公式计算
                custom_quantity = attribute_quantities.get(str(attr_id), 1)
                attr_price = attr.calculate_formula_price(custom_quantity, base_price)
                attr_detail.update({
                    'formula': attr.price_formula,
                    'custom_quantity': custom_quantity,
                    'unit': attr.quantity_unit,
                    'calculated_price': attr_price
                })
            elif attr.price_modifier_type == 'percentage':
                # 百分比调整
                if base_price > 0:
                    attr_price = base_price * (float(attr.price_modifier) / 100)
                attr_detail.update({
                    'modifier': float(attr.price_modifier),
                    'calculated_price': attr_price
                })
            else:
                # 固定金额调整
                attr_price = float(attr.price_modifier) if attr.price_modifier else 0
                attr_detail.update({
                    'modifier': float(attr.price_modifier) if attr.price_modifier else 0,
                    'calculated_price': attr_price
                })
            
            total_attribute_modifier += attr_price
            calculation_result['attribute_details'].append(attr_detail)
        
        # 处理自定义属性
        for attr_id_str, attr_value in custom_attributes.items():
            attr_id = int(attr_id_str)
            if attr_id in selected_attributes:
                continue  # 已在标准属性中处理
                
            attr = Attribute.query.get(attr_id)
            if not attr or not attr.is_custom_input:
                continue
            
            attr_price = 0
            attr_detail = {
                'id': attr_id,
                'name': attr.name,
                'value': attr_value,
                'type': 'custom',
                'calculation_type': attr.get_calculation_type()
            }
            
            if hasattr(attr, 'calculate_advanced_price'):
                # 高级价格计算
                attr_quantity = attribute_quantities.get(str(attr_id), 1)
                attr_price = attr.calculate_advanced_price(attr_value, attr_quantity, base_price)
                attr_detail['calculated_price'] = attr_price
            elif attr.price_formula:
                # 简单公式计算
                try:
                    # 安全的公式计算环境
                    safe_dict = {
                        'value': attr_value,
                        'length': len(str(attr_value)) if attr_value else 0,
                        'quantity': attribute_quantities.get(str(attr_id), 1),
                        'base_price': base_price,
                        'abs': abs,
                        'max': max,
                        'min': min,
                        'round': round,
                        '__builtins__': {}
                    }
                    attr_price = eval(attr.price_formula, safe_dict)
                    attr_detail.update({
                        'formula': attr.price_formula,
                        'calculated_price': attr_price
                    })
                except:
                    attr_price = 0
            
            total_attribute_modifier += attr_price
            calculation_result['attribute_details'].append(attr_detail)
        
        # 计算最终价格
        calculation_result['total_attribute_price'] = total_attribute_modifier
        calculation_result['unit_price'] = base_price + total_attribute_modifier
        calculation_result['total_price'] = calculation_result['unit_price'] * quantity
        
        # 应用数量折扣
        discount = product.get_quantity_discount(quantity)
        if discount:
            discount_amount = 0
            if discount.discount_type == 'percentage':
                discount_amount = calculation_result['total_price'] * (float(discount.discount_value) / 100)
            else:
                discount_amount = float(discount.discount_value)
            
            calculation_result['discount'] = {
                'type': discount.discount_type,
                'value': float(discount.discount_value),
                'amount': discount_amount
            }
            calculation_result['total_price'] -= discount_amount
        
        # 确保价格不为负数
        calculation_result['total_price'] = max(0, calculation_result['total_price'])
        calculation_result['unit_price'] = max(0, calculation_result['unit_price'])
        
        return jsonify({
            'success': True,
            'data': calculation_result
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'价格计算失败: {str(e)}'
        }), 500

@attribute_api.route('/validate', methods=['POST'])
def validate_attributes():
    """验证属性值"""
    try:
        data = request.get_json()
        attribute_values = data.get('attribute_values', {})  # {attr_id: value}
        
        validation_results = {}
        
        for attr_id_str, value in attribute_values.items():
            attr_id = int(attr_id_str)
            attr = Attribute.query.get(attr_id)
            
            if not attr:
                validation_results[attr_id_str] = {
                    'valid': False,
                    'message': '属性不存在'
                }
                continue
            
            # 使用属性的验证方法
            if hasattr(attr, 'validate_input_value'):
                is_valid, message = attr.validate_input_value(value)
                validation_results[attr_id_str] = {
                    'valid': is_valid,
                    'message': message
                }
            else:
                # 基本验证
                validation_results[attr_id_str] = {
                    'valid': True,
                    'message': ''
                }
        
        return jsonify({
            'success': True,
            'data': validation_results
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'验证失败: {str(e)}'
        }), 500
