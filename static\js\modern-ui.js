/**
 * Modern UI - Advanced User Interactions
 * 现代化印刷服务系统 - 高级交互组件
 * @version 1.0.0
 */

// Module pattern for better organization and encapsulation
const ModernUI = (function() {
    // Private variables
    const defaults = {
        notificationDuration: 5000,
        scrollOffset: 100,
        animationDuration: 300,
        debounceTime: 200
    };
    
    // Utility functions
    const utils = {
        // Debounce function to limit execution rate
        debounce: function(func, wait = defaults.debounceTime) {
            let timeout;
            return function(...args) {
                const context = this;
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(context, args), wait);
            };
        },
        
        // Smooth scroll to element
        scrollTo: function(element, duration = 500) {
            if (!element) return;
            
            const targetPosition = element.getBoundingClientRect().top + window.pageYOffset;
            const startPosition = window.pageYOffset;
            const distance = targetPosition - startPosition;
            let startTime = null;
            
            function animation(currentTime) {
                if (startTime === null) startTime = currentTime;
                const timeElapsed = currentTime - startTime;
                const progress = Math.min(timeElapsed / duration, 1);
                const easeInOutCubic = progress < 0.5
                    ? 4 * progress * progress * progress
                    : 1 - Math.pow(-2 * progress + 2, 3) / 2;
                
                window.scrollTo(0, startPosition + distance * easeInOutCubic);
                
                if (timeElapsed < duration) {
                    requestAnimationFrame(animation);
                }
            }
            
            requestAnimationFrame(animation);
        },
        
        // Generate a unique ID
        generateId: function() {
            return '_' + Math.random().toString(36).substr(2, 9);
        },
        
        // Check if element is in viewport
        isInViewport: function(element) {
            const rect = element.getBoundingClientRect();
            return (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
            );
        }
    };
    
    // Notification system
    const notification = {
        container: null,
        
        init: function() {
            // Create notification container if not exists
            if (!this.container) {
                this.container = document.createElement('div');
                this.container.className = 'notification-container';
                Object.assign(this.container.style, {
                    position: 'fixed',
                    top: '1rem',
                    right: '1rem',
                    zIndex: '9999',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '0.5rem',
                    maxWidth: '100%',
                    width: '400px'
                });
                document.body.appendChild(this.container);
            }
        },
        
        show: function(message, type = 'info', duration = defaults.notificationDuration) {
            this.init();
            
            // Create notification element
            const id = utils.generateId();
            const notification = document.createElement('div');
            notification.id = `notification-${id}`;
            notification.className = `notification-modern ${type}`;
            
            // Icon mapping
            const icons = {
                success: '<i class="fas fa-check-circle notification-icon success"></i>',
                warning: '<i class="fas fa-exclamation-triangle notification-icon warning"></i>',
                error: '<i class="fas fa-exclamation-circle notification-icon error"></i>',
                info: '<i class="fas fa-info-circle notification-icon info"></i>'
            };
            
            // Set content
            notification.innerHTML = `
                <div class="notification-content">
                    ${icons[type] || icons.info}
                    <div class="notification-body">
                        <h4>${type.charAt(0).toUpperCase() + type.slice(1)}</h4>
                        <p>${message}</p>
                    </div>
                </div>
                <button class="notification-close" aria-label="关闭通知">
                    <i class="fas fa-times"></i>
                </button>
            `;
            
            // Prepare for animation
            notification.style.transform = 'translateX(100%)';
            notification.style.opacity = '0';
            
            // Add to container
            this.container.appendChild(notification);
            
            // Trigger animation
            setTimeout(() => {
                notification.style.transition = 'transform 0.3s ease, opacity 0.3s ease';
                notification.style.transform = 'translateX(0)';
                notification.style.opacity = '1';
            }, 10);
            
            // Add close button event
            const closeButton = notification.querySelector('.notification-close');
            closeButton.addEventListener('click', () => this.close(id));
            
            // Auto close after duration
            if (duration > 0) {
                setTimeout(() => this.close(id), duration);
            }
            
            return id;
        },
        
        close: function(id) {
            const notification = document.getElementById(`notification-${id}`);
            if (!notification) return;
            
            // Animate out
            notification.style.transform = 'translateX(100%)';
            notification.style.opacity = '0';
            
            // Remove after animation
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        },
        
        // Shorthand methods
        success: function(message, duration) {
            return this.show(message, 'success', duration);
        },
        
        error: function(message, duration) {
            return this.show(message, 'error', duration);
        },
        
        warning: function(message, duration) {
            return this.show(message, 'warning', duration);
        },
        
        info: function(message, duration) {
            return this.show(message, 'info', duration);
        }
    };
    
    // Modal system
    const modal = {
        instances: {},
        
        create: function(options = {}) {
            const id = options.id || utils.generateId();
            
            // Default options
            const settings = {
                title: options.title || '对话框',
                content: options.content || '',
                closable: options.closable !== undefined ? options.closable : true,
                onConfirm: options.onConfirm || null,
                onCancel: options.onCancel || null,
                confirmText: options.confirmText || '确认',
                cancelText: options.cancelText || '取消',
                showFooter: options.showFooter !== undefined ? options.showFooter : true,
                size: options.size || 'md', // 'sm', 'md', 'lg', 'xl'
                removeOnClose: options.removeOnClose !== undefined ? options.removeOnClose : true
            };
            
            // Create modal element
            const modalElement = document.createElement('div');
            modalElement.id = `modal-${id}`;
            modalElement.className = `modal-modern`;
            
            // Size class mapping
            const sizeClass = {
                sm: 'max-w-md',
                md: 'max-w-lg',
                lg: 'max-w-2xl',
                xl: 'max-w-4xl'
            };
            
            // Construct modal HTML
            modalElement.innerHTML = `
                <div class="modal-content-modern ${sizeClass[settings.size] || ''}">
                    <div class="modal-header-modern">
                        <h2 class="modal-title-modern">${settings.title}</h2>
                        ${settings.closable ? '<button class="modal-close" aria-label="关闭"><i class="fas fa-times"></i></button>' : ''}
                    </div>
                    <div class="modal-body-modern">
                        ${settings.content}
                    </div>
                    ${settings.showFooter ? `
                        <div class="modal-footer-modern">
                            ${settings.onCancel ? `<button class="btn btn-outline-secondary modal-cancel">${settings.cancelText}</button>` : ''}
                            ${settings.onConfirm ? `<button class="btn btn-primary modal-confirm">${settings.confirmText}</button>` : ''}
                        </div>
                    ` : ''}
                </div>
            `;
            
            // Add to DOM
            document.body.appendChild(modalElement);
            
            // Store instance
            this.instances[id] = {
                element: modalElement,
                settings: settings
            };
            
            // Add event listeners
            const instance = this.instances[id];
            
            if (settings.closable) {
                const closeButton = instance.element.querySelector('.modal-close');
                closeButton.addEventListener('click', () => this.close(id));
                
                // Close when clicking outside the modal content
                instance.element.addEventListener('click', (e) => {
                    if (e.target === instance.element) {
                        this.close(id);
                    }
                });
                
                // Close on ESC key
                const escHandler = function(e) {
                    if (e.key === 'Escape') {
                        modal.close(id);
                    }
                };
                
                document.addEventListener('keydown', escHandler);
                
                // Store the handler to remove it later
                instance.escHandler = escHandler;
            }
            
            // Add confirm/cancel event listeners
            if (settings.onConfirm) {
                const confirmButton = instance.element.querySelector('.modal-confirm');
                confirmButton.addEventListener('click', () => {
                    settings.onConfirm();
                    if (settings.removeOnClose) {
                        this.close(id);
                    }
                });
            }
            
            if (settings.onCancel) {
                const cancelButton = instance.element.querySelector('.modal-cancel');
                cancelButton.addEventListener('click', () => {
                    settings.onCancel();
                    if (settings.removeOnClose) {
                        this.close(id);
                    }
                });
            }
            
            return id;
        },
        
        open: function(id) {
            const instance = this.instances[id];
            if (!instance) return;
            
            // Lock body scroll
            document.body.style.overflow = 'hidden';
            
            // Show modal
            instance.element.style.display = 'flex';
            
            // Trigger animation
            setTimeout(() => {
                instance.element.classList.add('show');
            }, 10);
            
            return id;
        },
        
        close: function(id) {
            const instance = this.instances[id];
            if (!instance) return;
            
            // Animate out
            instance.element.classList.remove('show');
            
            // Remove after animation
            setTimeout(() => {
                instance.element.style.display = 'none';
                
                // Restore body scroll
                document.body.style.overflow = '';
                
                // Remove from DOM if needed
                if (instance.settings.removeOnClose) {
                    if (instance.element.parentNode) {
                        instance.element.parentNode.removeChild(instance.element);
                    }
                    
                    // Remove event listener
                    if (instance.escHandler) {
                        document.removeEventListener('keydown', instance.escHandler);
                    }
                    
                    // Remove from instances
                    delete this.instances[id];
                }
            }, defaults.animationDuration);
        },
        
        // Shorthand methods for common modals
        alert: function(message, title = '提示', callback) {
            const id = this.create({
                title: title,
                content: `<p class="text-center">${message}</p>`,
                onConfirm: callback || function() {},
                onCancel: null,
                confirmText: '确定',
                showFooter: true
            });
            
            this.open(id);
            return id;
        },
        
        confirm: function(message, title = '确认', onConfirm, onCancel) {
            const id = this.create({
                title: title,
                content: `<p class="text-center">${message}</p>`,
                onConfirm: onConfirm || function() {},
                onCancel: onCancel || function() {},
                confirmText: '确认',
                cancelText: '取消',
                showFooter: true
            });
            
            this.open(id);
            return id;
        },
        
        prompt: function(message, defaultValue = '', title = '输入', onConfirm, onCancel) {
            const id = this.create({
                title: title,
                content: `
                    <p class="mb-3">${message}</p>
                    <input type="text" class="form-control prompt-input" value="${defaultValue}">
                `,
                onConfirm: function() {
                    const input = document.querySelector(`#modal-${id} .prompt-input`);
                    if (onConfirm) onConfirm(input.value);
                },
                onCancel: onCancel || function() {},
                confirmText: '确认',
                cancelText: '取消',
                showFooter: true
            });
            
            this.open(id);
            
            // Focus the input
            setTimeout(() => {
                const input = document.querySelector(`#modal-${id} .prompt-input`);
                if (input) input.focus();
            }, 100);
            
            return id;
        }
    };
    
    // Lazy loading for images
    const lazyLoad = {
        observers: [],
        
        init: function(selector = '.lazy-load') {
            if ('IntersectionObserver' in window) {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const lazyElement = entry.target;
                            
                            if (lazyElement.tagName === 'IMG') {
                                const src = lazyElement.dataset.src;
                                if (src) {
                                    lazyElement.src = src;
                                    lazyElement.classList.add('loaded');
                                    observer.unobserve(lazyElement);
                                }
                            } else {
                                const bgImage = lazyElement.dataset.bgImage;
                                if (bgImage) {
                                    lazyElement.style.backgroundImage = `url(${bgImage})`;
                                    lazyElement.classList.add('loaded');
                                    observer.unobserve(lazyElement);
                                }
                            }
                        }
                    });
                }, {
                    rootMargin: '100px'
                });
                
                document.querySelectorAll(selector).forEach(element => {
                    observer.observe(element);
                });
                
                this.observers.push(observer);
            } else {
                // Fallback for browsers without Intersection Observer
                this.loadAllLazy(selector);
            }
        },
        
        loadAllLazy: function(selector = '.lazy-load') {
            document.querySelectorAll(selector).forEach(element => {
                if (element.tagName === 'IMG') {
                    const src = element.dataset.src;
                    if (src) {
                        element.src = src;
                        element.classList.add('loaded');
                    }
                } else {
                    const bgImage = element.dataset.bgImage;
                    if (bgImage) {
                        element.style.backgroundImage = `url(${bgImage})`;
                        element.classList.add('loaded');
                    }
                }
            });
        },
        
        disconnect: function() {
            this.observers.forEach(observer => {
                if (observer) {
                    observer.disconnect();
                }
            });
            this.observers = [];
        }
    };
    
    // Scroll animations
    const scrollAnimations = {
        observers: [],
        
        init: function(selector = '.animate-on-scroll') {
            if ('IntersectionObserver' in window) {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('animated');
                            observer.unobserve(entry.target);
                        }
                    });
                }, {
                    threshold: 0.1
                });
                
                document.querySelectorAll(selector).forEach(element => {
                    // Add base class
                    element.classList.add('animation-ready');
                    observer.observe(element);
                });
                
                this.observers.push(observer);
            } else {
                // Fallback for browsers without Intersection Observer
                document.querySelectorAll(selector).forEach(element => {
                    element.classList.add('animated');
                });
            }
        },
        
        disconnect: function() {
            this.observers.forEach(observer => {
                if (observer) {
                    observer.disconnect();
                }
            });
            this.observers = [];
        }
    };
    
    // Back to top button
    const backToTop = {
        button: null,
        
        init: function(options = {}) {
            const settings = {
                offset: options.offset || defaults.scrollOffset,
                smooth: options.smooth !== undefined ? options.smooth : true
            };
            
            // Create button if not exists
            if (!this.button) {
                this.button = document.querySelector('.back-to-top') || document.createElement('button');
                this.button.className = 'back-to-top';
                this.button.innerHTML = '<i class="fas fa-arrow-up"></i>';
                this.button.setAttribute('aria-label', '返回顶部');
                this.button.setAttribute('title', '返回顶部');
                
                // Apply styles
                Object.assign(this.button.style, {
                    position: 'fixed',
                    bottom: '20px',
                    right: '20px',
                    display: 'none',
                    width: '40px',
                    height: '40px',
                    borderRadius: '50%',
                    background: 'var(--primary-600)',
                    color: 'white',
                    border: 'none',
                    boxShadow: 'var(--shadow-md)',
                    cursor: 'pointer',
                    opacity: '0',
                    transition: 'opacity 0.3s ease, transform 0.3s ease',
                    zIndex: '99'
                });
                
                // If not already in the DOM, add it
                if (!document.querySelector('.back-to-top')) {
                    document.body.appendChild(this.button);
                }
                
                // Add click event
                this.button.addEventListener('click', () => {
                    if (settings.smooth) {
                        window.scrollTo({
                            top: 0,
                            behavior: 'smooth'
                        });
                    } else {
                        window.scrollTo(0, 0);
                    }
                });
            }
            
            // Scroll event to show/hide button
            const handleScroll = utils.debounce(() => {
                if (window.pageYOffset > settings.offset) {
                    this.button.style.display = 'block';
                    // Ensure opacity transition works by delaying the opacity change
                    setTimeout(() => {
                        this.button.style.opacity = '1';
                        this.button.style.transform = 'translateY(0)';
                    }, 10);
                } else {
                    this.button.style.opacity = '0';
                    this.button.style.transform = 'translateY(20px)';
                    setTimeout(() => {
                        if (parseFloat(this.button.style.opacity) === 0) {
                            this.button.style.display = 'none';
                        }
                    }, 300);
                }
            });
            
            window.addEventListener('scroll', handleScroll);
            
            // Initial check
            handleScroll();
        }
    };
    
    // Tooltip system
    const tooltip = {
        instances: {},
        
        init: function(selector = '[data-tooltip]') {
            document.querySelectorAll(selector).forEach(element => {
                const id = utils.generateId();
                const content = element.getAttribute('data-tooltip');
                const position = element.getAttribute('data-tooltip-position') || 'top';
                
                // Create tooltip element
                const tooltipElement = document.createElement('div');
                tooltipElement.id = `tooltip-${id}`;
                tooltipElement.className = `tooltip-content tooltip-${position}`;
                tooltipElement.textContent = content;
                
                // Apply styles
                Object.assign(tooltipElement.style, {
                    position: 'absolute',
                    background: 'var(--gray-900)',
                    color: 'white',
                    padding: '0.25rem 0.5rem',
                    borderRadius: '0.25rem',
                    fontSize: '0.75rem',
                    opacity: '0',
                    visibility: 'hidden',
                    transition: 'opacity 0.2s ease, visibility 0.2s ease',
                    zIndex: '9999',
                    whiteSpace: 'nowrap',
                    pointerEvents: 'none',
                    fontWeight: 'normal'
                });
                
                // Add to DOM
                document.body.appendChild(tooltipElement);
                
                // Store instance
                this.instances[id] = {
                    element: tooltipElement,
                    target: element,
                    position: position
                };
                
                // Add event listeners
                element.addEventListener('mouseenter', () => this.show(id));
                element.addEventListener('mouseleave', () => this.hide(id));
                element.addEventListener('focus', () => this.show(id));
                element.addEventListener('blur', () => this.hide(id));
            });
        },
        
        position: function(id) {
            const instance = this.instances[id];
            if (!instance) return;
            
            const targetRect = instance.target.getBoundingClientRect();
            const tooltipRect = instance.element.getBoundingClientRect();
            
            let top, left;
            
            switch (instance.position) {
                case 'top':
                    top = targetRect.top - tooltipRect.height - 5;
                    left = targetRect.left + (targetRect.width / 2) - (tooltipRect.width / 2);
                    break;
                case 'bottom':
                    top = targetRect.bottom + 5;
                    left = targetRect.left + (targetRect.width / 2) - (tooltipRect.width / 2);
                    break;
                case 'left':
                    top = targetRect.top + (targetRect.height / 2) - (tooltipRect.height / 2);
                    left = targetRect.left - tooltipRect.width - 5;
                    break;
                case 'right':
                    top = targetRect.top + (targetRect.height / 2) - (tooltipRect.height / 2);
                    left = targetRect.right + 5;
                    break;
                default:
                    top = targetRect.top - tooltipRect.height - 5;
                    left = targetRect.left + (targetRect.width / 2) - (tooltipRect.width / 2);
            }
            
            // Ensure tooltip stays within viewport
            if (left < 0) left = 0;
            if (left + tooltipRect.width > window.innerWidth) {
                left = window.innerWidth - tooltipRect.width;
            }
            
            if (top < 0) {
                // Flip to bottom if tooltip would go off the top of the screen
                top = targetRect.bottom + 5;
            }
            
            instance.element.style.top = `${top + window.scrollY}px`;
            instance.element.style.left = `${left}px`;
        },
        
        show: function(id) {
            const instance = this.instances[id];
            if (!instance) return;
            
            // Position tooltip
            this.position(id);
            
            // Show tooltip
            instance.element.style.opacity = '1';
            instance.element.style.visibility = 'visible';
        },
        
        hide: function(id) {
            const instance = this.instances[id];
            if (!instance) return;
            
            // Hide tooltip
            instance.element.style.opacity = '0';
            instance.element.style.visibility = 'hidden';
        },
        
        destroy: function() {
            Object.keys(this.instances).forEach(id => {
                const instance = this.instances[id];
                
                // Remove element from DOM
                if (instance.element.parentNode) {
                    instance.element.parentNode.removeChild(instance.element);
                }
                
                // Remove event listeners
                instance.target.removeEventListener('mouseenter', () => this.show(id));
                instance.target.removeEventListener('mouseleave', () => this.hide(id));
                instance.target.removeEventListener('focus', () => this.show(id));
                instance.target.removeEventListener('blur', () => this.hide(id));
            });
            
            this.instances = {};
        }
    };
    
    // Theme toggle
    const themeToggle = {
        init: function() {
            const toggleButton = document.querySelector('#theme-icon')?.parentElement;
            if (!toggleButton) return;
            
            // Load saved theme
            const savedTheme = localStorage.getItem('theme') || 'light';
            this.setTheme(savedTheme);
            
            // Update icon
            this.updateIcon(savedTheme);
        },
        
        setTheme: function(theme) {
            document.documentElement.setAttribute('data-bs-theme', theme);
            localStorage.setItem('theme', theme);
            this.updateIcon(theme);
        },
        
        updateIcon: function(theme) {
            const icon = document.getElementById('theme-icon');
            if (!icon) return;
            
            if (theme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }
        },
        
        toggle: function() {
            const currentTheme = document.documentElement.getAttribute('data-bs-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            this.setTheme(newTheme);
        }
    };
    
    // Initialize components
    function init() {
        // Init theme toggle
        themeToggle.init();
        
        // Init lazy loading
        lazyLoad.init();
        
        // Init scroll animations
        scrollAnimations.init();
        
        // Init back to top button
        backToTop.init();
        
        // Init tooltips
        tooltip.init();
        
        // Enable smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]:not([href="#"])').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    e.preventDefault();
                    utils.scrollTo(target);
                }
            });
        });
    }
    
    // Public API
    return {
        init: init,
        notification: notification,
        modal: modal,
        tooltip: tooltip,
        lazyLoad: lazyLoad,
        scrollAnimations: scrollAnimations,
        backToTop: backToTop,
        themeToggle: themeToggle,
        utils: utils
    };
})();

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    ModernUI.init();
    
    // Make ModernUI globally available
    window.ModernUI = ModernUI;
}); 