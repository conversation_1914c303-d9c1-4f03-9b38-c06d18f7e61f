{% extends "base_modern.html" %}

{% block title %}{{ product.name }} - {{ site_config.site_name }}{% endblock %}
{% block description %}{{ product.description or product.name }}，专业印刷产品定制服务{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/region-selector.css') }}">
<style>
    /* ========== Product Detail Styles - Optimized for 1920x1080 ========== */
    .product-detail-section {
        padding: 2rem 0 6rem;
        background: var(--gradient-light);
        min-height: 100vh;
    }
    
    .breadcrumb-modern {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-radius: var(--border-radius-xl);
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: var(--shadow-sm);
    }
    
    .breadcrumb-modern .breadcrumb {
        margin: 0;
        background: none;
        padding: 0;
    }
    
    .breadcrumb-modern .breadcrumb-item a {
        color: var(--primary-600);
        text-decoration: none;
        transition: var(--transition-base);
    }
    
    .breadcrumb-modern .breadcrumb-item a:hover {
        color: var(--primary-700);
    }
    
    /* ========== 左侧分类面板 ========== */
    .product-layout-container {
        display: flex;
        gap: 2rem;
        max-width: 1400px;
        margin: 0 auto;
    }
    
    .category-sidebar {
        width: 280px;
        flex-shrink: 0;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-radius: var(--border-radius-2xl);
        padding: 2rem;
        box-shadow: var(--shadow-lg);
        border: 1px solid rgba(255, 255, 255, 0.3);
        height: fit-content;
        position: sticky;
        top: 2rem;
    }
    
    .sidebar-title {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--gray-900);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .category-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .category-item {
        margin-bottom: 0.5rem;
    }
    
    .category-link {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        color: var(--gray-700);
        text-decoration: none;
        border-radius: var(--border-radius-lg);
        transition: var(--transition-base);
        font-weight: 500;
    }
    
    .category-link:hover {
        background: var(--primary-gradient);
        color: white;
        transform: translateX(5px);
    }
    
    .category-link.active {
        background: var(--primary-500);
        color: white;
    }
    
    .category-icon {
        margin-right: 0.75rem;
        width: 16px;
    }
    
    .search-box {
        margin-bottom: 2rem;
        position: relative;
    }
    
    .search-input {
        width: 100%;
        padding: 0.75rem 1rem 0.75rem 2.5rem;
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: var(--border-radius-lg);
        font-size: 0.9rem;
        background: rgba(255, 255, 255, 0.9);
        transition: var(--transition-base);
    }
    
    .search-input:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .search-icon {
        position: absolute;
        left: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--gray-400);
    }
    
    /* ========== 主要产品区域 ========== */
    .product-main-container {
        flex: 1;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-radius: var(--border-radius-2xl);
        padding: 2rem;
        box-shadow: var(--shadow-xl);
        border: 1px solid rgba(255, 255, 255, 0.3);
        position: relative;
        overflow: hidden;
        max-width: 1000px; /* 限制最大宽度 */
    }
    
    .product-main-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: var(--primary-gradient);
    }
    
    .product-content-grid {
        display: grid;
        grid-template-columns: 350px 1fr; /* 固定图片区域宽度 */
        gap: 2rem;
        align-items: start;
    }
    
    /* ========== Image Gallery - 缩小尺寸 ========== */
    .image-gallery-container {
        position: sticky;
        top: 2rem;
    }
    
    .main-image-wrapper {
        position: relative;
        border-radius: var(--border-radius-xl);
        overflow: hidden;
        background: var(--gradient-light);
        margin-bottom: 1rem;
        box-shadow: var(--shadow-lg);
        aspect-ratio: 1;
        max-width: 350px; /* 限制图片最大宽度 */
    }
    
    .main-product-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: var(--transition-slow);
        cursor: zoom-in;
    }
    
    .main-product-image:hover {
        transform: scale(1.03); /* 减小缩放效果 */
    }
    
    .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.3);
        opacity: 0;
        transition: var(--transition-base);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem; /* 减小图标尺寸 */
    }
    
    .main-image-wrapper:hover .image-overlay {
        opacity: 1;
    }
    
    .image-thumbnails {
        display: flex;
        gap: 0.75rem;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .thumbnail-item {
        width: 60px; /* 减小缩略图尺寸 */
        height: 60px;
        border-radius: var(--border-radius-lg);
        overflow: hidden;
        cursor: pointer;
        transition: var(--transition-base);
        border: 2px solid transparent;
        background: var(--gradient-light);
    }
    
    .thumbnail-item.active {
        border-color: var(--primary-500);
        box-shadow: var(--shadow-primary);
    }
    
    .thumbnail-item:hover {
        transform: translateY(-3px);
        box-shadow: var(--shadow-md);
    }
    
    .thumbnail-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    /* ========== Product Info - 优化布局 ========== */
    .product-info {
        /* 移除左侧padding */
    }
    
    .product-title {
        font-size: 2rem; /* 减小标题尺寸 */
        font-weight: 700;
        color: var(--gray-900);
        margin-bottom: 0.75rem;
        line-height: 1.2;
    }
    
    .product-subtitle {
        font-size: 1rem; /* 减小副标题尺寸 */
        color: var(--gray-600);
        margin-bottom: 1.5rem;
        line-height: 1.5;
    }
    
    .product-category-badge {
        display: inline-flex;
        align-items: center;
        background: var(--primary-gradient);
        color: white;
        padding: 0.4rem 0.8rem; /* 减小内边距 */
        border-radius: var(--border-radius-xl);
        font-size: 0.8rem; /* 减小字体 */
        font-weight: 600;
        margin-bottom: 1rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    /* ========== Price Section - 紧凑化 ========== */
    .price-section {
        background: var(--gradient-light);
        border-radius: var(--border-radius-xl);
        padding: 1.5rem; /* 减小内边距 */
        margin-bottom: 1.5rem;
        border: 1px solid rgba(102, 126, 234, 0.1);
        position: relative;
        overflow: hidden;
    }
    
    .price-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--primary-gradient);
        opacity: 0.03;
    }
    
    .price-main {
        display: flex;
        align-items: baseline;
        gap: 0.4rem;
        margin-bottom: 0.4rem;
        position: relative;
        z-index: 2;
    }
    
    .price-symbol {
        font-size: 1.2rem; /* 减小尺寸 */
        color: var(--primary-600);
        font-weight: 600;
    }
    
    .price-value {
        font-size: 2.2rem; /* 减小尺寸 */
        font-weight: 800;
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        line-height: 1;
    }
    
    .price-unit {
        font-size: 0.9rem;
        color: var(--gray-500);
        font-weight: 500;
    }
    
    .price-info {
        color: var(--gray-600);
        position: relative;
        z-index: 2;
        font-size: 0.9rem; /* 减小字体 */
    }
    
    .discount-info {
        margin-top: 1rem;
        padding: 0.75rem; /* 减小内边距 */
        background: rgba(16, 185, 129, 0.1);
        border-radius: var(--border-radius-lg);
        border-left: 4px solid var(--success-500);
        display: none;
    }
    
    .discount-info.show {
        display: block;
        animation: slideInUp 0.3s ease-out;
    }
    
    /* ========== Specifications - 紧凑化 ========== */
    .spec-section {
        margin-bottom: 1.25rem; /* 减小间距 */
    }
    
    .spec-title {
        font-size: 1rem; /* 减小标题尺寸 */
        font-weight: 600;
        color: var(--gray-800);
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .spec-group {
        margin-bottom: 0.75rem; /* 减小间距 */
    }
    
    .spec-group-title {
        font-size: 0.9rem;
        font-weight: 600;
        color: var(--gray-800);
        margin-bottom: 0;
        margin-right: 0.75rem;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        white-space: nowrap;
        flex-shrink: 0;
        min-width: 40px;
    }
    
    .spec-group-title::before {
        content: '';
        width: 3px;
        height: 1rem;
        background: var(--primary-gradient);
        border-radius: 2px;
    }
    
    .spec-group-title::after {
        display: none;
    }
    
    .spec-inline-layout {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }
    
    .spec-options {
        display: flex;
        flex-wrap: wrap;
        gap: 0.4rem; /* 减小间距 */
        margin-bottom: 0.5rem;
    }
    
    /* ========== 下拉框样式 ========== */
    .spec-select-container {
        margin-bottom: 0.5rem;
    }
    
    .spec-select {
        width: 100%;
        max-width: 300px;
        padding: 0.5rem 0.75rem;
        border: 2px solid var(--gray-300);
        border-radius: var(--border-radius-lg);
        background: white;
        color: #333333 !important;
        font-size: 0.9rem;
        font-weight: 500;
        transition: var(--transition-base);
        cursor: pointer;
    }
    
    .spec-select:focus {
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
    }
    
    .spec-select:hover {
        border-color: var(--primary-400);
    }
    
    .spec-select option {
        padding: 0.5rem;
        font-weight: 500;
        color: #333333 !important;
    }
    
    /* 选择后的样式 */
    .spec-select.selected {
        border-color: var(--primary-500);
        background: rgba(102, 126, 234, 0.05);
    }

    /* ========== 自定义输入框样式 ========== */
    .custom-input-wrapper {
        animation: slideDown 0.3s ease-out;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .custom-input-field {
        border: 2px solid var(--gray-300);
        border-radius: var(--border-radius-lg);
        padding: 0.75rem 1rem;
        font-size: 0.95rem;
        transition: var(--transition-base);
        background: white;
    }

    .custom-input-field:focus {
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
    }

    .custom-input-field.is-valid {
        border-color: var(--success-500);
        background: rgba(16, 185, 129, 0.05);
    }

    .custom-input-field.is-invalid {
        border-color: var(--danger-500);
        background: rgba(239, 68, 68, 0.05);
    }

    .custom-input-wrapper .btn {
        border-color: var(--gray-300);
    }

    .custom-input-wrapper .btn:hover {
        border-color: var(--danger-400);
        color: var(--danger-600);
    }
    
    .spec-option {
        position: relative;
        cursor: pointer;
    }
    
    .spec-option input[type="checkbox"] {
        position: absolute;
        opacity: 0;
        width: 0;
        height: 0;
    }
    
    .spec-option-label {
        display: inline-block;
        padding: 0.4rem 0.8rem; /* 减小内边距 */
        border: 2px solid var(--gray-300);
        border-radius: var(--border-radius-md);
        background: white;
        color: var(--gray-700);
        font-size: 0.85rem; /* 减小字体 */
        font-weight: 500;
        transition: var(--transition-base);
        cursor: pointer;
        user-select: none;
        min-width: 50px; /* 减小最小宽度 */
        text-align: center;
    }
    
    .spec-option-label:hover {
        border-color: var(--primary-400);
        background: rgba(102, 126, 234, 0.05);
        transform: translateY(-1px);
    }
    
    .spec-option input[type="checkbox"]:checked + .spec-option-label {
        border-color: var(--primary-500);
        background: var(--primary-500);
        color: white;
        box-shadow: var(--shadow-primary);
    }

    /* ========== 自定义输入框样式 ========== */
    .custom-input-container {
        margin-bottom: 1rem;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.8);
        border-radius: var(--border-radius-lg);
        border: 1px solid var(--gray-200);
        transition: var(--transition-base);
    }

    .custom-input-container:hover {
        border-color: var(--primary-300);
        background: rgba(255, 255, 255, 0.95);
    }

    .custom-attribute-input {
        border: 2px solid var(--gray-300);
        border-radius: var(--border-radius-md);
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
        transition: var(--transition-base);
        background: white;
    }

    .custom-attribute-input:focus {
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
    }

    .custom-attribute-input.is-valid {
        border-color: var(--success-500);
        background: rgba(16, 185, 129, 0.05);
    }

    .custom-attribute-input.is-invalid {
        border-color: var(--danger-500);
        background: rgba(239, 68, 68, 0.05);
    }

    .custom-attribute-input.selected {
        border-color: var(--primary-500);
        background: rgba(102, 126, 234, 0.05);
    }

    .invalid-feedback {
        display: block;
        width: 100%;
        margin-top: 0.25rem;
        font-size: 0.8rem;
        color: var(--danger-600);
    }
        color: white;
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        transform: translateY(-1px);
    }
    
    .spec-option input[type="checkbox"]:checked + .spec-option-label::after {
        content: '✓';
        position: absolute;
        top: -4px;
        right: -4px;
        width: 16px; /* 减小尺寸 */
        height: 16px;
        background: var(--success-500);
        color: white;
        border-radius: 50%;
        font-size: 0.65rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
    }
    
    .spec-text {
        position: relative;
        z-index: 2;
        transition: var(--transition-base);
        font-weight: 600;
    }
    
    .spec-price {
        font-size: 0.7rem;
        opacity: 0.8;
    }
    
    /* ========== Quantity Section - 紧凑化 ========== */
    .quantity-section {
        margin-bottom: 1.25rem;
    }
    
    .quantity-label {
        font-size: 0.9rem;
        font-weight: 600;
        color: var(--gray-800);
        margin-bottom: 0;
        margin-right: 0.75rem;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        white-space: nowrap;
        flex-shrink: 0;
        min-width: 40px;
    }
    
    .quantity-label::before {
        content: '';
        width: 3px;
        height: 1rem;
        background: var(--primary-gradient);
        border-radius: 2px;
    }
    
    .quantity-controls {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex-wrap: wrap;
    }
    
    .quantity-selector {
        display: flex;
        align-items: center;
        background: rgba(255, 255, 255, 0.8);
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: var(--border-radius-lg);
        overflow: hidden;
        box-shadow: var(--shadow-sm);
    }
    
    .quantity-btn {
        background: var(--gradient-light);
        border: none;
        color: var(--gray-600);
        width: 2.2rem; /* 减小尺寸 */
        height: 2.2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: var(--transition-base);
        font-size: 0.9rem;
    }
    
    .quantity-btn:hover:not(:disabled) {
        background: var(--primary-gradient);
        color: white;
    }
    
    .quantity-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
    
    .quantity-input {
        width: 50px;
        min-width: 40px;
        height: 2.2rem;
        border: none;
        text-align: center;
        font-size: 0.9rem;
        font-weight: 600;
        background: white;
        color: var(--gray-800);
    }
    
    .quantity-input:focus {
        outline: none;
    }
    
    .quantity-limit {
        color: var(--gray-500);
        font-size: 0.75rem; /* 减小字体 */
    }
    
    /* ========== Action Buttons ========== */
    .action-section {
        margin-bottom: 1.5rem;
    }
    
    /* ========== Address Form - 紧凑化 ========== */
    .address-section {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-radius: var(--border-radius-xl);
        padding: 1.5rem; /* 减小内边距 */
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: var(--shadow-sm);
        margin-bottom: 1.5rem;
    }
    
    .address-section .spec-title {
        margin-bottom: 1rem;
    }
    
    .form-label {
        font-weight: 600;
        color: var(--gray-800);
        margin-bottom: 0.4rem;
        font-size: 0.85rem; /* 减小字体 */
    }
    
    .form-control {
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: var(--border-radius-lg);
        padding: 0.6rem 0.8rem; /* 减小内边距 */
        font-size: 0.9rem; /* 减小字体 */
        transition: var(--transition-base);
        background: rgba(255, 255, 255, 0.9);
        color: #333333 !important;
        font-weight: 500;
    }
    
    .form-control:focus {
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
        background: white;
    }
    
    .form-control:invalid {
        border-color: var(--danger-400);
    }
    
    .form-control::placeholder {
        color: var(--gray-400);
    }
    
    .text-danger {
        color: var(--danger-500) !important;
    }
    
    .action-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .btn-add-cart {
        background: var(--primary-gradient);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: var(--border-radius-xl);
        font-size: 1.125rem;
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition-base);
        display: flex;
        align-items: center;
        gap: 0.75rem;
        box-shadow: var(--shadow-md);
        position: relative;
        overflow: hidden;
        flex: 1;
        justify-content: center;
        min-width: 200px;
    }
    
    .btn-add-cart::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        transition: var(--transition-base);
    }
    
    .btn-add-cart:hover {
        transform: translateY(-3px);
        box-shadow: var(--shadow-xl);
    }
    
    .btn-add-cart:hover::before {
        left: 100%;
    }
    
    .btn-buy-now {
        background: var(--success-gradient);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: var(--border-radius-xl);
        font-size: 1.125rem;
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition-base);
        display: flex;
        align-items: center;
        gap: 0.75rem;
        box-shadow: var(--shadow-md);
        flex: 1;
        justify-content: center;
        min-width: 200px;
    }
    
    .btn-buy-now:hover {
        transform: translateY(-3px);
        box-shadow: var(--shadow-xl);
    }
    
    .btn-login {
        background: var(--gradient-light);
        color: var(--primary-600);
        border: 2px solid var(--primary-200);
        padding: 1rem 2rem;
        border-radius: var(--border-radius-xl);
        font-size: 1.125rem;
        font-weight: 600;
        text-decoration: none;
        transition: var(--transition-base);
        display: flex;
        align-items: center;
        gap: 0.75rem;
        justify-content: center;
        width: 100%;
    }
    
    .btn-login:hover {
        background: var(--primary-gradient);
        color: white;
        border-color: var(--primary-500);
        transform: translateY(-3px);
        box-shadow: var(--shadow-lg);
    }
    
    /* ========== Discount Section ========== */
    .discount-section {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-radius: var(--border-radius-xl);
        padding: 1.5rem;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: var(--shadow-sm);
        margin-bottom: 2rem;
    }
    
    .discount-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--gray-800);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .discount-list {
        margin-bottom: 1rem;
    }
    
    .discount-list:last-child {
        margin-bottom: 0;
    }
    
    .discount-type {
        font-weight: 600;
        color: var(--primary-600);
        margin-bottom: 0.75rem;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .discount-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 1rem;
        background: rgba(102, 126, 234, 0.05);
        border-radius: var(--border-radius-lg);
        margin-bottom: 0.5rem;
        border-left: 4px solid var(--primary-500);
    }
    
    .discount-range {
        font-weight: 500;
        color: var(--gray-700);
    }
    
    .discount-value {
        font-weight: 600;
        color: var(--success-600);
    }
    
    /* ========== Floating Actions ========== */
    .floating-actions {
        position: fixed;
        right: 2rem;
        top: 50%;
        transform: translateY(-50%);
        z-index: 100;
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    
    .floating-btn {
        width: 3.5rem;
        height: 3.5rem;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: var(--gray-600);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: var(--transition-base);
        box-shadow: var(--shadow-md);
    }
    
    .floating-btn:hover {
        background: var(--primary-gradient);
        color: white;
        transform: scale(1.1);
        box-shadow: var(--shadow-lg);
    }
    
    /* ========== Responsive Design ========== */
    @media (max-width: 1200px) {
        .product-layout-container {
            max-width: 1200px;
        }
        
        .product-main-container {
            max-width: 900px;
        }
        
        .product-content-grid {
            grid-template-columns: 300px 1fr;
        }
        
        .main-image-wrapper {
            max-width: 300px;
        }
    }
    
    @media (max-width: 992px) {
        .product-layout-container {
            flex-direction: column;
            gap: 1.5rem;
        }
        
        .category-sidebar {
            width: 100%;
            position: static;
            order: 2;
        }
        
        .product-main-container {
            max-width: 100%;
            order: 1;
        }
        
        .product-content-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }
        
        .image-gallery-container {
            position: static;
        }
        
        .main-image-wrapper {
            max-width: 100%;
            margin: 0 auto;
        }
        
        .floating-actions {
            display: none;
        }
        
        .action-buttons {
            flex-direction: column;
        }
        
        .btn-add-cart,
        .btn-buy-now {
            flex: none;
            width: 100%;
            min-width: auto;
        }
    }
    
    @media (max-width: 768px) {
        .product-main-container {
            padding: 1.5rem;
        }
        
        .product-title {
            font-size: 1.75rem;
        }
        
        .price-value {
            font-size: 2rem;
        }
        
        .spec-options {
            justify-content: center;
        }
        
        .quantity-controls {
            justify-content: center;
        }
        
        .category-sidebar {
            padding: 1.5rem;
        }
        
        .sidebar-title {
            font-size: 1.1rem;
        }
        
        .category-link {
            padding: 0.6rem 0.8rem;
            font-size: 0.9rem;
        }
        
        .search-input {
            font-size: 0.85rem;
            padding: 0.6rem 0.8rem 0.6rem 2.2rem;
        }
        
        .search-icon {
            left: 0.6rem;
        }
    }
    
    @media (max-width: 576px) {
        .product-detail-section {
            padding: 1rem 0 4rem;
        }
        
        .breadcrumb-modern {
            padding: 0.75rem 1rem;
            margin-bottom: 1rem;
        }
        
        .product-main-container {
            padding: 1rem;
        }
        
        .product-title {
            font-size: 1.5rem;
        }
        
        .price-value {
            font-size: 1.8rem;
        }
        
        .price-section {
            padding: 1rem;
        }
        
        .address-section {
            padding: 1rem;
        }
        
        .category-sidebar {
            padding: 1rem;
        }
        
        .spec-option-label {
            padding: 0.3rem 0.6rem;
            font-size: 0.8rem;
            min-width: 45px;
        }
        
        .quantity-btn {
            width: 2rem;
            height: 2rem;
            font-size: 0.8rem;
        }
        
        .quantity-input {
            width: 2.5rem;
            height: 2rem;
            font-size: 0.85rem;
        }
        
        .form-control {
            padding: 0.5rem 0.6rem;
            font-size: 0.85rem;
        }
        
        .form-label {
            font-size: 0.8rem;
        }
    }
    
    /* ========== Animation Enhancement ========== */
    .animate-price {
        animation: priceUpdate 0.5s ease-out;
    }
    
    @keyframes priceUpdate {
        0% { transform: scale(0.95); opacity: 0.7; }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); opacity: 1; }
    }
    
    .loading-price {
        opacity: 0.6;
        pointer-events: none;
    }
    
    .loading-price::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.5), transparent);
        animation: shimmer 1.5s infinite;
    }
    
    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    /* ========== 公式数量输入区域样式 ========== */
    .formula-quantity-section {
        width: 100%;
        margin-top: 0.5rem;
    }
    
    .formula-quantity-section .input-group-sm {
        max-width: 120px; /* 增加宽度，确保数字完全显示 */
    }
    
    .formula-quantity-section .form-control-sm {
        height: 1.5rem;
        padding: 0.1rem 0.3rem;
        font-size: 0.75rem;
        min-width: 40px; /* 确保输入框有足够的宽度 */
    }
    
    .formula-quantity-section .btn-sm {
        padding: 0.1rem 0.3rem;
        font-size: 0.75rem;
        height: 1.5rem;
        flex-shrink: 0; /* 防止按钮被压缩 */
    }
    
    .formula-quantity-section label.small {
        font-size: 0.75rem;
    }
    
    .formula-quantity-section .mt-2 {
        margin-top: 0.25rem !important;
    }
    
    .formula-quantity-section .p-2 {
        padding: 0.25rem !important;
    }

    /* ========== 区域选择器样式 ========== */
    .form-control.loading {
        background-image: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
        background-size: 200% 100%;
        animation: loading-shimmer 1.5s infinite;
        opacity: 0.7;
    }

    @keyframes loading-shimmer {
        0% { background-position: -200% 0; }
        100% { background-position: 200% 0; }
    }

    .region-selector-error {
        color: #dc3545;
        font-size: 0.8rem;
        margin-top: 0.25rem;
        padding: 0.25rem 0.5rem;
        background: rgba(220, 53, 69, 0.1);
        border-radius: 4px;
        border-left: 3px solid #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<section class="product-detail-section">
    <div class="container-fluid">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="animate-on-scroll">
            <div class="breadcrumb-modern">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('main.index') }}">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('main.products') }}">商品中心</a>
                    </li>
                    {% if product.category %}
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('main.products', category=product.category.id) }}">
                            {{ product.category.name }}
                        </a>
                    </li>
                    {% endif %}
                    <li class="breadcrumb-item active">{{ product.name }}</li>
                </ol>
            </div>
        </nav>
        
        <!-- 主要布局容器 -->
        <div class="product-layout-container">
            <!-- 左侧分类面板 -->
            <div class="category-sidebar">
                <div class="search-box">
                    <div class="position-relative">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="搜索商品...">
                    </div>
                </div>
                
                <div class="sidebar-title">
                    <i class="fas fa-list me-2"></i>商品分类
                </div>
                
                <ul class="category-list">
                    <li class="category-item">
                        <a href="{{ url_for('main.products') }}" class="category-link {% if not request.args.get('category') %}active{% endif %}">
                            <i class="fas fa-th-large category-icon"></i>
                            全部商品
                        </a>
                    </li>
                    {% for category in categories %}
                    <li class="category-item">
                        <a href="{{ url_for('main.products', category=category.id) }}" 
                           class="category-link {% if request.args.get('category')|int == category.id %}active{% endif %}">
                            <i class="fas fa-tag category-icon"></i>
                            {{ category.name }}
                        </a>
                    </li>
                    {% endfor %}
                </ul>
            </div>
            
            <!-- 主要产品区域 -->
        <div class="product-main-container animate-on-scroll delay-1">
                <div class="product-content-grid">
                <!-- Image Gallery -->
                    <div class="image-gallery-container">
                        <div class="main-image-wrapper">
                            <img src="{{ product.get_main_image() }}" 
                                 class="main-product-image"
                                 alt="{{ product.name }}" 
                                 id="mainProductImage"
                                 onclick="openImageModal(this.src)">
                            <div class="image-overlay">
                                <i class="fas fa-search-plus"></i>
                            </div>
                        </div>

                        <!-- Thumbnails -->
                        {% set images = product.get_images_list() %}
                        {% if images|length > 1 %}
                        <div class="image-thumbnails">
                            {% for image in images[:5] %}
                            <div class="thumbnail-item {{ 'active' if loop.first else '' }}">
                                <img src="{{ image }}" alt="{{ product.name }}"
                                     onclick="changeMainImage(this.src, this.parentElement)">
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}
                </div>
                
                <!-- Product Info -->
                    <div class="product-info">
                        {% if product.category %}
                        <div class="product-category-badge">
                            <i class="fas fa-tag me-2"></i>{{ product.category.name }}
                        </div>
                        {% endif %}
                        
                        <h1 class="product-title">{{ product.name }}</h1>

                        {% if product.description %}
                        <p class="product-subtitle">{{ product.description }}</p>
                        {% endif %}

                        <!-- Price Section -->
                        <div class="price-section" id="priceSection">
                            <div class="price-main">
                                <span class="price-symbol">¥</span>
                                <span class="price-value" id="priceValue">{{ "%.5f"|format(product.base_price) }}</span>
                            </div>
                            <div class="price-info">
                                <small>起价，最终价格根据规格和数量计算</small>
                            </div>
                            <div class="discount-info" id="discountInfo"></div>
                        </div>
                    
                        <!-- Specifications -->
                        {% if attribute_groups_with_config %}
                        <div class="spec-section">
                            <div class="spec-title">
                                <i class="fas fa-cog me-2"></i>商品规格
                                <small class="text-muted ms-2">(选择您需要的规格)</small>
                            </div>
                            {% for group_config in attribute_groups_with_config %}
                            {% set group = group_config.group %}
                            {% set group_attrs = group_config.attributes %}
                            {% set group_name = group.name %}
                            <div class="spec-group mb-3">
                                <div class="spec-inline-layout">
                                <h6 class="spec-group-title">{{ group_name }}</h6>
                                    
                                    {% if group and group.display_type == 'select' %}
                                    <!-- 下拉框显示 -->
                                    <div class="spec-select-container flex-grow-1">
                                        <select class="form-select spec-select"
                                                data-group="{{ group_name }}"
                                                data-allow-custom="{{ group_config.allow_custom_input|lower }}"
                                                data-custom-type="{{ group_config.custom_input_type or 'text' }}"
                                                data-custom-placeholder="{{ group_config.custom_input_placeholder or '请输入自定义值' }}"
                                                data-custom-validation="{{ group_config.custom_validation_pattern or '' }}"
                                                data-custom-message="{{ group_config.custom_validation_message or '请输入有效值' }}"
                                                data-custom-price-modifier="{{ group.custom_price_modifier or 0 }}"
                                                data-custom-price-type="{{ group.custom_price_modifier_type or 'fixed' }}"
                                                data-custom-price-formula="{{ group.custom_price_formula or '' }}"
                                                onchange="handleAttributeSelectChange(this)">
                                            <option value="">请选择{{ group_name }}</option>
                                            {% for attr in group_attrs %}
                                            <option value="{{ attr.id }}"
                                                    data-group="{{ group_name }}"
                                                    data-id="{{ attr.id }}"
                                                    data-price-modifier="{{ attr.price_modifier }}"
                                                    data-modifier-type="{{ attr.price_modifier_type }}"
                                                    data-is-quantity-based="{{ attr.is_quantity_based|lower }}"
                                                    data-price-formula="{{ attr.price_formula or '' }}"
                                                    data-quantity-unit="{{ attr.quantity_unit or '本' }}"
                                                    data-min-quantity="{{ attr.min_quantity or 1 }}"
                                                    data-max-quantity="{{ attr.max_quantity or 9999 }}">
                                                {{ attr.value }}
                                            </option>
                                            {% endfor %}
                                            {% if group_config.allow_custom_input %}
                                            <!-- 自定义选项 -->
                                            <option value="custom"
                                                    data-group="{{ group_name }}"
                                                    data-id="custom"
                                                    data-custom="true">
                                                {{ group_config.custom_input_label or '自定义' }}
                                            </option>
                                            {% endif %}
                                        </select>

                                        {% if group_config.allow_custom_input %}
                                        <!-- 自定义输入框（初始隐藏） -->
                                        <div class="custom-input-wrapper mt-2" id="customInput_{{ group_name.replace(' ', '_') }}" style="display: none;">
                                            <div class="input-group">
                                                {% if group_config.custom_input_type == 'textarea' %}
                                                <textarea class="form-control custom-input-field"
                                                          data-group="{{ group_name }}"
                                                          data-custom-group="true"
                                                          placeholder="{{ group_config.custom_input_placeholder or '请输入自定义值' }}"
                                                          rows="3"
                                                          onchange="handleCustomGroupInput(this)"
                                                          oninput="validateCustomGroupInput(this)"></textarea>
                                                {% elif group_config.custom_input_type == 'number' %}
                                                <input type="number"
                                                       class="form-control custom-input-field"
                                                       data-group="{{ group_name }}"
                                                       data-custom-group="true"
                                                       placeholder="{{ group_config.custom_input_placeholder or '请输入数字' }}"
                                                       onchange="handleCustomGroupInput(this)"
                                                       oninput="validateCustomGroupInput(this)">
                                                {% else %}
                                                <input type="text"
                                                       class="form-control custom-input-field"
                                                       data-group="{{ group_name }}"
                                                       data-custom-group="true"
                                                       placeholder="{{ group_config.custom_input_placeholder or '请输入自定义值' }}"
                                                       onchange="handleCustomGroupInput(this)"
                                                       oninput="validateCustomGroupInput(this)">
                                                {% endif %}
                                                <button type="button" class="btn btn-outline-secondary" onclick="clearCustomInput('{{ group_name.replace(' ', '_') }}')">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                            <div class="invalid-feedback" id="customFeedback_{{ group_name.replace(' ', '_') }}"></div>
                                            {% if group.custom_price_formula %}
                                            <small class="text-muted mt-1">
                                                <i class="fas fa-calculator me-1"></i>根据输入值自动计算价格
                                            </small>
                                            {% endif %}
                                        </div>
                                        {% endif %}
                                    </div>
                                    {% else %}
                                    <!-- 根据属性的input_type显示不同的输入方式 -->
                                    <div class="spec-options flex-grow-1">
                                    {% for attr in group_attrs %}
                                        {% if attr.input_type == 'input' and attr.is_custom_input %}
                                        <!-- 自定义输入框 -->
                                        <div class="custom-input-container">
                                            <div class="form-group">
                                                <label for="custom_attr_{{ attr.id }}" class="form-label">{{ attr.name }}</label>
                                                <div class="input-group">
                                                    <input type="text"
                                                           class="form-control custom-attribute-input"
                                                           id="custom_attr_{{ attr.id }}"
                                                           data-group="{{ group_name }}"
                                                           data-id="{{ attr.id }}"
                                                           data-price-modifier="{{ attr.price_modifier }}"
                                                           data-modifier-type="{{ attr.price_modifier_type }}"
                                                           data-is-quantity-based="{{ attr.is_quantity_based|lower }}"
                                                           data-price-formula="{{ attr.price_formula or '' }}"
                                                           data-quantity-unit="{{ attr.quantity_unit or '张' }}"
                                                           data-min-quantity="{{ attr.min_quantity or 1 }}"
                                                           data-max-quantity="{{ attr.max_quantity or 9999 }}"
                                                           data-validation-pattern="{{ attr.validation_pattern or '' }}"
                                                           data-validation-message="{{ attr.validation_message or '请输入有效值' }}"
                                                           placeholder="{{ attr.input_placeholder or '请输入' + attr.name }}"
                                                           value="{{ attr.default_value or '' }}"
                                                           onchange="handleCustomAttributeChange(this)"
                                                           oninput="validateCustomInput(this)">
                                                    {% if attr.quantity_unit %}
                                                    <span class="input-group-text">{{ attr.quantity_unit }}</span>
                                                    {% endif %}
                                                </div>
                                                <div class="invalid-feedback" id="feedback_{{ attr.id }}"></div>
                                                {% if attr.price_formula %}
                                                <small class="text-muted">
                                                    <i class="fas fa-calculator me-1"></i>根据数量自动计算价格
                                                </small>
                                                {% endif %}
                                            </div>
                                        </div>
                                        {% else %}
                                        <!-- 传统的单选按钮显示 -->
                                        <div class="spec-option">
                                            <input type="checkbox"
                                                   id="attr_{{ attr.id }}"
                                                   data-group="{{ group_name }}"
                                                   data-id="{{ attr.id }}"
                                                   data-price-modifier="{{ attr.price_modifier }}"
                                                   data-modifier-type="{{ attr.price_modifier_type }}"
                                                   data-is-quantity-based="{{ attr.is_quantity_based|lower }}"
                                                   data-price-formula="{{ attr.price_formula or '' }}"
                                                   data-quantity-unit="{{ attr.quantity_unit or '本' }}"
                                                   data-min-quantity="{{ attr.min_quantity or 1 }}"
                                                   data-max-quantity="{{ attr.max_quantity or 9999 }}"
                                                   onchange="handleAttributeChange(this)">
                                            <label for="attr_{{ attr.id }}" class="spec-option-label">
                                                {{ attr.value }}
                                                {% if attr.is_quantity_based and attr.price_formula %}
                                                <small class="d-block text-primary"><i class="fas fa-calculator"></i> 公式计算</small>
                                                {% endif %}
                                            </label>
                                        </div>
                                        {% endif %}
                                    {% endfor %}

                                    {% if group_config.allow_custom_input %}
                                    <!-- 自定义输入选项（单选按钮模式） -->
                                    <div class="spec-option">
                                        <input type="checkbox"
                                               id="custom_radio_{{ group_name.replace(' ', '_') }}"
                                               data-group="{{ group_name }}"
                                               data-id="custom"
                                               data-custom="true"
                                               data-custom-group="true"
                                               onchange="handleCustomRadioChange(this)">
                                        <label for="custom_radio_{{ group_name.replace(' ', '_') }}" class="spec-option-label">
                                            {{ group_config.custom_input_label or '自定义' }}
                                        </label>
                                    </div>
                                    {% endif %}
                                    </div>

                                    {% if group_config.allow_custom_input %}
                                    <!-- 自定义输入框（单选按钮模式，初始隐藏） -->
                                    <div class="custom-input-wrapper" id="customRadioInput_{{ group_name.replace(' ', '_') }}" style="display: none;">
                                        <div class="input-group">
                                            {% if group_config.custom_input_type == 'textarea' %}
                                            <textarea class="form-control custom-input-field"
                                                      data-group="{{ group_name }}"
                                                      data-custom-group="true"
                                                      placeholder="{{ group_config.custom_input_placeholder or '请输入自定义值' }}"
                                                      rows="3"
                                                      onchange="handleCustomGroupInput(this)"
                                                      oninput="validateCustomGroupInput(this)"></textarea>
                                            {% elif group_config.custom_input_type == 'number' %}
                                            <input type="number"
                                                   class="form-control custom-input-field"
                                                   data-group="{{ group_name }}"
                                                   data-custom-group="true"
                                                   placeholder="{{ group_config.custom_input_placeholder or '请输入数字' }}"
                                                   onchange="handleCustomGroupInput(this)"
                                                   oninput="validateCustomGroupInput(this)">
                                            {% else %}
                                            <input type="text"
                                                   class="form-control custom-input-field"
                                                   data-group="{{ group_name }}"
                                                   data-custom-group="true"
                                                   placeholder="{{ group_config.custom_input_placeholder or '请输入自定义值' }}"
                                                   onchange="handleCustomGroupInput(this)"
                                                   oninput="validateCustomGroupInput(this)">
                                            {% endif %}
                                            <button type="button" class="btn btn-outline-secondary" onclick="clearCustomRadioInput('{{ group_name.replace(' ', '_') }}')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="invalid-feedback" id="customRadioFeedback_{{ group_name.replace(' ', '_') }}"></div>
                                        {% if group.custom_price_formula %}
                                        <small class="text-muted mt-1">
                                            <i class="fas fa-calculator me-1"></i>根据输入值自动计算价格
                                        </small>
                                        {% endif %}
                                    </div>
                                    {% endif %}
                                    {% endif %}

                                    <!-- 公式属性的自定义数量输入区域 -->
                                    <div class="formula-quantity-section" id="formulaQuantity_{{ group_name.replace(' ', '_') }}" style="display: none;">
                                        <div class="mt-2 p-2 bg-light rounded border">
                                            <div class="d-flex align-items-center">
                                                <div class="input-group input-group-sm" style="max-width: 120px; width: 120px;">
                                                    <button type="button" class="btn btn-sm btn-outline-secondary formula-qty-btn" data-action="decrease">
                                                        <i class="fas fa-minus"></i>
                                                    </button>
                                                    <input type="number" 
                                                           class="form-control form-control-sm text-center formula-quantity-input"
                                                           data-group="{{ group_name }}"
                                                           min="1" 
                                                           max="9999" 
                                                           value="1"
                                                           style="min-width: 40px; width: 50px;"
                                                           onchange="updateFormulaPrice(this)">
                                                    <button type="button" class="btn btn-sm btn-outline-secondary formula-qty-btn" data-action="increase">
                                                        <i class="fas fa-plus"></i>
                                                    </button>
                                                </div>
                                                <small class="text-muted ms-1" id="quantityUnit_{{ group_name.replace(' ', '_') }}">本</small>
                                                
                                                <!-- 隐藏公式显示和价格显示 -->
                                                <small class="text-muted d-none" id="formulaDisplay_{{ group_name.replace(' ', '_') }}"></small>
                                                <div class="d-none">
                                                    <small class="text-muted">此项价格：</small>
                                                    <div class="fw-bold text-primary" id="formulaPrice_{{ group_name.replace(' ', '_') }}">¥0.00000</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="spec-section">
                            <div class="spec-empty">
                                <i class="fas fa-info-circle me-2"></i>
                                <span class="text-muted">该商品暂无可选规格</span>
                            </div>
                        </div>
                        {% endif %}
                    
                        <!-- Quantity Section -->
                        <div class="quantity-section">
                            <div class="spec-inline-layout">
                            <div class="quantity-label">
                                    <i class="fas fa-calculator me-2"></i>总数量
                            </div>
                            <div class="quantity-controls">
                                <div class="quantity-selector">
                                    <button type="button" class="quantity-btn" data-action="decrease">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                    <input type="number" class="quantity-input" value="{{ product.min_quantity }}"
                                               min="{{ product.min_quantity }}" max="{{ product.max_quantity }}" id="quantityInput"
                                               style="width: 50px; min-width: 40px;">
                                    <button type="button" class="quantity-btn" data-action="increase">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                                <span class="quantity-limit">
                                    ({{ product.min_quantity }}-{{ product.max_quantity }}{{ product.unit }})
                                </span>
                                </div>
                            </div>
                        </div>
                    
                        <!-- Action Buttons -->
                        <div class="action-section">
                            {% if current_user.is_authenticated %}
                            
                            <!-- 收货地址表单 -->
                            <div class="address-section mb-4">
                                <div class="spec-title">
                                    <i class="fas fa-map-marker-alt me-2"></i>收货地址
                                    <small class="text-muted ms-2">(请填写收货信息)</small>
                                </div>
                                
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="recipientName" class="form-label">收货人姓名 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="recipientName" name="recipient_name" 
                                               placeholder="请输入收货人姓名" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="recipientPhone" class="form-label">联系电话 <span class="text-danger">*</span></label>
                                        <input type="tel" class="form-control" id="recipientPhone" name="recipient_phone" 
                                               placeholder="请输入联系电话" required pattern="^1[3-9]\d{9}$">
                                    </div>
                                    <div class="col-md-4 region-selector-col">
                                        <label for="shippingProvince" class="form-label">省份 <span class="text-danger required-mark">*</span></label>
                                        <select class="form-control" id="shippingProvince" name="shipping_province" required>
                                            <option value="">请选择省份</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 region-selector-col">
                                        <label for="shippingCity" class="form-label">城市 <span class="text-danger required-mark">*</span></label>
                                        <select class="form-control" id="shippingCity" name="shipping_city" required>
                                            <option value="">请选择城市</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 region-selector-col">
                                        <label for="shippingDistrict" class="form-label">区县</label>
                                        <select class="form-control" id="shippingDistrict" name="shipping_district">
                                            <option value="">请选择区县</option>
                                        </select>
                                        <input type="text" class="form-control" id="shippingDistrictInput" name="shipping_district_input"
                                               placeholder="请输入区县名称" style="display: none;">
                                    </div>
                                    <div class="col-12">
                                        <label for="shippingAddress" class="form-label">详细地址 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="shippingAddress" name="shipping_address" 
                                               placeholder="请输入详细地址（如街道、门牌号等）" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="shippingPostalCode" class="form-label">邮政编码</label>
                                        <input type="text" class="form-control" id="shippingPostalCode" name="shipping_postal_code" 
                                               placeholder="请输入邮政编码（可选）" pattern="^\d{6}$">
                                    </div>
                                    <div class="col-12">
                                        <label for="orderNotes" class="form-label">订单备注</label>
                                        <textarea class="form-control" id="orderNotes" name="notes" rows="3" 
                                                  placeholder="请输入订单备注（可选）"></textarea>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="action-buttons">
                                <button type="button" class="btn-buy-now" onclick="createOrder()">
                                    <i class="fas fa-shopping-bag"></i>
                                    立即下单
                                </button>
                            </div>
                            {% else %}
                            <div class="action-buttons">
                                <a href="{{ url_for('auth.login') }}" class="btn-login">
                                    <i class="fas fa-sign-in-alt"></i>
                                    登录后购买
                                </a>
                            </div>
                            {% endif %}
                        </div>
                            </div>
                                </div>
                            </div>
        </div>
    </div>
</section>

<!-- Floating Actions -->
<div class="floating-actions">
    <button class="floating-btn" title="分享商品" onclick="shareProduct()">
        <i class="fas fa-share-alt"></i>
    </button>
    <button class="floating-btn" title="收藏商品" onclick="toggleWishlist({{ product.id|tojson }})">
        <i class="far fa-heart" id="wishlistIcon"></i>
    </button>
    <button class="floating-btn" title="返回顶部" onclick="scrollToTop()">
        <i class="fas fa-arrow-up"></i>
    </button>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/simple-region-selector.js') }}"></script>
<script>
// Global variables
let selectedAttributes = [];
let currentQuantity = {{ product.min_quantity|tojson }};
let regionSelector; // 省市县选择器实例

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    // Set global variables
    window.productId = {{ product.id|tojson }};
    window.productMinQuantity = {{ product.min_quantity|tojson }};
    window.productMaxQuantity = {{ product.max_quantity|tojson }};
    
    // Initialize components
    initializeQuantityControls();
    initializeSpecOptions();
    initializeAddressForm();
    initializeRegionSelector(); // 初始化标准化省市县选择器
    
    // Calculate initial price
    updatePrice();
});

// 初始化标准化省市县选择器
function initializeRegionSelector() {
    // 创建省市县选择器实例
    regionSelector = new SimpleRegionSelector({
        provinceSelector: '#shippingProvince',
        citySelector: '#shippingCity',
        districtSelector: '#shippingDistrict',
        districtInputSelector: '#shippingDistrictInput',
        provincePlaceholder: '请选择省份',
        cityPlaceholder: '请选择城市',
        districtPlaceholder: '请选择区县',
        onChange: function(region) {
            console.log('区域选择变化:', region);
        }
    });
}

// 获取区县值的辅助函数
function getDistrictValue() {
    const districtSelect = document.getElementById('shippingDistrict');
    const districtInput = document.getElementById('shippingDistrictInput');

    // 检查哪个元素是可见的
    if (districtSelect && districtSelect.style.display !== 'none' && !districtSelect.disabled) {
        // 选择框模式
        return districtSelect.value.trim();
    } else if (districtInput && districtInput.style.display !== 'none' && !districtInput.disabled) {
        // 输入框模式
        const value = districtInput.value.trim();
        return value === '' ? ' ' : value;
    }

    return ' '; // 默认返回空格
}

// Initialize address form validation
function initializeAddressForm() {
    const phoneInput = document.getElementById('recipientPhone');
    const postalCodeInput = document.getElementById('shippingPostalCode');
    
    if (phoneInput) {
        phoneInput.addEventListener('input', function() {
            validatePhoneNumber(this);
        });
    }
    
    if (postalCodeInput) {
        postalCodeInput.addEventListener('input', function() {
            validatePostalCode(this);
        });
    }
}

function validatePhoneNumber(input) {
    const phone = input.value.trim();
    const phonePattern = /^1[3-9]\d{9}$/;
    
    if (phone && !phonePattern.test(phone)) {
        input.setCustomValidity('请输入正确的手机号码格式');
    } else {
        input.setCustomValidity('');
    }
}

function validatePostalCode(input) {
    const postalCode = input.value.trim();
    const postalPattern = /^\d{6}$/;
    
    if (postalCode && !postalPattern.test(postalCode)) {
        input.setCustomValidity('邮政编码必须是6位数字');
    } else {
        input.setCustomValidity('');
    }
}

// Create order function
function createOrder() {
    // 验证规格选择
    if (!validateSpecifications()) {
        return;
    }

    // 验证自定义输入
    if (!validateCustomInputs()) {
        return;
    }
    
    // 验证地址信息
    if (!validateAddressForm()) {
        return;
    }
    
    const button = document.querySelector('.btn-buy-now');
    if (!button || button.disabled) {
        return;
    }
    
    const originalContent = button.innerHTML;
    
    // Loading state
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>创建订单中...';
    button.disabled = true;
    
    // 准备订单数据
    const orderData = {
        product_id: window.productId,
        quantity: currentQuantity,
        selected_attributes: selectedAttributes,
        attribute_quantities: getAttributeQuantities(), // 添加自定义套数信息
        recipient_name: document.getElementById('recipientName').value.trim(),
        recipient_phone: document.getElementById('recipientPhone').value.trim(),
        shipping_province: document.getElementById('shippingProvince').value.trim(),
        shipping_city: document.getElementById('shippingCity').value.trim(),
        shipping_district: getDistrictValue(),
        shipping_address: document.getElementById('shippingAddress').value.trim(),
        shipping_postal_code: document.getElementById('shippingPostalCode').value.trim(),
        notes: document.getElementById('orderNotes').value.trim()
    };
    
    console.log('创建订单请求:', orderData);
    console.log('订单商品ID:', orderData.product_id);
    console.log('订单数量:', orderData.quantity);
    console.log('选中属性IDs:', orderData.selected_attributes);
    console.log('收货人信息:', {
        姓名: orderData.recipient_name,
        电话: orderData.recipient_phone
    });
    console.log('收货地址:', {
        省份: orderData.shipping_province,
        城市: orderData.shipping_city,
        区县: orderData.shipping_district,
        详细地址: orderData.shipping_address,
        邮编: orderData.shipping_postal_code
    });
    
    // 获取CSRF令牌
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || 
                      document.querySelector('input[name="csrf_token"]')?.value || 
                      '{{ csrf_token() }}';
    console.log('CSRF令牌:', csrfToken);
    
    fetch('/create-order', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify(orderData)
    })
    .then(response => {
        console.log('响应状态:', response.status);
        console.log('响应头:', Object.fromEntries([...response.headers]));
        return response.json();
    })
    .then(data => {
        console.log('响应数据:', data);
        
        if (data.error) {
            showNotification('error', data.error);
            button.innerHTML = originalContent;
            button.disabled = false;
        } else {
            // 创建订单成功后，直接发起支付请求
            if (data.order_id) {
                // 显示支付加载状态
                showNotification('success', '订单创建成功，正在跳转到支付页面...');
                
                // 直接发起支付请求
                initiatePayment(data.order_id);
            } else if (data.redirect_url) {
                window.location.href = data.redirect_url;
            } else {
                window.location.href = '/orders';
            }
        }
    })
    .catch(error => {
        console.error('创建订单失败:', error);
        showNotification('error', '创建订单失败，请重试');
        button.innerHTML = originalContent;
        button.disabled = false;
    });
}

function validateSpecifications() {
    // 检查是否有必选属性未选择
    const requiredGroups = new Set();
    const selectedGroups = new Set();
    
    // 收集所有属性组和选中的属性组（复选框）
    document.querySelectorAll('input[data-group]').forEach(input => {
        const group = input.dataset.group;
        requiredGroups.add(group);
        
        if (input.checked) {
            selectedGroups.add(group);
        }
    });
    
    // 收集所有属性组和选中的属性组（下拉框）
    document.querySelectorAll('select[data-group]').forEach(select => {
        const group = select.dataset.group;
        requiredGroups.add(group);
        
        if (select.selectedIndex > 0) { // 选择了非默认选项
            selectedGroups.add(group);
        }
    });
    
    console.log('必选属性组:', [...requiredGroups]);
    console.log('已选择属性组:', [...selectedGroups]);
    
    // 检查是否所有组都有选择
    for (let group of requiredGroups) {
        if (!selectedGroups.has(group)) {
            showNotification('warning', `请选择${group}`);
            return false;
        }
    }
    
    return true;
}

function validateAddressForm() {
    const requiredFields = [
        { id: 'recipientName', name: '收货人姓名' },
        { id: 'recipientPhone', name: '联系电话' },
        { id: 'shippingProvince', name: '省份' },
        { id: 'shippingCity', name: '城市' },
        { id: 'shippingAddress', name: '详细地址' }
    ];

    // 检查基本必填字段
    for (let field of requiredFields) {
        const input = document.getElementById(field.id);
        if (!input || !input.value.trim()) {
            showNotification('warning', `请填写${field.name}`);
            if (input) input.focus();
            return false;
        }
    }

    // 特殊处理区县字段
    const districtSelect = document.getElementById('shippingDistrict');
    const districtInput = document.getElementById('shippingDistrictInput');

    if (districtSelect && districtSelect.style.display !== 'none' && !districtSelect.disabled) {
        // 选择框模式：必须选择一个区县
        const districtValue = districtSelect.value.trim();
        if (!districtValue || districtValue === '') {
            showNotification('warning', '请选择区县');
            districtSelect.focus();
            return false;
        }
    } else if (districtInput && districtInput.style.display !== 'none' && !districtInput.disabled) {
        // 输入框模式：如果为空，自动设置为空格
        const districtValue = districtInput.value.trim();
        if (!districtValue) {
            districtInput.value = ' ';
        }
    }
    
    // 验证手机号格式
    const phoneInput = document.getElementById('recipientPhone');
    if (phoneInput && phoneInput.value.trim()) {
        const phonePattern = /^1[3-9]\d{9}$/;
        if (!phonePattern.test(phoneInput.value.trim())) {
            showNotification('warning', '请输入正确的手机号码格式');
            phoneInput.focus();
            return false;
        }
    }
    
    // 验证邮政编码格式（如果填写了）
    const postalCodeInput = document.getElementById('shippingPostalCode');
    if (postalCodeInput && postalCodeInput.value.trim()) {
        const postalPattern = /^\d{6}$/;
        if (!postalPattern.test(postalCodeInput.value.trim())) {
            showNotification('warning', '邮政编码必须是6位数字');
            postalCodeInput.focus();
            return false;
        }
    }
    
    return true;
}

function showNotification(type, message) {
    if (window.ModernUI && window.ModernUI.notification) {
        window.ModernUI.notification[type](message);
    } else if (window.ModernNotification) {
        window.ModernNotification[type](message);
    } else {
        alert(message);
    }
}

// Quantity controls
function initializeQuantityControls() {
    const quantityInput = document.getElementById('quantityInput');
    
    // 更精确的选择器，确保只选择总数量部分的按钮
    const decreaseBtn = document.querySelector('.quantity-selector .quantity-btn[data-action="decrease"]');
    const increaseBtn = document.querySelector('.quantity-selector .quantity-btn[data-action="increase"]');
    
    console.log('数量控制按钮初始化:', { quantityInput, decreaseBtn, increaseBtn });
    
    if (!quantityInput || !decreaseBtn || !increaseBtn) {
        console.error('无法找到数量控制元素');
        return;
    }
    
    // 移除可能已存在的事件监听器
    decreaseBtn.removeEventListener('click', decreaseQuantity);
    increaseBtn.removeEventListener('click', increaseQuantity);
    
    // 添加新的事件监听器
    decreaseBtn.addEventListener('click', decreaseQuantity);
    increaseBtn.addEventListener('click', increaseQuantity);
    
    // 减少数量的函数
    function decreaseQuantity() {
        console.log('点击减少按钮');
        if (currentQuantity > window.productMinQuantity) {
            currentQuantity--;
            quantityInput.value = currentQuantity;
            updatePrice();
            updateButtonStates();
        }
    }
    
    // 增加数量的函数
    function increaseQuantity() {
        console.log('点击增加按钮');
        if (currentQuantity < window.productMaxQuantity) {
            currentQuantity++;
            quantityInput.value = currentQuantity;
            updatePrice();
            updateButtonStates();
        }
    }
    
    // 监听输入框值变化
    quantityInput.addEventListener('change', function() {
        console.log('输入框值变化:', this.value);
        const value = parseInt(this.value);
        if (value >= window.productMinQuantity && value <= window.productMaxQuantity) {
            currentQuantity = value;
            updatePrice();
        } else {
            this.value = currentQuantity;
        }
        updateButtonStates();
    });
    
    // Update button states
    function updateButtonStates() {
        decreaseBtn.disabled = currentQuantity <= window.productMinQuantity;
        increaseBtn.disabled = currentQuantity >= window.productMaxQuantity;
    }
    
    updateButtonStates();
}

// Specification options
function initializeSpecOptions() {
    // 不需要添加事件监听器，因为HTML中已经有onchange
}

// 新的属性变化处理函数
function handleAttributeChange(checkbox) {
    const group = checkbox.dataset.group;
    const attrId = parseInt(checkbox.dataset.id);
    
    if (!attrId) return;
    
    if (checkbox.checked) {
        // 清除同组的其他选择
        document.querySelectorAll(`input[data-group="${group}"]`).forEach(input => {
            if (input !== checkbox) {
                input.checked = false;
            }
        });
        
        // 更新选中的属性
        selectedAttributes = selectedAttributes.filter(id => {
            const optionElement = document.querySelector(`input[data-id="${id}"]`);
            return optionElement && optionElement.dataset.group !== group;
        });
        
        selectedAttributes.push(attrId);
        
        // 处理公式属性
        handleFormulaAttribute(checkbox, group, true);
    } else {
        // 移除取消选中的属性
        selectedAttributes = selectedAttributes.filter(id => id !== attrId);
        
        // 隐藏公式输入区域
        hideFormulaQuantitySection(group);
    }
    
    console.log('Updated selected attributes:', selectedAttributes);
    updatePrice();
}

// 处理下拉框属性选择变化
function handleAttributeSelectChange(selectElement) {
    const group = selectElement.dataset.group;
    const selectedOption = selectElement.options[selectElement.selectedIndex];
    const customInputWrapper = document.getElementById(`customInput_${group.replace(/\s+/g, '_')}`);

    // 移除该组的所有现有选择
    selectedAttributes = selectedAttributes.filter(id => {
        const optionElement = document.querySelector(`input[data-id="${id}"], option[data-id="${id}"]`);
        return optionElement && optionElement.dataset.group !== group;
    });

    // 隐藏之前的公式输入区域
    hideFormulaQuantitySection(group);

    // 检查是否选择了自定义选项
    if (selectedOption.value === 'custom') {
        // 显示自定义输入框
        if (customInputWrapper) {
            customInputWrapper.style.display = 'block';
            const customInput = customInputWrapper.querySelector('.custom-input-field');
            if (customInput) {
                customInput.focus();
            }
        }
        selectElement.classList.add('selected');
        console.log(`显示${group}的自定义输入框`);
    } else {
        // 隐藏自定义输入框
        if (customInputWrapper) {
            customInputWrapper.style.display = 'none';
            const customInput = customInputWrapper.querySelector('.custom-input-field');
            if (customInput) {
                customInput.value = '';
                customInput.classList.remove('is-valid', 'is-invalid');
            }
        }

        // 添加新选择（如果有选择）
        if (selectedOption.value) {
            const attrId = parseInt(selectedOption.dataset.id);
            if (attrId) {
                selectedAttributes.push(attrId);
                selectElement.classList.add('selected');

                // 处理公式属性
                handleFormulaAttribute(selectedOption, group, true);
            }
        } else {
            selectElement.classList.remove('selected');
        }
    }

    console.log('Updated selected attributes from select:', selectedAttributes);
    updatePrice();
}

// 处理自定义输入属性变化
function handleCustomAttributeChange(inputElement) {
    const group = inputElement.dataset.group;
    const attrId = parseInt(inputElement.dataset.id);
    const value = inputElement.value.trim();

    if (!attrId) return;

    // 验证输入
    if (!validateCustomInput(inputElement)) {
        return;
    }

    // 移除该组的所有现有选择
    selectedAttributes = selectedAttributes.filter(id => {
        const optionElement = document.querySelector(`input[data-id="${id}"], .custom-attribute-input[data-id="${id}"]`);
        return optionElement && optionElement.dataset.group !== group;
    });

    // 如果有值，添加到选中属性
    if (value) {
        selectedAttributes.push(attrId);
        inputElement.classList.add('selected');

        // 存储自定义值
        inputElement.dataset.customValue = value;

        console.log(`自定义属性 ${group} 设置为: ${value}`);
    } else {
        inputElement.classList.remove('selected');
        delete inputElement.dataset.customValue;
    }

    console.log('Updated selected attributes from custom input:', selectedAttributes);
    updatePrice();
}

// 处理属性组自定义输入变化
function handleCustomGroupInput(inputElement) {
    const group = inputElement.dataset.group;
    const value = inputElement.value.trim();

    // 验证输入
    if (!validateCustomGroupInput(inputElement)) {
        return;
    }

    // 移除该组的所有现有选择
    selectedAttributes = selectedAttributes.filter(id => {
        const optionElement = document.querySelector(`input[data-id="${id}"], option[data-id="${id}"]`);
        return optionElement && optionElement.dataset.group !== group;
    });

    // 如果有值，标记为自定义选择
    if (value) {
        // 使用特殊的自定义ID标记
        const customId = `custom_${group.replace(/\s+/g, '_')}`;
        selectedAttributes.push(customId);
        inputElement.classList.add('selected');

        // 存储自定义值
        inputElement.dataset.customValue = value;

        // 确保对应的自定义选项被选中
        const customRadio = document.getElementById(`custom_radio_${group.replace(/\s+/g, '_')}`);
        if (customRadio) {
            customRadio.checked = true;
            customRadio.classList.add('selected');
        }

        console.log(`属性组 ${group} 自定义输入: ${value}`);
    } else {
        inputElement.classList.remove('selected');
        delete inputElement.dataset.customValue;

        // 如果值为空，取消自定义选项的选择
        const customRadio = document.getElementById(`custom_radio_${group.replace(/\s+/g, '_')}`);
        if (customRadio) {
            customRadio.checked = false;
            customRadio.classList.remove('selected');
        }
    }

    console.log('Updated selected attributes from custom group input:', selectedAttributes);
    updatePrice();
}

// 验证属性组自定义输入
function validateCustomGroupInput(inputElement) {
    const group = inputElement.dataset.group;
    const value = inputElement.value.trim();
    const selectElement = document.querySelector(`select[data-group="${group}"]`);

    // 尝试获取下拉框模式的反馈元素，如果没有则尝试单选按钮模式的
    let feedbackElement = document.getElementById(`customFeedback_${group.replace(/\s+/g, '_')}`);
    if (!feedbackElement) {
        feedbackElement = document.getElementById(`customRadioFeedback_${group.replace(/\s+/g, '_')}`);
    }

    // 清除之前的验证状态
    inputElement.classList.remove('is-valid', 'is-invalid');
    if (feedbackElement) {
        feedbackElement.textContent = '';
        feedbackElement.style.display = 'none';
    }

    if (!value) {
        return true; // 空值是允许的
    }

    // 获取验证规则 - 优先从下拉框获取，如果没有则从自定义单选按钮获取
    let validationPattern, validationMessage, inputType;

    if (selectElement) {
        validationPattern = selectElement.dataset.customValidation;
        validationMessage = selectElement.dataset.customMessage || '请输入有效值';
        inputType = selectElement.dataset.customType || 'text';
    } else {
        // 从自定义单选按钮获取验证规则（如果有的话）
        const customRadio = document.getElementById(`custom_radio_${group.replace(/\s+/g, '_')}`);
        if (customRadio) {
            validationPattern = customRadio.dataset.customValidation;
            validationMessage = customRadio.dataset.customMessage || '请输入有效值';
            inputType = customRadio.dataset.customType || inputElement.type || 'text';
        }
    }

    // 数字类型验证
    if (inputType === 'number') {
        const numValue = parseFloat(value);
        if (isNaN(numValue)) {
            showValidationError(inputElement, feedbackElement, '请输入有效的数字');
            return false;
        }
    }

    // 正则表达式验证
    if (validationPattern && value) {
        try {
            const regex = new RegExp(validationPattern);
            if (!regex.test(value)) {
                showValidationError(inputElement, feedbackElement, validationMessage);
                return false;
            }
        } catch (e) {
            console.warn('无效的正则表达式:', validationPattern);
        }
    }

    // 验证通过
    inputElement.classList.add('is-valid');
    return true;
}

// 显示验证错误
function showValidationError(inputElement, feedbackElement, message) {
    inputElement.classList.add('is-invalid');
    if (feedbackElement) {
        feedbackElement.textContent = message;
        feedbackElement.style.display = 'block';
    }
}

// 清除自定义输入
function clearCustomInput(groupId) {
    const customInputWrapper = document.getElementById(`customInput_${groupId}`);
    const selectElement = document.querySelector(`select[data-group="${groupId.replace(/_/g, ' ')}"]`);

    if (customInputWrapper) {
        const customInput = customInputWrapper.querySelector('.custom-input-field');
        if (customInput) {
            customInput.value = '';
            customInput.classList.remove('is-valid', 'is-invalid', 'selected');
            delete customInput.dataset.customValue;
        }
        customInputWrapper.style.display = 'none';
    }

    if (selectElement) {
        selectElement.selectedIndex = 0; // 重置为默认选项
        selectElement.classList.remove('selected');
    }

    // 移除该组的选择
    const group = groupId.replace(/_/g, ' ');
    selectedAttributes = selectedAttributes.filter(id => {
        if (typeof id === 'string' && id.startsWith('custom_')) {
            return !id.includes(groupId);
        }
        const optionElement = document.querySelector(`input[data-id="${id}"], option[data-id="${id}"]`);
        return optionElement && optionElement.dataset.group !== group;
    });

    console.log('Cleared custom input for group:', group);
    updatePrice();
}

// 处理单选按钮模式的自定义输入选择
function handleCustomRadioChange(radioElement) {
    const group = radioElement.dataset.group;
    const customInputWrapper = document.getElementById(`customRadioInput_${group.replace(/\s+/g, '_')}`);

    if (radioElement.checked) {
        // 取消该组的其他选择
        const groupInputs = document.querySelectorAll(`input[data-group="${group}"]:not([data-custom="true"])`);
        groupInputs.forEach(input => {
            input.checked = false;
            input.classList.remove('selected');
        });

        // 显示自定义输入框
        if (customInputWrapper) {
            customInputWrapper.style.display = 'block';
            const customInput = customInputWrapper.querySelector('.custom-input-field');
            if (customInput) {
                customInput.focus();
            }
        }

        radioElement.classList.add('selected');
        console.log(`选择了属性组 ${group} 的自定义输入`);
    } else {
        // 隐藏自定义输入框
        if (customInputWrapper) {
            customInputWrapper.style.display = 'none';
            const customInput = customInputWrapper.querySelector('.custom-input-field');
            if (customInput) {
                customInput.value = '';
                customInput.classList.remove('is-valid', 'is-invalid', 'selected');
                delete customInput.dataset.customValue;
            }
        }

        radioElement.classList.remove('selected');

        // 移除该组的自定义选择
        selectedAttributes = selectedAttributes.filter(id => {
            if (typeof id === 'string' && id.startsWith('custom_')) {
                return !id.includes(group.replace(/\s+/g, '_'));
            }
            return true;
        });

        console.log(`取消了属性组 ${group} 的自定义输入`);
    }

    updatePrice();
}

// 清除单选按钮模式的自定义输入
function clearCustomRadioInput(groupId) {
    const customInputWrapper = document.getElementById(`customRadioInput_${groupId}`);
    const radioElement = document.getElementById(`custom_radio_${groupId}`);

    if (customInputWrapper) {
        const customInput = customInputWrapper.querySelector('.custom-input-field');
        if (customInput) {
            customInput.value = '';
            customInput.classList.remove('is-valid', 'is-invalid', 'selected');
            delete customInput.dataset.customValue;
        }
        customInputWrapper.style.display = 'none';
    }

    if (radioElement) {
        radioElement.checked = false;
        radioElement.classList.remove('selected');
    }

    // 移除该组的选择
    const group = groupId.replace(/_/g, ' ');
    selectedAttributes = selectedAttributes.filter(id => {
        if (typeof id === 'string' && id.startsWith('custom_')) {
            return !id.includes(groupId);
        }
        return true;
    });

    console.log('Cleared custom radio input for group:', group);
    updatePrice();
}

// 验证自定义输入
function validateCustomInput(inputElement) {
    const value = inputElement.value.trim();
    const pattern = inputElement.dataset.validationPattern;
    const message = inputElement.dataset.validationMessage || '请输入有效值';
    const feedbackElement = document.getElementById(`feedback_${inputElement.dataset.id}`);

    // 清除之前的验证状态
    inputElement.classList.remove('is-invalid', 'is-valid');
    if (feedbackElement) {
        feedbackElement.textContent = '';
    }

    // 如果没有值，不进行验证（允许为空）
    if (!value) {
        return true;
    }

    // 如果有验证模式，进行验证
    if (pattern) {
        try {
            const regex = new RegExp(pattern);
            if (!regex.test(value)) {
                inputElement.classList.add('is-invalid');
                if (feedbackElement) {
                    feedbackElement.textContent = message;
                }
                return false;
            }
        } catch (error) {
            console.error('正则表达式错误:', error);
            return true; // 如果正则表达式有错误，跳过验证
        }
    }

    // 验证通过
    inputElement.classList.add('is-valid');
    return true;
}

// 处理公式属性的显示/隐藏
function handleFormulaAttribute(element, group, isSelected) {
    const isQuantityBased = element.dataset.isQuantityBased === 'true';
    const priceFormula = element.dataset.priceFormula;
    
    if (isSelected && isQuantityBased && priceFormula) {
        showFormulaQuantitySection(group, element);
        } else {
        hideFormulaQuantitySection(group);
    }
}

// 显示公式数量输入区域
function showFormulaQuantitySection(group, element) {
    const sectionId = `formulaQuantity_${group.replace(/\s+/g, '_')}`;
    const section = document.getElementById(sectionId);
    
    if (!section) return;
    
    // 设置公式相关数据
    const quantityUnit = element.dataset.quantityUnit || '本';
    const minQuantity = parseInt(element.dataset.minQuantity) || 1;
    const maxQuantity = parseInt(element.dataset.maxQuantity) || 9999;
    const priceFormula = element.dataset.priceFormula;
    
    // 更新UI元素
    const quantityInput = section.querySelector('.formula-quantity-input');
    const unitElement = document.getElementById(`quantityUnit_${group.replace(/\s+/g, '_')}`);
    // 不再显示公式文本
    const formulaDisplay = document.getElementById(`formulaDisplay_${group.replace(/\s+/g, '_')}`);
    
    if (quantityInput) {
        quantityInput.min = minQuantity;
        quantityInput.max = maxQuantity;
        quantityInput.value = minQuantity;
        quantityInput.dataset.attrId = element.dataset.id;
        quantityInput.dataset.priceFormula = priceFormula;
    }
    
    if (unitElement) {
        unitElement.textContent = quantityUnit;
    }
    
    // 绑定按钮事件
    bindFormulaQuantityButtons(section, quantityInput, minQuantity, maxQuantity);
    
    // 显示区域
    section.style.display = 'block';
    
    // 初始计算公式价格 - 仍然需要计算价格，但不显示
    updateFormulaPrice(quantityInput);
}

// 隐藏公式数量输入区域
function hideFormulaQuantitySection(group) {
    const sectionId = `formulaQuantity_${group.replace(/\s+/g, '_')}`;
    const section = document.getElementById(sectionId);
    
    if (section) {
        section.style.display = 'none';
    }
}

// 绑定公式数量按钮事件
function bindFormulaQuantityButtons(section, quantityInput, minQuantity, maxQuantity) {
    const decreaseBtn = section.querySelector('.formula-qty-btn[data-action="decrease"]');
    const increaseBtn = section.querySelector('.formula-qty-btn[data-action="increase"]');
    
    console.log('公式数量按钮:', { decreaseBtn, increaseBtn, section });
    
    // 移除之前的事件监听器 - 使用更安全的方式
    if (decreaseBtn) {
        const newDecreaseBtn = decreaseBtn.cloneNode(true);
        decreaseBtn.parentNode.replaceChild(newDecreaseBtn, decreaseBtn);
    }
    
    if (increaseBtn) {
        const newIncreaseBtn = increaseBtn.cloneNode(true);
        increaseBtn.parentNode.replaceChild(newIncreaseBtn, increaseBtn);
    }
    
    // 重新获取元素并绑定事件
    const updatedDecreaseBtn = section.querySelector('.formula-qty-btn[data-action="decrease"]');
    const updatedIncreaseBtn = section.querySelector('.formula-qty-btn[data-action="increase"]');
    
    // 定义减少数量的函数
    function decreaseFormulaQuantity(e) {
        e.preventDefault(); // 防止表单提交
        e.stopPropagation(); // 防止事件冒泡
        console.log('减少公式数量');
        
        const currentValue = parseInt(quantityInput.value);
        if (currentValue > minQuantity) {
            quantityInput.value = currentValue - 1;
            updateFormulaPrice(quantityInput);
        }
    }
    
    // 定义增加数量的函数
    function increaseFormulaQuantity(e) {
        e.preventDefault(); // 防止表单提交
        e.stopPropagation(); // 防止事件冒泡
        console.log('增加公式数量');
        
        const currentValue = parseInt(quantityInput.value);
        if (currentValue < maxQuantity) {
            quantityInput.value = currentValue + 1;
            updateFormulaPrice(quantityInput);
        }
    }
    
    // 添加事件监听器
    if (updatedDecreaseBtn) {
        updatedDecreaseBtn.addEventListener('click', decreaseFormulaQuantity);
    }
    
    if (updatedIncreaseBtn) {
        updatedIncreaseBtn.addEventListener('click', increaseFormulaQuantity);
    }
}

// 更新公式价格显示
function updateFormulaPrice(quantityInput) {
    const group = quantityInput.dataset.group;
    const attrId = quantityInput.dataset.attrId;
    const priceFormula = quantityInput.dataset.priceFormula;
    const quantity = parseInt(quantityInput.value) || 1;
    const basePrice = {{ product.base_price|tojson }};
    
    if (!group || !priceFormula) return;
    
    // 计算公式价格
    try {
        // 安全的公式计算环境
        const safeEnv = {
            quantity: quantity,
            base_price: basePrice,
            abs: Math.abs,
            max: Math.max,
            min: Math.min,
            round: Math.round
        };
        
        // 使用Function构造器计算公式
        const calculate = new Function('env', `
            with(env) {
                return ${priceFormula};
            }
        `);
        
        const result = calculate(safeEnv);
        const price = Math.max(0, parseFloat(result));
        
        // 更新价格显示
        const priceDisplayElement = document.getElementById(`formulaPrice_${group.replace(/\s+/g, '_')}`);
        if (priceDisplayElement) {
            priceDisplayElement.textContent = `¥${price.toFixed(5)}`;
        }
        
        // 更新全局价格计算
        updatePrice();
        
    } catch (error) {
        console.error('公式计算错误:', error);
        const priceDisplayElement = document.getElementById(`formulaPrice_${group.replace(/\s+/g, '_')}`);
        if (priceDisplayElement) {
            priceDisplayElement.textContent = '计算错误';
        }
    }
}

// 收集属性自定义数量和自定义值
function getAttributeQuantities() {
    const attributeQuantities = {};

    // 收集公式数量输入
    document.querySelectorAll('.formula-quantity-input').forEach(input => {
        const attrId = input.dataset.attrId;
        const quantity = parseInt(input.value);

        if (attrId && quantity) {
            attributeQuantities[attrId] = quantity;
        }
    });

    // 收集自定义输入值 - 不依赖于selected类，直接检查有值的输入
    document.querySelectorAll('.custom-attribute-input').forEach(input => {
        const attrId = input.dataset.id;
        const customValue = input.dataset.customValue || input.value.trim();

        if (attrId && customValue) {
            // 对于自定义输入，如果是数字，存储为数量；否则存储为字符串值
            const numericValue = parseFloat(customValue);
            if (!isNaN(numericValue) && isFinite(numericValue)) {
                attributeQuantities[attrId] = numericValue;
            } else {
                // 非数字值存储为特殊标记
                attributeQuantities[attrId] = customValue;
            }
            console.log(`收集自定义属性输入 - 属性ID: ${attrId}, 值: ${customValue}`);
        }
    });

    // 收集属性组自定义输入值 - 不依赖于selected类，直接检查有值的输入
    document.querySelectorAll('.custom-input-field').forEach(input => {
        const group = input.dataset.group;
        const customValue = input.dataset.customValue || input.value.trim();

        if (group && customValue) {
            // 使用特殊的键名存储属性组自定义值
            const customKey = `custom_group_${group.replace(/\s+/g, '_')}`;

            // 尝试解析为数字
            const numericValue = parseFloat(customValue);
            if (!isNaN(numericValue) && isFinite(numericValue)) {
                attributeQuantities[customKey] = numericValue;
            } else {
                attributeQuantities[customKey] = customValue;
            }
            console.log(`收集属性组自定义输入 - 组: ${group}, 键: ${customKey}, 值: ${customValue}`);

            // 确保自定义属性组被标记为选中
            const customAttrId = `custom_${group.replace(/\s+/g, '_')}`;
            if (!selectedAttributes.includes(customAttrId)) {
                selectedAttributes.push(customAttrId);
                console.log(`自动添加自定义属性组到选择列表: ${customAttrId}`);
            }
        }
    });

    return attributeQuantities;
}

// 验证自定义输入数据
function validateCustomInputs() {
    let isValid = true;
    const errors = [];

    // 验证所有自定义输入字段
    document.querySelectorAll('.custom-input-field').forEach(input => {
        const group = input.dataset.group;
        const value = input.value.trim();

        if (value) {
            // 检查是否有验证规则
            const validationPattern = input.dataset.validationPattern ||
                                    input.closest('.custom-input-wrapper').querySelector('select')?.dataset.customValidation;
            const validationMessage = input.dataset.validationMessage ||
                                    input.closest('.custom-input-wrapper').querySelector('select')?.dataset.customMessage;

            if (validationPattern && value) {
                try {
                    const regex = new RegExp(validationPattern);
                    if (!regex.test(value)) {
                        isValid = false;
                        errors.push(`${group}: ${validationMessage || '输入格式不正确'}`);
                        input.classList.add('is-invalid');
                    } else {
                        input.classList.remove('is-invalid');
                        input.classList.add('is-valid');
                    }
                } catch (e) {
                    console.warn(`验证正则表达式错误: ${validationPattern}`, e);
                }
            }

            // 数字类型验证
            if (input.type === 'number') {
                const numValue = parseFloat(value);
                if (isNaN(numValue)) {
                    isValid = false;
                    errors.push(`${group}: 请输入有效的数字`);
                    input.classList.add('is-invalid');
                } else {
                    input.classList.remove('is-invalid');
                    input.classList.add('is-valid');
                }
            }
        }
    });

    if (!isValid) {
        alert('自定义输入验证失败：\n' + errors.join('\n'));
    }

    return isValid;
}

// Image gallery
function changeMainImage(src, thumbnailElement) {
    const mainImage = document.getElementById('mainProductImage');
    
    if (!mainImage) return;
    
    // Fade out effect
    mainImage.style.opacity = '0.5';
    
    setTimeout(() => {
        mainImage.src = src;
        mainImage.style.opacity = '1';
    }, 150);

    // Update thumbnail active state
    document.querySelectorAll('.thumbnail-item').forEach(item => {
        item.classList.remove('active');
    });
    if (thumbnailElement) {
        thumbnailElement.classList.add('active');
    }
}

function openImageModal(src) {
    if (window.ModernUI && window.ModernUI.modal) {
        const modal = window.ModernUI.modal.create({
            title: '商品大图',
            content: `<div class="text-center"><img src="${src}" class="img-fluid rounded" alt="商品大图"></div>`,
            closable: true,
            showFooter: false,
            size: 'xl'
        });
        
        window.ModernUI.modal.open(modal);
    }
}

// Share product
function shareProduct() {
    if (navigator.share) {
        navigator.share({
            title: document.title,
            url: window.location.href
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(window.location.href).then(() => {
            if (window.ModernUI && window.ModernUI.notification) {
                window.ModernUI.notification.success('链接已复制到剪贴板');
            } else if (window.ModernNotification) {
                window.ModernNotification.success('链接已复制到剪贴板');
            }
        });
    }
}

// Wishlist toggle
function toggleWishlist(productId) {
    const icon = document.getElementById('wishlistIcon');
    if (!icon) return;
    
    if (icon.classList.contains('far')) {
        icon.classList.remove('far');
        icon.classList.add('fas');
        icon.style.color = '#ef4444';
        if (window.ModernUI && window.ModernUI.notification) {
            window.ModernUI.notification.success('已添加到收藏夹');
        } else if (window.ModernNotification) {
            window.ModernNotification.success('已添加到收藏夹');
        }
    } else {
        icon.classList.remove('fas');
        icon.classList.add('far');
        icon.style.color = '';
        if (window.ModernUI && window.ModernUI.notification) {
            window.ModernUI.notification.info('已从收藏夹移除');
        } else if (window.ModernNotification) {
            window.ModernNotification.info('已从收藏夹移除');
        }
    }
}

// Scroll animations
function initializeScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animated');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
    });
}

function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// Price calculation - 支持公式计算和自定义数量
function updatePrice() {
    const attributeQuantities = getAttributeQuantities();
    
    const requestData = {
        product_id: window.productId,
        quantity: currentQuantity,
        selected_attributes: selectedAttributes,
        attribute_quantities: attributeQuantities
    };
    
    console.log('价格计算请求数据:', requestData);
    
    // 获取CSRF token
    const csrfToken = document.querySelector('meta[name=csrf-token]') 
        ? document.querySelector('meta[name=csrf-token]').getAttribute('content')
        : document.querySelector('input[name=csrf_token]')?.value;
    
    const headers = {
        'Content-Type': 'application/json'
    };
    
    // 添加CSRF token
    if (csrfToken) {
        headers['X-CSRFToken'] = csrfToken;
    }
    
    fetch('/api/calculate-price', {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(requestData)
    })
    .then(response => {
        console.log('价格计算响应状态:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('价格计算响应数据:', data);
        if (data.error) {
            console.error('价格计算错误:', data.error);
            // 显示基础价格
            const priceValueElement = document.getElementById('priceValue');
            if (priceValueElement) {
                priceValueElement.textContent = ({{ product.base_price|tojson }} * currentQuantity).toFixed(5);
            }
        } else {
            // 更新显示的价格
            const priceValueElement = document.getElementById('priceValue');
            if (priceValueElement) {
                priceValueElement.textContent = parseFloat(data.total_price).toFixed(5);
                // 添加动画效果
                priceValueElement.classList.add('animate-price');
                setTimeout(() => {
                    priceValueElement.classList.remove('animate-price');
                }, 500);
            }
            
            // 显示折扣信息（如果有）
            const discountInfoElement = document.getElementById('discountInfo');
            if (data.discount && discountInfoElement) {
                discountInfoElement.innerHTML = `
                    <div class="d-flex align-items-center gap-2">
                        <i class="fas fa-tags text-success"></i>
                        <span class="text-success fw-bold">${data.discount.display}</span>
                    </div>
                `;
                discountInfoElement.classList.add('show');
            } else if (discountInfoElement) {
                discountInfoElement.classList.remove('show');
            }
            
            // 显示属性计算详情（如果有）
            if (data.attribute_details && data.attribute_details.length > 0) {
                console.log('属性计算详情:', data.attribute_details);
            }
        }
    })
    .catch(error => {
        console.error('价格计算网络错误:', error);
        // 显示基础价格作为后备
        const priceValueElement = document.getElementById('priceValue');
        if (priceValueElement) {
            priceValueElement.textContent = ({{ product.base_price|tojson }} * currentQuantity).toFixed(5);
        }
    });
}

// 直接发起支付
function initiatePayment(orderId) {
    // 默认使用支付宝支付
    const paymentType = 'alipay';
    
    // 获取CSRF令牌
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || 
                      document.querySelector('input[name="csrf_token"]')?.value || 
                      '{{ csrf_token() }}';
    
    // 发起支付请求
    fetch(`/api/orders/${orderId}/pay`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            payment_type: paymentType
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.payment_url) {
            // 跳转到支付页面
            window.location.href = data.payment_url;
        } else {
            // 支付请求失败，跳转到订单详情页
            showNotification('warning', data.message || '支付请求失败，请在订单页面重新支付');
            window.location.href = `/order/${orderId}`;
        }
    })
    .catch(error => {
        console.error('支付请求失败:', error);
        showNotification('error', '网络错误，请在订单页面重新支付');
        window.location.href = `/order/${orderId}`;
    });
}
</script>
{% endblock %}
