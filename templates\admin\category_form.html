{% extends "admin/base.html" %}

{% block title %}{{ title }} - 管理后台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ title }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('admin.categories') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>返回列表
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.name.label(class="form-label") }}
                        {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                        {% if form.name.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.name.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control", rows="3") }}
                        {% if form.description.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.description.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.parent_id.label(class="form-label") }}
                                {{ form.parent_id(class="form-select") }}
                                {% if form.parent_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.parent_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.sort_order.label(class="form-label") }}
                                {{ form.sort_order(class="form-control" + (" is-invalid" if form.sort_order.errors else "")) }}
                                {% if form.sort_order.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.sort_order.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">数值越小排序越靠前</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 分类管理说明 -->
                    <div class="mb-3">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>提示：</strong>创建分类后，您可以为该分类添加专属的大属性组，每个大属性组下可以包含多个小属性选项。
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_active(class="form-check-input") }}
                            {{ form.is_active.label(class="form-check-label") }}
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('admin.categories') }}" class="btn btn-secondary">取消</a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">操作说明</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li><i class="fas fa-info-circle text-info me-2"></i>分类名称为必填项</li>
                    <li><i class="fas fa-info-circle text-info me-2"></i>可以设置上级分类创建层级结构</li>
                    <li><i class="fas fa-info-circle text-info me-2"></i>排序值越小显示越靠前</li>
                    <li><i class="fas fa-info-circle text-info me-2"></i>禁用的分类不会在前台显示</li>
                </ul>
            </div>
        </div>
        
        {% if category and category.id %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">分类信息</h6>
            </div>
            <div class="card-body">
                <p class="small mb-1"><strong>创建时间：</strong>{{ category.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                <p class="small mb-1"><strong>更新时间：</strong>{{ category.updated_at.strftime('%Y-%m-%d %H:%M') }}</p>
                <p class="small mb-1"><strong>商品数量：</strong>{{ category.products.count() }}</p>
                <p class="small mb-0"><strong>大属性组数量：</strong>{{ category.get_attribute_groups_count() }}</p>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
