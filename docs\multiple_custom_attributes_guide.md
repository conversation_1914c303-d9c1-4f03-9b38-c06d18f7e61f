# 多个自定义属性完整指南

## 🎯 概述

本指南详细说明如何确保系统能够正确处理多个不同的自定义属性组，包括数据收集、存储、计算和显示的完整流程。

## 🔧 系统架构

### 1. 数据库设计

#### AttributeGroup 表（属性组）
```sql
-- 支持自定义输入的关键字段
allow_custom_input BOOLEAN DEFAULT FALSE,
custom_input_type VARCHAR(20),           -- text, number, textarea, select
custom_input_label VARCHAR(100),         -- 自定义输入标签
custom_input_placeholder VARCHAR(200),   -- 占位符文本
custom_validation_pattern TEXT,          -- 验证正则表达式
custom_validation_message VARCHAR(200),  -- 验证错误消息
custom_price_formula TEXT,               -- 价格计算公式
custom_price_modifier DECIMAL(10,2),     -- 价格调整值
custom_price_modifier_type VARCHAR(20)   -- fixed, percentage, formula
```

#### OrderItem 表（订单项）
```sql
-- 存储所有属性数据（包括自定义）
product_attributes JSON  -- 格式：{"属性组名": "属性值 (自定义)"}
```

### 2. 前端数据收集

#### JavaScript 收集逻辑
```javascript
function getAttributeQuantities() {
    const attributeQuantities = {};
    
    // 收集属性组自定义输入值
    document.querySelectorAll('.custom-input-field').forEach(input => {
        const group = input.dataset.group;
        const customValue = input.value.trim();
        
        if (group && customValue) {
            // 使用特殊键名存储
            const customKey = `custom_group_${group.replace(/\s+/g, '_')}`;
            attributeQuantities[customKey] = customValue;
            
            // 自动添加到选择列表
            const customAttrId = `custom_${group.replace(/\s+/g, '_')}`;
            if (!selectedAttributes.includes(customAttrId)) {
                selectedAttributes.push(customAttrId);
            }
        }
    });
    
    return attributeQuantities;
}
```

### 3. 后端处理逻辑

#### 订单创建时的数据处理
```python
# 在 create_order 函数中
product_attributes = OrderedDict()

# 处理自定义属性组
for custom_attr_id in custom_groups:
    group_key = custom_attr_id.replace('custom_', '')
    custom_value_key = f'custom_group_{group_key}'
    custom_value = attribute_quantities.get(custom_value_key)
    
    if custom_value:
        group_name = group_key.replace('_', ' ')
        group = AttributeGroup.query.filter_by(name=group_name).first()
        
        if group and group.allow_custom_input:
            display_value = f"{custom_value} (自定义)"
            product_attributes[group_name] = display_value
```

## 📝 配置步骤

### 1. 创建自定义属性组

在后台管理中为每个需要自定义输入的属性组配置：

```python
# 示例：尺寸属性组
size_group = AttributeGroup(
    name='尺寸',
    allow_custom_input=True,
    custom_input_type='text',
    custom_input_label='自定义尺寸',
    custom_input_placeholder='请输入尺寸（如：300×400mm）',
    custom_validation_pattern=r'^.{1,50}$',
    custom_validation_message='请输入有效的尺寸',
    custom_price_formula='base_price * 0.1',
    custom_price_modifier_type='formula'
)

# 示例：材质属性组
material_group = AttributeGroup(
    name='材质',
    allow_custom_input=True,
    custom_input_type='text',
    custom_input_label='自定义材质',
    custom_input_placeholder='请输入材质要求',
    custom_price_modifier=20.0,
    custom_price_modifier_type='fixed'
)
```

### 2. 前端模板配置

在商品详情页面为每个属性组添加自定义输入选项：

```html
<!-- 尺寸属性组 -->
<div class="attribute-group">
    <label>尺寸规格</label>
    <select onchange="handleAttributeChange(this)">
        <option value="">请选择</option>
        <option value="A4">A4</option>
        <option value="custom">自定义尺寸</option>
    </select>
    <input type="text" class="custom-input-field" 
           data-group="尺寸" 
           placeholder="请输入自定义尺寸"
           style="display: none;">
</div>

<!-- 材质属性组 -->
<div class="attribute-group">
    <label>材质规格</label>
    <select onchange="handleAttributeChange(this)">
        <option value="">请选择</option>
        <option value="铜版纸">铜版纸</option>
        <option value="custom">自定义材质</option>
    </select>
    <input type="text" class="custom-input-field" 
           data-group="材质" 
           placeholder="请输入材质要求"
           style="display: none;">
</div>
```

## 🧪 测试验证

### 1. 使用诊断脚本
```bash
python scripts/diagnose_custom_attributes.py
```

### 2. 创建测试订单
```bash
python scripts/create_test_order_with_custom_attributes.py
```

### 3. 访问测试页面
- 多属性测试：`/test/multiple-custom-attributes`
- 功能测试：`/test/custom-attributes`
- 显示修复验证：`/test/display-fix`

## 📊 数据流程

### 1. 用户输入阶段
```
用户选择 "自定义尺寸" → 输入 "350×500mm"
用户选择 "自定义材质" → 输入 "特殊防水纸"
```

### 2. 前端收集阶段
```javascript
selectedAttributes = ['custom_尺寸', 'custom_材质']
attributeQuantities = {
    'custom_group_尺寸': '350×500mm',
    'custom_group_材质': '特殊防水纸'
}
```

### 3. 后端处理阶段
```python
product_attributes = {
    '尺寸': '350×500mm (自定义)',
    '材质': '特殊防水纸 (自定义)'
}
```

### 4. 数据库存储
```json
{
    "尺寸": "350×500mm (自定义)",
    "材质": "特殊防水纸 (自定义)"
}
```

### 5. 前台显示
```html
<span class="badge bg-warning">
    <i class="fas fa-edit"></i>尺寸: 350×500mm
</span>
<span class="badge bg-warning">
    <i class="fas fa-edit"></i>材质: 特殊防水纸
</span>
```

## 🔍 故障排除

### 问题1：自定义数据没有保存
**检查项：**
- 属性组是否设置了 `allow_custom_input=True`
- 前端是否正确收集了 `custom_group_*` 键值
- 后端是否正确处理了自定义属性组

### 问题2：自定义数据没有显示
**检查项：**
- `OrderItem.product_attributes` 是否包含 "(自定义)" 标记
- `has_custom_attributes()` 方法是否正确识别
- 模板是否正确调用了 `get_attributes_detail()`

### 问题3：价格计算不正确
**检查项：**
- 属性组的价格计算配置是否正确
- 公式语法是否正确（如：`numeric_value * 0.1`）
- 是否正确传递了自定义数值

## 📋 最佳实践

### 1. 属性组命名
- 使用简洁明确的中文名称
- 避免特殊字符和空格过多
- 保持命名一致性

### 2. 验证规则
- 为每个自定义输入设置合适的验证规则
- 提供清晰的错误提示信息
- 考虑用户体验和输入便利性

### 3. 价格计算
- 根据业务需求选择合适的计算方式
- 测试各种边界情况
- 确保计算结果的准确性

### 4. 显示优化
- 使用醒目的视觉标识区分自定义属性
- 提供详细的属性信息展示
- 确保在不同设备上的显示效果

## 🚀 扩展功能

### 1. 批量自定义
支持用户一次性设置多个相关的自定义属性

### 2. 模板保存
允许用户保存常用的自定义配置为模板

### 3. 历史记录
记录用户的自定义输入历史，便于重复使用

### 4. 智能推荐
基于历史数据推荐相关的自定义选项

---

通过以上完整的配置和测试，系统能够正确处理多个不同的自定义属性组，确保所有自定义数据都能正确存储到数据库并在前台和后台清晰显示。
