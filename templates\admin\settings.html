{% extends "admin/base.html" %}

{% block title %}系统设置 - 管理后台{% endblock %}

{% block extra_css %}
<style>
.settings-card {
    margin-bottom: 1.5rem;
    border: 1px solid #dee2e6;
    box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
}

.settings-card .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 1.25rem;
}

.settings-card .card-header h5 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.setting-item {
    padding: 1rem 0;
    border-bottom: 1px solid #f1f1f1;
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #495057;
}

.setting-description {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.setting-input-group {
    position: relative;
}

.setting-input-group .form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

.save-indicator {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.875rem;
}

.save-indicator.saving {
    color: #ffc107;
}

.save-indicator.saved {
    color: #28a745;
}

.save-indicator.error {
    color: #dc3545;
}

.required-field::after {
    content: " *";
    color: #dc3545;
}

.invalid-feedback {
    display: block;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">系统设置</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-outline-primary me-2" onclick="exportSettings()">
            <i class="fas fa-download me-2"></i>导出配置
        </button>
    </div>
</div>

<!-- 成功/错误提示 -->
{% with messages = get_flashed_messages(with_categories=true) %}
{% if messages %}
<div class="row">
    <div class="col-12">
        {% for category, message in messages %}
        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endfor %}
    </div>
</div>
{% endif %}
{% endwith %}

<div class="row">
    <div class="col-lg-8">
        {% for group_name, icon_class, color_class in group_order %}
        {% if group_name in grouped_configs %}
        <div class="card settings-card">
            <div class="card-header">
                <h5 class="text-{{ color_class }}">
                    <i class="{{ icon_class }} me-2"></i>{{ group_name }}
                </h5>
            </div>
            <div class="card-body">
                {% for config in grouped_configs[group_name] %}
                <div class="setting-item" data-config-key="{{ config.config_key }}">
                    <div class="setting-label">
                        {{ config.description or config.config_key }}
                        {% if config.is_required %}
                        <span class="required-field"></span>
                        {% endif %}
                    </div>
                    
                    <div class="setting-input-group">
                        {% if config.config_type == 'boolean' %}
                        <div class="form-check form-switch">
                            <input class="form-check-input config-input" 
                                   type="checkbox" 
                                   id="{{ config.config_key }}"
                                   data-config-key="{{ config.config_key }}"
                                   data-config-type="{{ config.config_type }}"
                                   {% if config.config_value|lower in ['true', '1', 'yes', 'on'] %}checked{% endif %}>
                            <label class="form-check-label" for="{{ config.config_key }}">
                                {{ '启用' if config.config_value|lower in ['true', '1', 'yes', 'on'] else '禁用' }}
                            </label>
                        </div>
                        {% elif config.config_type == 'number' %}
                        <input type="number" 
                               class="form-control config-input" 
                               id="{{ config.config_key }}"
                               data-config-key="{{ config.config_key }}"
                               data-config-type="{{ config.config_type }}"
                               value="{{ config.config_value }}"
                               {% if config.is_required %}required{% endif %}
                               {% if config.validation_rules %}
                                   {% if config.validation_rules.min is defined %}min="{{ config.validation_rules.min }}"{% endif %}
                                   {% if config.validation_rules.max is defined %}max="{{ config.validation_rules.max }}"{% endif %}
                               {% endif %}>
                        {% elif config.config_type == 'email' %}
                        <input type="email" 
                               class="form-control config-input" 
                               id="{{ config.config_key }}"
                               data-config-key="{{ config.config_key }}"
                               data-config-type="{{ config.config_type }}"
                               value="{{ config.config_value }}"
                               {% if config.is_required %}required{% endif %}>
                        {% elif config.config_type == 'url' %}
                        <input type="url" 
                               class="form-control config-input" 
                               id="{{ config.config_key }}"
                               data-config-key="{{ config.config_key }}"
                               data-config-type="{{ config.config_type }}"
                               value="{{ config.config_value }}"
                               {% if config.is_required %}required{% endif %}>
                        {% else %}
                        <input type="text" 
                               class="form-control config-input" 
                               id="{{ config.config_key }}"
                               data-config-key="{{ config.config_key }}"
                               data-config-type="{{ config.config_type }}"
                               value="{{ config.config_value }}"
                               {% if config.is_required %}required{% endif %}>
                        {% endif %}
                        
                        <span class="save-indicator"></span>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        {% endfor %}
    </div>
    
    <div class="col-lg-4">
        <!-- 帮助信息 -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>使用说明
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        修改配置后会自动保存
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-asterisk text-danger me-2"></i>
                        带红色星号的为必填项
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-save text-primary me-2"></i>
                        保存成功后会显示绿色勾号
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        输入不合法时会显示错误提示
                    </li>
                </ul>
                
                <hr>
                
                <h6 class="mb-3">配置项说明</h6>
                <dl class="small">
                    <dt>基本信息</dt>
                    <dd>网站和公司的基本信息，会显示在前台页面</dd>
                    
                    <dt>运费设置</dt>
                    <dd>配置订单运费相关的规则</dd>
                    
                    <dt>订单设置</dt>
                    <dd>订单处理相关的业务规则</dd>
                    
                    <dt>系统限制</dt>
                    <dd>系统性能和安全相关的限制参数</dd>
                </dl>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 保存定时器
    let saveTimers = {};
    
    // 监听所有配置输入框的变化
    $('.config-input').on('input change', function() {
        const $input = $(this);
        const configKey = $input.data('config-key');
        const configType = $input.data('config-type');
        const $settingItem = $input.closest('.setting-item');
        const $indicator = $settingItem.find('.save-indicator');
        const $feedback = $settingItem.find('.invalid-feedback');
        
        // 清除之前的定时器
        if (saveTimers[configKey]) {
            clearTimeout(saveTimers[configKey]);
        }
        
        // 清除错误提示
        $input.removeClass('is-invalid');
        $feedback.text('');
        
        // 显示保存中状态
        $indicator.removeClass('saved error').addClass('saving');
        $indicator.html('<i class="fas fa-spinner fa-spin"></i> 保存中...');
        
        // 延迟保存（防抖）
        saveTimers[configKey] = setTimeout(() => {
            saveConfig(configKey, $input, $indicator, $feedback);
        }, 800);
    });
    
    // 处理开关切换的标签更新
    $('.form-check-input.config-input').on('change', function() {
        const isChecked = $(this).prop('checked');
        $(this).next('label').text(isChecked ? '启用' : '禁用');
    });
    
    // 保存配置函数
    function saveConfig(configKey, $input, $indicator, $feedback) {
        let value;
        
        // 根据类型获取值
        if ($input.attr('type') === 'checkbox') {
            value = $input.prop('checked') ? 'true' : 'false';
        } else {
            value = $input.val();
        }
        
        // 发送AJAX请求
        $.ajax({
            url: `/admin/api/settings/${configKey}`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ value: value }),
            headers: {
                'X-CSRFToken': $('meta[name="csrf-token"]').attr('content') || 
                              $('#csrf_token').val() || ''
            },
            success: function(response) {
                $indicator.removeClass('saving error').addClass('saved');
                $indicator.html('<i class="fas fa-check"></i> 已保存');
                
                // 3秒后隐藏成功提示
                setTimeout(() => {
                    $indicator.fadeOut(() => {
                        $indicator.removeClass('saved').html('').show();
                    });
                }, 3000);
            },
            error: function(xhr) {
                $indicator.removeClass('saving saved').addClass('error');
                $indicator.html('<i class="fas fa-times"></i> 保存失败');
                
                // 显示错误信息
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    $input.addClass('is-invalid');
                    $feedback.text(xhr.responseJSON.message);
                }
            }
        });
    }
});

// 导出配置功能
function exportSettings() {
    // 显示加载状态
    const $btn = $('button[onclick="exportSettings()"]');
    const originalText = $btn.html();
    $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>导出中...');
    
    // 发送导出请求
    fetch('/admin/api/settings/export', {
        method: 'GET',
        headers: {
            'X-CSRFToken': $('meta[name="csrf-token"]').attr('content') || 
                          $('#csrf_token').val() || ''
        }
    })
    .then(response => {
        if (response.ok) {
            return response.blob();
        }
        throw new Error('导出失败');
    })
    .then(blob => {
        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        
        // 生成文件名
        const now = new Date();
        const timestamp = now.getFullYear() + 
                         ('0' + (now.getMonth() + 1)).slice(-2) + 
                         ('0' + now.getDate()).slice(-2) + '_' +
                         ('0' + now.getHours()).slice(-2) + 
                         ('0' + now.getMinutes()).slice(-2) + 
                         ('0' + now.getSeconds()).slice(-2);
        a.download = `system_configs_${timestamp}.json`;
        
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        // 显示成功消息
        showAlert('success', '配置导出成功！');
    })
    .catch(error => {
        console.error('导出失败:', error);
        showAlert('danger', '配置导出失败，请重试');
    })
    .finally(() => {
        // 恢复按钮状态
        $btn.prop('disabled', false).html(originalText);
    });
}

// 显示提示消息
function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // 在页面顶部显示提示
    const $container = $('.row').first();
    $container.before(alertHtml);
    
    // 3秒后自动消失
    setTimeout(() => {
        $('.alert').alert('close');
    }, 3000);
}
</script>
{% endblock %}
