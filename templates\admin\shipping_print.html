<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发货单 - {{ order.order_no }}</title>
    <style>
        /* 打印专用样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: white;
        }

        .print-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 15mm;
            background: white;
        }

        /* 公司标头 */
        .company-header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .company-info {
            font-size: 11px;
            color: #666;
        }

        /* 发货单标题 */
        .shipping-title {
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            margin: 30px 0;
            color: #28a745;
            background-color: #f8fff9;
            padding: 10px;
            border: 2px solid #28a745;
        }

        /* 信息区域 */
        .info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }

        .info-block {
            width: 48%;
            border: 1px solid #ddd;
            padding: 15px;
            background-color: #f8f9fa;
        }

        .info-block h3 {
            font-size: 14px;
            font-weight: bold;
            border-bottom: 2px solid #28a745;
            padding-bottom: 5px;
            margin-bottom: 10px;
            color: #28a745;
        }

        .info-row {
            display: flex;
            margin-bottom: 8px;
        }

        .info-label {
            font-weight: bold;
            width: 90px;
            color: #555;
        }

        .info-value {
            flex: 1;
        }

        /* 重要信息高亮 */
        .highlight-value {
            background-color: #fff3cd;
            padding: 2px 5px;
            border-left: 3px solid #ffc107;
            font-weight: bold;
        }

        /* 发货清单表格 */
        .shipping-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            font-size: 11px;
        }

        .shipping-table th,
        .shipping-table td {
            border: 2px solid #333;
            padding: 8px;
            text-align: left;
        }

        .shipping-table th {
            background-color: #28a745;
            color: white;
            font-weight: bold;
        }

        .shipping-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .quantity-highlight {
            font-size: 14px;
            font-weight: bold;
            color: #28a745;
            text-align: center;
        }

        /* 物流信息区域 */
        .logistics-info {
            margin-top: 30px;
            border: 2px solid #17a2b8;
            padding: 20px;
            background-color: #f0fcff;
        }

        .logistics-title {
            font-size: 16px;
            font-weight: bold;
            color: #17a2b8;
            margin-bottom: 15px;
            text-align: center;
        }

        .logistics-row {
            display: flex;
            margin-bottom: 12px;
            align-items: center;
        }

        .logistics-label {
            font-weight: bold;
            width: 120px;
            color: #17a2b8;
        }

        .logistics-value {
            flex: 1;
            padding: 5px;
            border-bottom: 1px solid #17a2b8;
        }

        /* 发货确认清单 */
        .shipping-checklist {
            margin-top: 30px;
            border: 2px solid #fd7e14;
            padding: 15px;
            background-color: #fff7e6;
        }

        .checklist-title {
            font-weight: bold;
            color: #fd7e14;
            margin-bottom: 10px;
            text-align: center;
            font-size: 14px;
        }

        .check-item {
            display: flex;
            margin-bottom: 8px;
            align-items: center;
        }

        .check-box {
            width: 15px;
            height: 15px;
            border: 2px solid #fd7e14;
            margin-right: 10px;
            display: inline-block;
        }

        /* 汇总信息 */
        .summary-info {
            margin-top: 30px;
            border: 2px solid #6f42c1;
            padding: 15px;
            background-color: #f8f7ff;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 13px;
        }

        .summary-row.total {
            font-size: 16px;
            font-weight: bold;
            color: #6f42c1;
            border-top: 1px solid #6f42c1;
            padding-top: 8px;
            margin-top: 10px;
        }

        /* 签名区域 */
        .signature-area {
            display: flex;
            justify-content: space-around;
            margin-top: 40px;
            padding: 20px 0;
            border-top: 2px solid #333;
        }

        .signature-box {
            text-align: center;
            width: 30%;
        }

        .signature-line {
            border-bottom: 1px solid #333;
            margin: 20px 0 5px 0;
            height: 30px;
        }

        /* 页脚 */
        .print-footer {
            margin-top: 50px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }

        /* 打印样式 */
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            .print-container {
                margin: 0;
                padding: 0;
                max-width: none;
            }
            
            .no-print {
                display: none !important;
            }
            
            @page {
                margin: 15mm;
                size: A4;
            }
        }

        /* 屏幕预览样式 */
        @media screen {
            body {
                background-color: #f5f5f5;
                padding: 20px;
            }
            
            .print-container {
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
            }
            
            .print-actions {
                text-align: center;
                margin-bottom: 20px;
            }
            
            .btn {
                display: inline-block;
                padding: 10px 20px;
                margin: 0 10px;
                background-color: #007bff;
                color: white;
                text-decoration: none;
                border-radius: 5px;
                border: none;
                cursor: pointer;
                font-size: 14px;
            }
            
            .btn:hover {
                background-color: #0056b3;
            }
            
            .btn-secondary {
                background-color: #6c757d;
            }
            
            .btn-secondary:hover {
                background-color: #545b62;
            }
        }
    </style>
</head>
<body>
    <!-- 打印操作按钮（仅屏幕显示） -->
    <div class="print-actions no-print">
        <button class="btn" onclick="window.print()">打印发货单</button>
        <a href="{{ url_for('admin.order_detail', id=order.id) }}" class="btn btn-secondary">返回详情</a>
    </div>

    <div class="print-container">
        <!-- 公司标头 -->
        <div class="company-header">
            <div class="company-name">{{ site_config.company_name or '三联纸印刷有限公司' }}</div>
            <div class="company-info">
                电话：{{ site_config.company_phone or '************' }} | 
                地址：{{ site_config.company_address or '广东省深圳市南山区' }}
            </div>
        </div>

        <!-- 发货单标题 -->
        <div class="shipping-title">发货清单</div>

        <!-- 基本信息和收货信息 -->
        <div class="info-section">
            <div class="info-block">
                <h3>订单信息</h3>
                <div class="info-row">
                    <span class="info-label">订单号：</span>
                    <span class="info-value highlight-value">{{ order.order_no }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">下单时间：</span>
                    <span class="info-value">{{ order.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">发货时间：</span>
                    <span class="info-value highlight-value"><span id="shipping-time"></span></span>
                </div>
                <div class="info-row">
                    <span class="info-label">订单状态：</span>
                    <span class="info-value">{{ order.get_status_display() }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">客户姓名：</span>
                    <span class="info-value">{{ order.user.real_name or order.user.username }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">联系电话：</span>
                    <span class="info-value">{{ order.user.phone or '未提供' }}</span>
                </div>
            </div>

            <div class="info-block">
                <h3>收货信息</h3>
                {% if order.shipping_address %}
                <div class="info-row">
                    <span class="info-label">收货人：</span>
                    <span class="info-value highlight-value">{{ order.shipping_name or order.user.real_name or order.user.username }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">收货电话：</span>
                    <span class="info-value highlight-value">{{ order.shipping_phone or order.user.phone or '未提供' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">收货地址：</span>
                    <span class="info-value highlight-value">{{ order.shipping_address }}</span>
                </div>
                {% else %}
                <div class="info-row">
                    <span class="info-label">收货人：</span>
                    <span class="info-value highlight-value">{{ order.user.real_name or order.user.username }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">联系电话：</span>
                    <span class="info-value highlight-value">{{ order.user.phone or '未提供' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">收货地址：</span>
                    <span class="info-value">请客户自提或现场确认地址</span>
                </div>
                {% endif %}
                <div class="info-row">
                    <span class="info-label">备注说明：</span>
                    <span class="info-value">{{ order.notes or '无特殊说明' }}</span>
                </div>
            </div>
        </div>

        <!-- 发货清单表格 -->
        <table class="shipping-table">
            <thead>
                <tr>
                    <th style="width: 40px;">序号</th>
                    <th style="width: 200px;">商品名称</th>
                    <th style="width: 120px;">规格型号</th>
                    <th style="width: 60px;">数量</th>
                    <th style="width: 80px;">单价</th>
                    <th style="width: 80px;">小计</th>
                    <th style="width: 80px;">发货确认</th>
                </tr>
            </thead>
            <tbody>
                {% for item in order.order_items %}
                <tr>
                    <td style="text-align: center; font-weight: bold;">{{ loop.index }}</td>
                    <td>
                        <div style="font-weight: bold; margin-bottom: 3px;">{{ item.product_name }}</div>
                        {% if item.product %}
                        <div style="font-size: 9px; color: #666;">
                            商品编号：{{ item.product.id }}
                        </div>
                        {% endif %}
                    </td>
                    <td style="font-size: 10px;">
                        {% if item.get_attributes_display() %}
                        {{ item.get_attributes_display() }}
                        {% else %}
                        标准规格
                        {% endif %}
                    </td>
                    <td class="quantity-highlight">
                        {{ item.quantity }}
                    </td>
                    <td style="text-align: right;">¥{{ "%.2f"|format(item.unit_price) }}</td>
                    <td style="text-align: right; font-weight: bold;">¥{{ "%.2f"|format(item.total_price) }}</td>
                    <td style="text-align: center;">
                        <div style="height: 25px; border-bottom: 1px solid #ccc; margin: 3px 0;"></div>
                        <div style="font-size: 8px;">确认签名</div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- 物流信息 -->
        <div class="logistics-info">
            <div class="logistics-title">物流配送信息</div>
            <div class="logistics-row">
                <span class="logistics-label">快递公司：</span>
                <span class="logistics-value"></span>
            </div>
            <div class="logistics-row">
                <span class="logistics-label">快递单号：</span>
                <span class="logistics-value"></span>
            </div>
            <div class="logistics-row">
                <span class="logistics-label">发货重量：</span>
                <span class="logistics-value">_____ KG</span>
            </div>
            <div class="logistics-row">
                <span class="logistics-label">包装箱数：</span>
                <span class="logistics-value">_____ 箱</span>
            </div>
            <div class="logistics-row">
                <span class="logistics-label">运费金额：</span>
                <span class="logistics-value">¥ _____ 元</span>
            </div>
            <div class="logistics-row">
                <span class="logistics-label">预计到达：</span>
                <span class="logistics-value">{{ expected_delivery_date.strftime('%Y-%m-%d') if expected_delivery_date else '___年___月___日' }}</span>
            </div>
        </div>

        <!-- 发货确认清单 -->
        <div class="shipping-checklist">
            <div class="checklist-title">发货确认清单</div>
            <div class="check-item">
                <span class="check-box"></span>
                <span>商品数量核对正确</span>
            </div>
            <div class="check-item">
                <span class="check-box"></span>
                <span>商品规格确认无误</span>
            </div>
            <div class="check-item">
                <span class="check-box"></span>
                <span>包装完整无损坏</span>
            </div>
            <div class="check-item">
                <span class="check-box"></span>
                <span>收货地址信息准确</span>
            </div>
            <div class="check-item">
                <span class="check-box"></span>
                <span>发货单据齐全</span>
            </div>
            <div class="check-item">
                <span class="check-box"></span>
                <span>客户联系电话确认</span>
            </div>
        </div>

        <!-- 金额汇总 -->
        <div class="summary-info">
            <div class="summary-row">
                <span>商品总额：</span>
                <span>¥{{ "%.2f"|format(order.total_amount) }}</span>
            </div>
            <div class="summary-row">
                <span>优惠金额：</span>
                <span style="color: #28a745;">-¥{{ "%.2f"|format(order.discount_amount) }}</span>
            </div>
            <div class="summary-row">
                <span>运费：</span>
                <span>¥0.00</span>
            </div>
            <div class="summary-row total">
                <span>实付金额：</span>
                <span>¥{{ "%.2f"|format(order.final_amount) }}</span>
            </div>
            <div class="summary-row">
                <span>发货总件数：</span>
                <span style="font-weight: bold; color: #28a745;">{{ order.order_items.all() | sum(attribute='quantity') }} 件</span>
            </div>
        </div>

        <!-- 签名区域 -->
        <div class="signature-area">
            <div class="signature-box">
                <div><strong>发货员</strong></div>
                <div class="signature-line"></div>
                <div>签名/日期</div>
            </div>
            <div class="signature-box">
                <div><strong>质检员</strong></div>
                <div class="signature-line"></div>
                <div>签名/日期</div>
            </div>
            <div class="signature-box">
                <div><strong>仓库主管</strong></div>
                <div class="signature-line"></div>
                <div>签名/日期</div>
            </div>
        </div>

        <!-- 页脚 -->
        <div class="print-footer">
            <div>发货单打印时间：<span id="print-time"></span></div>
            <div style="margin-top: 10px;">{{ site_config.company_name or '三联纸印刷有限公司' }} - 仓储物流部</div>
            <div style="margin-top: 5px; font-size: 9px;">
                注意：请仔细核对商品信息，确认无误后发货。如有疑问请及时联系客服。
            </div>
        </div>
    </div>

    <script>
        // 设置打印时间
        document.getElementById('print-time').textContent = new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        
        // 设置发货时间（当前时间）
        document.getElementById('shipping-time').textContent = new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
        
        // 自动打印功能（可选）
        function autoPrint() {
            window.print();
        }
        
        // 如果URL包含auto参数，自动打印
        if (window.location.search.includes('auto=1')) {
            setTimeout(autoPrint, 1000);
        }
    </script>
</body>
</html> 