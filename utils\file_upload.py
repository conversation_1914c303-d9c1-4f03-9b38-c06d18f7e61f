#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
文件上传工具函数
"""

import os
import uuid
from datetime import datetime
from werkzeug.utils import secure_filename
from flask import current_app
from PIL import Image


def allowed_file(filename):
    """检查文件类型是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']


def generate_filename(original_filename):
    """生成唯一的文件名"""
    # 获取文件扩展名
    ext = ''
    if '.' in original_filename:
        ext = '.' + original_filename.rsplit('.', 1)[1].lower()
    
    # 生成唯一文件名：时间戳 + UUID
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    unique_id = str(uuid.uuid4())[:8]
    return f"{timestamp}_{unique_id}{ext}"


def save_uploaded_file(file, subfolder='products'):
    """
    保存上传的文件
    
    Args:
        file: 上传的文件对象
        subfolder: 子文件夹名称
        
    Returns:
        tuple: (success, message, file_path)
    """
    if not file or file.filename == '':
        return False, '没有选择文件', None
    
    if not allowed_file(file.filename):
        return False, '不支持的文件类型', None
    
    try:
        # 生成安全的文件名
        original_filename = secure_filename(file.filename)
        new_filename = generate_filename(original_filename)
        
        # 创建上传目录
        upload_folder = os.path.join(current_app.config['UPLOAD_FOLDER'], subfolder)
        os.makedirs(upload_folder, exist_ok=True)
        
        # 保存文件
        file_path = os.path.join(upload_folder, new_filename)
        file.save(file_path)
        
        # 如果是图片，进行压缩处理
        if is_image_file(new_filename):
            compress_image(file_path)
        
        # 返回相对路径（用于数据库存储）
        relative_path = f"/static/uploads/{subfolder}/{new_filename}"
        return True, '文件上传成功', relative_path
        
    except Exception as e:
        return False, f'文件上传失败: {str(e)}', None


def is_image_file(filename):
    """检查是否为图片文件"""
    image_extensions = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in image_extensions


def compress_image(file_path, max_size=(800, 800), quality=85):
    """
    压缩图片
    
    Args:
        file_path: 图片文件路径
        max_size: 最大尺寸 (width, height)
        quality: 压缩质量 (1-100)
    """
    try:
        with Image.open(file_path) as img:
            # 转换为RGB模式（处理RGBA等格式）
            if img.mode in ('RGBA', 'LA', 'P'):
                # 创建白色背景
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                img = background
            elif img.mode != 'RGB':
                img = img.convert('RGB')
            
            # 计算新尺寸（保持比例）
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # 保存压缩后的图片
            img.save(file_path, 'JPEG', quality=quality, optimize=True)
            
    except Exception as e:
        print(f"图片压缩失败: {e}")


def delete_file(file_path):
    """
    删除文件
    
    Args:
        file_path: 文件路径（相对路径，如 /static/uploads/products/xxx.jpg）
        
    Returns:
        bool: 是否删除成功
    """
    try:
        if file_path and file_path.startswith('/static/uploads/'):
            # 转换为绝对路径
            abs_path = os.path.join(current_app.root_path, file_path[1:])  # 去掉开头的 /
            if os.path.exists(abs_path):
                os.remove(abs_path)
                return True
    except Exception as e:
        print(f"删除文件失败: {e}")
    return False


def get_file_size(file_path):
    """
    获取文件大小
    
    Args:
        file_path: 文件路径
        
    Returns:
        int: 文件大小（字节）
    """
    try:
        if file_path and file_path.startswith('/static/uploads/'):
            abs_path = os.path.join(current_app.root_path, file_path[1:])
            if os.path.exists(abs_path):
                return os.path.getsize(abs_path)
    except Exception:
        pass
    return 0


def format_file_size(size_bytes):
    """
    格式化文件大小显示
    
    Args:
        size_bytes: 文件大小（字节）
        
    Returns:
        str: 格式化后的大小字符串
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"
