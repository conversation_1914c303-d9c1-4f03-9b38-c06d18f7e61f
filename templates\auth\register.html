{% extends "base_modern.html" %}

{% block title %}用户注册 - {{ site_config.site_name }}{% endblock %}
{% block description %}注册新账户，享受专业的印刷服务{% endblock %}

{% block extra_css %}
<style>
    /* ========== Register Page Styles ========== */
    .auth-page {
        background: var(--gradient-light);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem 0;
        position: relative;
        overflow: hidden;
    }
    
    /* Background Shapes */
    .auth-background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 1;
    }
    
    .auth-shape {
        position: absolute;
        border-radius: 50%;
        background: var(--success-gradient);
        opacity: 0.05;
        animation: float 6s ease-in-out infinite;
    }
    
    .auth-shape-1 {
        width: 200px;
        height: 200px;
        top: 15%;
        right: 10%;
        animation-delay: 0s;
    }
    
    .auth-shape-2 {
        width: 150px;
        height: 150px;
        top: 70%;
        left: 15%;
        animation-delay: 2s;
    }
    
    .auth-shape-3 {
        width: 100px;
        height: 100px;
        bottom: 10%;
        right: 20%;
        animation-delay: 4s;
    }
    
    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }
    
    /* Auth Container */
    .auth-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-radius: var(--border-radius-2xl);
        padding: 3rem;
        box-shadow: var(--shadow-xl);
        border: 1px solid rgba(255, 255, 255, 0.3);
        width: 100%;
        max-width: 480px;
        position: relative;
        z-index: 10;
    }
    
    .auth-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: var(--success-gradient);
        border-radius: var(--border-radius-2xl) var(--border-radius-2xl) 0 0;
    }
    
    /* Header */
    .auth-header {
        text-align: center;
        margin-bottom: 2.5rem;
    }
    
    .auth-logo {
        background: var(--success-gradient);
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        color: white;
        font-size: 2rem;
        box-shadow: var(--shadow-lg);
    }
    
    .auth-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--gray-900);
        margin-bottom: 0.5rem;
    }
    
    .auth-subtitle {
        color: var(--gray-600);
        font-size: 1rem;
    }
    
    /* Form Styles */
    .auth-form {
        margin-bottom: 2rem;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        display: block;
        font-weight: 600;
        color: var(--gray-700);
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }
    
    .form-control {
        width: 100%;
        padding: 1rem 1.25rem;
        border: 2px solid rgba(16, 185, 129, 0.2);
        border-radius: var(--border-radius-lg);
        background: rgba(255, 255, 255, 0.9);
        font-size: 1rem;
        transition: var(--transition-base);
        position: relative;
        color: #333333 !important;
        font-weight: 500;
    }
    
    .form-control:focus {
        outline: none;
        border-color: var(--success-500);
        box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        background: white;
    }
    
    .form-control::placeholder {
        color: #666666 !important;
        opacity: 1;
    }
    
    /* Input with Icon */
    .input-group {
        position: relative;
    }
    
    .input-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--gray-400);
        font-size: 1rem;
        z-index: 5;
    }
    
    .input-group .form-control {
        padding-left: 3rem;
    }
    
    /* Submit Button */
    .btn-submit {
        width: 100%;
        background: var(--success-gradient);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: var(--border-radius-lg);
        font-size: 1.125rem;
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition-base);
        box-shadow: var(--shadow-md);
        position: relative;
        overflow: hidden;
    }
    
    .btn-submit::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        transition: var(--transition-base);
    }
    
    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
    }
    
    .btn-submit:hover::before {
        left: 100%;
    }
    
    .btn-submit:active {
        transform: translateY(0);
    }
    
    /* Loading State */
    .btn-submit.loading {
        pointer-events: none;
        opacity: 0.8;
    }
    
    .btn-submit .loading-spinner {
        display: none;
    }
    
    .btn-submit.loading .loading-spinner {
        display: inline-block;
        margin-right: 0.5rem;
    }
    
    /* Links */
    .auth-links {
        text-align: center;
    }
    
    .auth-link {
        color: var(--success-600);
        text-decoration: none;
        font-weight: 500;
        transition: var(--transition-base);
    }
    
    .auth-link:hover {
        color: var(--success-700);
        text-decoration: underline;
    }
    
    .auth-divider {
        margin: 1.5rem 0;
        text-align: center;
        position: relative;
        color: var(--gray-500);
        font-size: 0.9rem;
    }
    
    .auth-divider::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background: var(--gray-200);
        z-index: 1;
    }
    
    .auth-divider span {
        background: rgba(255, 255, 255, 0.95);
        padding: 0 1rem;
        position: relative;
        z-index: 2;
    }
    
    /* Password Strength */
    .password-strength {
        margin-top: 0.5rem;
        font-size: 0.8rem;
    }
    
    .strength-bar {
        height: 4px;
        background: var(--gray-200);
        border-radius: 2px;
        overflow: hidden;
        margin-bottom: 0.5rem;
    }
    
    .strength-fill {
        height: 100%;
        width: 0%;
        transition: var(--transition-base);
        border-radius: 2px;
    }
    
    .strength-weak { background: var(--danger-500); width: 25%; }
    .strength-fair { background: var(--warning-500); width: 50%; }
    .strength-good { background: var(--primary-500); width: 75%; }
    .strength-strong { background: var(--success-500); width: 100%; }
    
    /* Error Messages */
    .alert {
        padding: 1rem;
        border-radius: var(--border-radius-lg);
        margin-bottom: 1.5rem;
        border: 1px solid transparent;
    }
    
    .alert-danger {
        background: rgba(239, 68, 68, 0.1);
        border-color: rgba(239, 68, 68, 0.2);
        color: var(--danger-700);
    }
    
    .alert-success {
        background: rgba(16, 185, 129, 0.1);
        border-color: rgba(16, 185, 129, 0.2);
        color: var(--success-700);
    }
    
    /* Responsive Design */
    @media (max-width: 768px) {
        .auth-container {
            padding: 2rem 1.5rem;
            margin: 1rem;
        }
        
        .auth-title {
            font-size: 1.75rem;
        }
        
        .auth-shape {
            display: none;
        }
    }
    
    /* Animation */
    .slide-in {
        animation: slideInUp 0.6s ease-out;
    }
    
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="auth-page">
    <div class="auth-background">
        <div class="auth-shape auth-shape-1"></div>
        <div class="auth-shape auth-shape-2"></div>
        <div class="auth-shape auth-shape-3"></div>
    </div>
    
    <div class="auth-container slide-in">
        <div class="auth-header">
            <div class="auth-logo">
                <i class="fas fa-user-plus"></i>
            </div>
            <h1 class="auth-title">创建账户</h1>
            <p class="auth-subtitle">加入我们，享受专业的印刷服务</p>
        </div>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                <div class="alert alert-{{ 'danger' if category == 'error' else category }}">
                    <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }} me-2"></i>
                    {{ message }}
                </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form method="POST" class="auth-form" id="registerForm">
            {{ form.hidden_tag() }}
            
            <div class="form-group">
                <label for="username" class="form-label">用户名</label>
                <div class="input-group">
                    <i class="fas fa-user input-icon"></i>
                    {{ form.username(class="form-control", placeholder="请输入用户名", id="username") }}
                </div>
                {% if form.username.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.username.errors %}
                            <small>{{ error }}</small>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="email" class="form-label">邮箱地址</label>
                <div class="input-group">
                    <i class="fas fa-envelope input-icon"></i>
                    {{ form.email(class="form-control", placeholder="请输入邮箱地址", id="email") }}
                </div>
                {% if form.email.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.email.errors %}
                            <small>{{ error }}</small>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="real_name" class="form-label">真实姓名 (可选)</label>
                <div class="input-group">
                    <i class="fas fa-id-card input-icon"></i>
                    {{ form.real_name(class="form-control", placeholder="请输入真实姓名", id="real_name") }}
                </div>
                {% if form.real_name.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.real_name.errors %}
                            <small>{{ error }}</small>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="phone" class="form-label">手机号 (可选)</label>
                <div class="input-group">
                    <i class="fas fa-mobile-alt input-icon"></i>
                    {{ form.phone(class="form-control", placeholder="请输入11位手机号码", id="phone", pattern="^1[3-9]\\d{9}$") }}
                </div>
                {% if form.phone.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.phone.errors %}
                            <small>{{ error }}</small>
                        {% endfor %}
                    </div>
                {% endif %}
                <small class="form-text text-muted">请输入有效的中国大陆手机号码</small>
            </div>
            
            <div class="form-group">
                <label for="password" class="form-label">密码</label>
                <div class="input-group">
                    <i class="fas fa-lock input-icon"></i>
                    {{ form.password(class="form-control", placeholder="请输入密码", id="password") }}
                </div>
                <div class="password-strength" id="passwordStrength" style="display: none;">
                    <div class="strength-bar">
                        <div class="strength-fill" id="strengthFill"></div>
                    </div>
                    <div class="strength-text" id="strengthText"></div>
                </div>
                {% if form.password.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.password.errors %}
                            <small>{{ error }}</small>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="password2" class="form-label">确认密码</label>
                <div class="input-group">
                    <i class="fas fa-lock input-icon"></i>
                    {{ form.password2(class="form-control", placeholder="请再次输入密码", id="password2") }}
                </div>
                {% if form.password2.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.password2.errors %}
                            <small>{{ error }}</small>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <button type="submit" class="btn-submit" id="submitBtn">
                <i class="fas fa-spinner fa-spin loading-spinner"></i>
                创建账户
            </button>
        </form>
        
        <div class="auth-links">
            <div class="auth-divider">
                <span>已有账户？</span>
            </div>
            <p>
                <a href="{{ url_for('auth.login') }}" class="auth-link">
                    <i class="fas fa-sign-in-alt me-1"></i>立即登录
                </a>
            </p>
            <p>
                <a href="{{ url_for('main.index') }}" class="auth-link">
                    <i class="fas fa-home me-1"></i>返回首页
                </a>
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('registerForm');
    const submitBtn = document.getElementById('submitBtn');
    const passwordInput = document.getElementById('password');
    const password2Input = document.getElementById('password2');
    const phoneInput = document.getElementById('phone');
    const strengthContainer = document.getElementById('passwordStrength');
    const strengthFill = document.getElementById('strengthFill');
    const strengthText = document.getElementById('strengthText');
    
    // Phone validation
    function validatePhone() {
        const phone = phoneInput.value.trim();
        const phoneRegex = /^1[3-9]\d{9}$/;
        
        // 清除之前的验证状态
        phoneInput.classList.remove('is-valid', 'is-invalid');
        
        if (phone === '') {
            // 空值不显示错误（可选字段）
            return true;
        }
        
        if (!phoneRegex.test(phone)) {
            phoneInput.style.borderColor = 'var(--danger-500)';
            return false;
        } else {
            phoneInput.style.borderColor = 'var(--success-500)';
            return true;
        }
    }
    
    // 手机号实时验证
    if (phoneInput) {
        phoneInput.addEventListener('input', function() {
            validatePhone();
        });
        
        phoneInput.addEventListener('blur', function() {
            validatePhone();
        });
        
        // 只允许输入数字
        phoneInput.addEventListener('keypress', function(e) {
            if (!/[0-9]/.test(e.key) && !['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(e.key)) {
                e.preventDefault();
            }
        });
        
        // 限制最大长度为11
        phoneInput.addEventListener('input', function(e) {
            if (this.value.length > 11) {
                this.value = this.value.slice(0, 11);
            }
        });
    }
    
    // Password strength checker
    function checkPasswordStrength(password) {
        if (!password) {
            strengthContainer.style.display = 'none';
            return;
        }
        
        strengthContainer.style.display = 'block';
        
        let score = 0;
        let feedback = [];
        
        // Length check
        if (password.length >= 8) score += 1;
        else feedback.push('至少8个字符');
        
        // Lowercase check
        if (/[a-z]/.test(password)) score += 1;
        else feedback.push('包含小写字母');
        
        // Uppercase check
        if (/[A-Z]/.test(password)) score += 1;
        else feedback.push('包含大写字母');
        
        // Number check
        if (/\d/.test(password)) score += 1;
        else feedback.push('包含数字');
        
        // Special character check
        if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 1;
        else feedback.push('包含特殊字符');
        
        // Update UI
        strengthFill.className = 'strength-fill';
        if (score <= 2) {
            strengthFill.classList.add('strength-weak');
            strengthText.textContent = '密码强度：弱';
            strengthText.style.color = 'var(--danger-600)';
        } else if (score === 3) {
            strengthFill.classList.add('strength-fair');
            strengthText.textContent = '密码强度：一般';
            strengthText.style.color = 'var(--warning-600)';
        } else if (score === 4) {
            strengthFill.classList.add('strength-good');
            strengthText.textContent = '密码强度：良好';
            strengthText.style.color = 'var(--primary-600)';
        } else {
            strengthFill.classList.add('strength-strong');
            strengthText.textContent = '密码强度：很强';
            strengthText.style.color = 'var(--success-600)';
        }
        
        if (feedback.length > 0 && score < 4) {
            strengthText.textContent += ' (建议: ' + feedback.slice(0, 2).join(', ') + ')';
        }
    }
    
    // Password confirmation check
    function checkPasswordMatch() {
        const password = passwordInput.value;
        const password2 = password2Input.value;
        
        if (password2 && password !== password2) {
            password2Input.style.borderColor = 'var(--danger-500)';
        } else if (password2) {
            password2Input.style.borderColor = 'var(--success-500)';
        } else {
            password2Input.style.borderColor = '';
        }
    }
    
    // Event listeners
    passwordInput.addEventListener('input', function() {
        checkPasswordStrength(this.value);
    });
    
    password2Input.addEventListener('input', checkPasswordMatch);
    passwordInput.addEventListener('input', checkPasswordMatch);
    
    // Form submission
    form.addEventListener('submit', function(e) {
        // Add loading state
        submitBtn.classList.add('loading');
        submitBtn.disabled = true;
        
        // Let the form submit normally
        setTimeout(() => {
            if (submitBtn.classList.contains('loading')) {
                submitBtn.classList.remove('loading');
                submitBtn.disabled = false;
            }
        }, 3000); // Reset after 3 seconds if no redirect
    });
    
    // Auto-focus first input
    document.getElementById('username').focus();
    
    // Enter key handling
    document.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !submitBtn.disabled) {
            form.submit();
        }
    });
});
</script>
{% endblock %}
