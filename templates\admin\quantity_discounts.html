{% extends "admin/base.html" %}

{% block title %}数量折扣管理 - 管理后台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">数量折扣管理</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('admin.add_quantity_discount') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>添加折扣规则
        </a>
    </div>
</div>

<!-- 筛选器 -->
<div class="card mb-3">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">商品筛选</label>
                <select name="product_id" class="form-select" onchange="this.form.submit()">
                    <option value="">全部商品</option>
                    {% for product in products %}
                    <option value="{{ product.id }}" {{ 'selected' if product.id == current_product_id }}>
                        {{ product.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-8 d-flex align-items-end">
                {% if current_product_id %}
                <a href="{{ url_for('admin.quantity_discounts') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-2"></i>清除筛选
                </a>
                {% endif %}
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-body">
        {% if discounts.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>商品名称</th>
                        <th>数量区间</th>
                        <th>折扣类型</th>
                        <th>折扣值</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for discount in discounts.items %}
                    <tr>
                        <td>{{ discount.id }}</td>
                        <td>{{ discount.product.name }}</td>
                        <td>
                            {{ discount.min_quantity }}
                            {% if discount.max_quantity %}
                                - {{ discount.max_quantity }}
                            {% else %}
                                +
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-{{ 'info' if discount.discount_type == 'percentage' else 'warning' }}">
                                {{ '百分比' if discount.discount_type == 'percentage' else '固定金额' }}
                            </span>
                        </td>
                        <td>
                            {% if discount.discount_type == 'percentage' %}
                                {{ discount.discount_value }}%
                            {% else %}
                                ¥{{ "%.2f"|format(discount.discount_value) }}
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-{{ 'success' if discount.is_active else 'secondary' }}">
                                {{ '启用' if discount.is_active else '禁用' }}
                            </span>
                        </td>
                        <td>{{ discount.created_at.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <a href="{{ url_for('admin.edit_quantity_discount', id=discount.id) }}" class="btn btn-sm btn-outline-primary">编辑</a>
                            <button type="button" class="btn btn-sm btn-outline-danger delete-btn"
                                    data-url="{{ url_for('admin.delete_quantity_discount', id=discount.id) }}"
                                    data-name="{{ discount.product.name }} ({{ discount.min_quantity }}+)">删除</button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        {% if discounts.pages > 1 %}
        <nav aria-label="折扣规则分页">
            <ul class="pagination justify-content-center">
                {% if discounts.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.quantity_discounts', page=discounts.prev_num, product_id=current_product_id) }}">上一页</a>
                </li>
                {% endif %}
                
                {% for page_num in discounts.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != discounts.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.quantity_discounts', page=page_num, product_id=current_product_id) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if discounts.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.quantity_discounts', page=discounts.next_num, product_id=current_product_id) }}">下一页</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-percentage fa-3x text-muted mb-3"></i>
            <h5>暂无折扣规则</h5>
            <p class="text-muted">还没有创建任何数量折扣规则</p>
            <a href="{{ url_for('admin.add_quantity_discount') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>添加第一个折扣规则
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除折扣规则 "<span id="deleteName"></span>" 吗？</p>
                <p class="text-danger small">删除后无法恢复，请谨慎操作。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 初始化数量折扣页面删除功能');
    
    // 初始化删除功能
    initDeleteFunction();
});

function initDeleteFunction() {
    const deleteModal = document.getElementById('deleteModal');
    const deleteNameSpan = document.getElementById('deleteName');
    const confirmDeleteBtn = document.getElementById('confirmDelete');
    let currentDeleteUrl = '';
    let currentDeleteName = '';
    
    if (!deleteModal) {
        console.error('删除模态框未找到');
        return;
    }
    
    const modal = new bootstrap.Modal(deleteModal);
    
    // 移除所有现有的事件监听器并重新绑定
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(btn => {
        const newBtn = btn.cloneNode(true);
        btn.parentNode.replaceChild(newBtn, btn);
    });
    
    document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            currentDeleteUrl = this.dataset.url;
            currentDeleteName = this.dataset.name;
            
            if (deleteNameSpan) {
                deleteNameSpan.textContent = currentDeleteName;
            }
            
            modal.show();
        });
    });
    
    // 确认删除事件
    if (confirmDeleteBtn) {
        const newConfirmBtn = confirmDeleteBtn.cloneNode(true);
        confirmDeleteBtn.parentNode.replaceChild(newConfirmBtn, confirmDeleteBtn);
        
        document.getElementById('confirmDelete').addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            if (!currentDeleteUrl) {
                return;
            }
            
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>删除中...';
            
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '{{ csrf_token() }}';
            
            fetch(currentDeleteUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken,
                    'Accept': 'application/json'
                },
                credentials: 'same-origin'
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    this.disabled = false;
                    this.innerHTML = '确认删除';
                }
            })
            .catch(error => {
                console.error('删除错误:', error);
                this.disabled = false;
                this.innerHTML = '确认删除';
            });
            
            modal.hide();
        });
    }
}
</script>
{% endblock %}
