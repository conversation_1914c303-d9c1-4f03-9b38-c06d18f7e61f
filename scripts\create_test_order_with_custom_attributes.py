#!/usr/bin/env python3
"""
创建包含多个自定义属性的测试订单
验证自定义数据的完整处理流程
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def create_test_attribute_groups():
    """创建测试用的自定义属性组"""
    print("🔧 创建测试用的自定义属性组...")
    
    try:
        from app import create_app
        from models import db
        from models.product import Category, AttributeGroup
        
        app = create_app()
        with app.app_context():
            # 查找或创建测试分类
            category = Category.query.filter_by(name='测试分类').first()
            if not category:
                category = Category(
                    name='测试分类',
                    description='用于测试自定义属性的分类'
                )
                db.session.add(category)
                db.session.flush()
            
            # 创建多个支持自定义输入的属性组
            test_groups = [
                {
                    'name': '尺寸',
                    'description': '商品尺寸规格',
                    'custom_input_type': 'text',
                    'custom_input_label': '自定义尺寸',
                    'custom_input_placeholder': '请输入尺寸（如：300×400mm）',
                    'custom_validation_pattern': r'^.{1,50}$',
                    'custom_validation_message': '请输入有效的尺寸',
                    'custom_price_formula': 'base_price * 0.1',
                    'custom_price_modifier_type': 'formula'
                },
                {
                    'name': '材质',
                    'description': '商品材质规格',
                    'custom_input_type': 'text',
                    'custom_input_label': '自定义材质',
                    'custom_input_placeholder': '请输入材质要求',
                    'custom_validation_pattern': r'^.{1,100}$',
                    'custom_validation_message': '请输入有效的材质',
                    'custom_price_modifier': 20.0,
                    'custom_price_modifier_type': 'fixed'
                },
                {
                    'name': '数量',
                    'description': '印刷数量',
                    'custom_input_type': 'number',
                    'custom_input_label': '自定义数量',
                    'custom_input_placeholder': '请输入数量',
                    'custom_validation_pattern': r'^[1-9]\d*$',
                    'custom_validation_message': '请输入有效的数量',
                    'custom_price_formula': 'numeric_value * 0.05',
                    'custom_price_modifier_type': 'formula'
                },
                {
                    'name': '工艺',
                    'description': '后道工艺要求',
                    'custom_input_type': 'textarea',
                    'custom_input_label': '自定义工艺',
                    'custom_input_placeholder': '请详细描述工艺要求',
                    'custom_validation_pattern': r'^.{1,500}$',
                    'custom_validation_message': '请输入有效的工艺要求',
                    'custom_price_modifier': 15.0,
                    'custom_price_modifier_type': 'percentage'
                }
            ]
            
            created_groups = []
            
            for group_data in test_groups:
                # 检查是否已存在
                existing_group = AttributeGroup.query.filter_by(
                    category_id=category.id,
                    name=group_data['name']
                ).first()
                
                if existing_group:
                    # 更新现有属性组
                    for key, value in group_data.items():
                        if key != 'name':
                            setattr(existing_group, key, value)
                    existing_group.allow_custom_input = True
                    created_groups.append(existing_group)
                    print(f"✅ 更新属性组: {group_data['name']}")
                else:
                    # 创建新属性组
                    group = AttributeGroup(
                        category_id=category.id,
                        allow_custom_input=True,
                        **group_data
                    )
                    db.session.add(group)
                    created_groups.append(group)
                    print(f"✅ 创建属性组: {group_data['name']}")
            
            db.session.commit()
            print(f"🎉 成功创建/更新 {len(created_groups)} 个自定义属性组")
            return created_groups
            
    except Exception as e:
        print(f"❌ 创建属性组失败: {str(e)}")
        return []

def create_test_product():
    """创建测试商品"""
    print("🛍️ 创建测试商品...")
    
    try:
        from app import create_app
        from models import db
        from models.product import Product, Category
        
        app = create_app()
        with app.app_context():
            # 获取测试分类
            category = Category.query.filter_by(name='测试分类').first()
            if not category:
                print("❌ 测试分类不存在")
                return None
            
            # 查找或创建测试商品
            product = Product.query.filter_by(name='多属性测试商品').first()
            if not product:
                product = Product(
                    name='多属性测试商品',
                    description='用于测试多个自定义属性的商品',
                    category_id=category.id,
                    base_price=100.0,
                    is_active=True
                )
                db.session.add(product)
                db.session.commit()
                print("✅ 创建测试商品成功")
            else:
                print("✅ 测试商品已存在")
            
            return product
            
    except Exception as e:
        print(f"❌ 创建测试商品失败: {str(e)}")
        return None

def create_test_user():
    """创建测试用户"""
    print("👤 创建测试用户...")
    
    try:
        from app import create_app
        from models import db
        from models.user import User
        
        app = create_app()
        with app.app_context():
            # 查找或创建测试用户
            user = User.query.filter_by(username='testuser').first()
            if not user:
                user = User(
                    username='testuser',
                    email='<EMAIL>',
                    real_name='测试用户',
                    phone='13800138000',
                    is_active=True
                )
                user.set_password('test123')
                db.session.add(user)
                db.session.commit()
                print("✅ 创建测试用户成功")
            else:
                print("✅ 测试用户已存在")
            
            return user
            
    except Exception as e:
        print(f"❌ 创建测试用户失败: {str(e)}")
        return None

def create_test_order_with_multiple_custom_attributes():
    """创建包含多个自定义属性的测试订单"""
    print("📦 创建包含多个自定义属性的测试订单...")
    
    try:
        from app import create_app
        from models import db
        from models.order import Order, OrderItem
        from collections import OrderedDict
        import uuid
        
        app = create_app()
        with app.app_context():
            # 获取测试数据
            user = create_test_user()
            product = create_test_product()
            
            if not user or not product:
                print("❌ 缺少必要的测试数据")
                return None
            
            # 模拟多个自定义属性数据
            custom_attributes_data = OrderedDict([
                ('尺寸', '350×500mm (自定义)'),
                ('材质', '特殊防水纸张 (自定义)'),
                ('数量', '2500张 (自定义)'),
                ('工艺', '双面覆膜+烫金+UV (自定义)')
            ])
            
            # 创建订单
            order_no = f"TEST{uuid.uuid4().hex[:8].upper()}"
            order = Order(
                order_no=order_no,
                user_id=user.id,
                total_amount=250.0,
                final_amount=265.0,  # 包含运费
                shipping_name='测试收货人',
                shipping_phone='13800138000',
                shipping_province='广东省',
                shipping_city='广州市',
                shipping_district='天河区',
                shipping_address='测试地址123号',
                shipping_postal_code='510000',
                notes='这是一个包含多个自定义属性的测试订单',
                status='pending'
            )
            
            db.session.add(order)
            db.session.flush()  # 获取订单ID
            
            # 创建订单项
            order_item = OrderItem(
                order_id=order.id,
                product_id=product.id,
                product_name=product.name,
                product_attributes=custom_attributes_data,
                quantity=1,
                unit_price=250.0,
                total_price=250.0
            )
            
            db.session.add(order_item)
            db.session.commit()
            
            print(f"🎉 成功创建测试订单: {order_no}")
            print(f"   • 订单ID: {order.id}")
            print(f"   • 包含自定义属性数量: {len(custom_attributes_data)}")
            print(f"   • 自定义属性详情:")
            
            for attr_name, attr_value in custom_attributes_data.items():
                print(f"     - {attr_name}: {attr_value}")
            
            # 验证订单项方法
            print(f"\n🔍 验证订单项方法:")
            print(f"   • has_custom_attributes(): {order_item.has_custom_attributes()}")
            print(f"   • get_attributes_display(): {order_item.get_attributes_display()}")
            
            attr_details = order_item.get_attributes_detail()
            custom_attrs = [attr for attr in attr_details if attr['is_custom']]
            print(f"   • 自定义属性详情数量: {len(custom_attrs)}")
            
            for attr in custom_attrs:
                print(f"     - {attr['name']}: {attr['display_value']}")
            
            return order
            
    except Exception as e:
        import traceback
        print(f"❌ 创建测试订单失败: {str(e)}")
        print(traceback.format_exc())
        return None

def main():
    """主函数"""
    print("🧪 创建多自定义属性测试订单")
    print("=" * 50)
    
    # 1. 创建测试属性组
    groups = create_test_attribute_groups()
    if not groups:
        print("❌ 无法创建测试属性组，退出")
        return
    
    # 2. 创建测试订单
    order = create_test_order_with_multiple_custom_attributes()
    if not order:
        print("❌ 无法创建测试订单，退出")
        return
    
    # 3. 提供测试链接
    print("\n" + "=" * 50)
    print("🔗 测试链接:")
    print(f"   • 前台订单详情: /order/{order.id}")
    print(f"   • 后台订单详情: /admin/orders/{order.id}")
    print(f"   • 调试API: /api/debug/order-attributes/{order.id}")
    print(f"   • 多属性测试页面: /test/multiple-custom-attributes")
    
    print("\n📋 测试步骤:")
    print("1. 访问前台订单详情，检查自定义属性显示")
    print("2. 访问后台订单详情，检查管理员视图")
    print("3. 使用调试API查看原始数据")
    print("4. 在多属性测试页面进行交互测试")
    
    print("\n✅ 测试订单创建完成！")

if __name__ == '__main__':
    main()
