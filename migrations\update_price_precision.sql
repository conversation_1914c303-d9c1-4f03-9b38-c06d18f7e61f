-- 升级价格字段精度：从DECIMAL(10,2)改为DECIMAL(15,5)
-- 执行前请备份数据库

-- 1. 更新attributes表的price_modifier字段
ALTER TABLE attributes MODIFY COLUMN price_modifier DECIMAL(15,5) DEFAULT 0.00000;

-- 2. 更新products表的base_price字段
ALTER TABLE products MODIFY COLUMN base_price DECIMAL(15,5) NOT NULL;

-- 3. 更新quantity_discounts表的discount_value字段
ALTER TABLE quantity_discounts MODIFY COLUMN discount_value DECIMAL(15,5) NOT NULL;

-- 4. 更新orders表的价格相关字段
ALTER TABLE orders MODIFY COLUMN total_amount DECIMAL(15,5) NOT NULL;
ALTER TABLE orders MODIFY COLUMN discount_amount DECIMAL(15,5) DEFAULT 0.00000;
ALTER TABLE orders MODIFY COLUMN final_amount DECIMAL(15,5) NOT NULL;

-- 5. 更新order_items表的价格相关字段
ALTER TABLE order_items MODIFY COLUMN unit_price DECIMAL(15,5) NOT NULL;
ALTER TABLE order_items MODIFY COLUMN total_price DECIMAL(15,5) NOT NULL;

-- 6. 如果存在shipping_fee字段也需要更新
-- ALTER TABLE orders MODIFY COLUMN shipping_fee DECIMAL(15,5) DEFAULT 0.00000;

-- 显示迁移结果
SELECT 
    'Price precision upgrade completed' as status,
    'All price fields now support 5 decimal places (DECIMAL(15,5))' as message;

-- 验证字段精度
SELECT 
    TABLE_NAME, 
    COLUMN_NAME, 
    DATA_TYPE, 
    NUMERIC_PRECISION, 
    NUMERIC_SCALE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND COLUMN_NAME LIKE '%price%' 
OR COLUMN_NAME LIKE '%amount%' 
OR COLUMN_NAME = 'price_modifier'
OR COLUMN_NAME = 'discount_value'
ORDER BY TABLE_NAME, COLUMN_NAME; 