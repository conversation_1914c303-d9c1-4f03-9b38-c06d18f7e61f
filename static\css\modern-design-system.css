/**
 * Modern Design System
 * 现代化印刷服务系统 - 设计系统
 * @version 2.0.0
 */

:root {
    /* ========== 色彩系统 ========== */
    
    /* 主色调 */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;
    
    /* 辅助色 */
    --secondary-50: #fdf4ff;
    --secondary-100: #fae8ff;
    --secondary-200: #f5d0fe;
    --secondary-300: #f0abfc;
    --secondary-400: #e879f9;
    --secondary-500: #d946ef;
    --secondary-600: #c026d3;
    --secondary-700: #a21caf;
    --secondary-800: #86198f;
    --secondary-900: #701a75;
    
    /* 功能色 */
    --success-50: #ecfdf5;
    --success-100: #d1fae5;
    --success-200: #a7f3d0;
    --success-300: #6ee7b7;
    --success-400: #34d399;
    --success-500: #10b981;
    --success-600: #059669;
    --success-700: #047857;
    --success-800: #065f46;
    --success-900: #064e3b;
    
    --warning-50: #fffbeb;
    --warning-100: #fef3c7;
    --warning-200: #fde68a;
    --warning-300: #fcd34d;
    --warning-400: #fbbf24;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    --warning-700: #b45309;
    --warning-800: #92400e;
    --warning-900: #78350f;
    
    --danger-50: #fef2f2;
    --danger-100: #fee2e2;
    --danger-200: #fecaca;
    --danger-300: #fca5a5;
    --danger-400: #f87171;
    --danger-500: #ef4444;
    --danger-600: #dc2626;
    --danger-700: #b91c1c;
    --danger-800: #991b1b;
    --danger-900: #7f1d1d;
    
    /* 中性色 */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* 渐变色 */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-warning: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
    --gradient-danger: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --gradient-light: linear-gradient(135deg, #f6f9fc 0%, #eef1f5 100%);
    --gradient-dark: linear-gradient(135deg, #2b2b2b 0%, #1a1a1a 100%);
    
    /* ========== 字体系统 ========== */
    
    /* 字体族 */
    --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-family-heading: 'Poppins', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-family-mono: 'JetBrains Mono', 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    
    /* 字体粗细 */
    --font-thin: 100;
    --font-extralight: 200;
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    --font-extrabold: 800;
    --font-black: 900;
    
    /* 字体大小 */
    --text-xs: 0.75rem;      /* 12px */
    --text-sm: 0.875rem;     /* 14px */
    --text-base: 1rem;       /* 16px */
    --text-lg: 1.125rem;     /* 18px */
    --text-xl: 1.25rem;      /* 20px */
    --text-2xl: 1.5rem;      /* 24px */
    --text-3xl: 1.875rem;    /* 30px */
    --text-4xl: 2.25rem;     /* 36px */
    --text-5xl: 3rem;        /* 48px */
    --text-6xl: 3.75rem;     /* 60px */
    --text-7xl: 4.5rem;      /* 72px */
    --text-8xl: 6rem;        /* 96px */
    --text-9xl: 8rem;        /* 128px */
    
    /* 行高 */
    --leading-none: 1;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;
    
    /* 字间距 */
    --tracking-tighter: -0.05em;
    --tracking-tight: -0.025em;
    --tracking-normal: 0em;
    --tracking-wide: 0.025em;
    --tracking-wider: 0.05em;
    --tracking-widest: 0.1em;
    
    /* ========== 间距系统 ========== */
    
    /* 基于8点网格系统 */
    --space-0: 0;
    --space-1: 0.25rem;      /* 4px */
    --space-2: 0.5rem;       /* 8px */
    --space-3: 0.75rem;      /* 12px */
    --space-4: 1rem;         /* 16px */
    --space-5: 1.25rem;      /* 20px */
    --space-6: 1.5rem;       /* 24px */
    --space-7: 1.75rem;      /* 28px */
    --space-8: 2rem;         /* 32px */
    --space-9: 2.25rem;      /* 36px */
    --space-10: 2.5rem;      /* 40px */
    --space-11: 2.75rem;     /* 44px */
    --space-12: 3rem;        /* 48px */
    --space-14: 3.5rem;      /* 56px */
    --space-16: 4rem;        /* 64px */
    --space-20: 5rem;        /* 80px */
    --space-24: 6rem;        /* 96px */
    --space-28: 7rem;        /* 112px */
    --space-32: 8rem;        /* 128px */
    --space-36: 9rem;        /* 144px */
    --space-40: 10rem;       /* 160px */
    --space-44: 11rem;       /* 176px */
    --space-48: 12rem;       /* 192px */
    --space-52: 13rem;       /* 208px */
    --space-56: 14rem;       /* 224px */
    --space-60: 15rem;       /* 240px */
    --space-64: 16rem;       /* 256px */
    --space-72: 18rem;       /* 288px */
    --space-80: 20rem;       /* 320px */
    --space-96: 24rem;       /* 384px */
    
    /* ========== 圆角系统 ========== */
    
    --radius: 0.25rem;       /* 4px */
    --radius-sm: 0.125rem;   /* 2px */
    --radius-md: 0.375rem;   /* 6px */
    --radius-lg: 0.5rem;     /* 8px */
    --radius-xl: 0.75rem;    /* 12px */
    --radius-2xl: 1rem;      /* 16px */
    --radius-3xl: 1.5rem;    /* 24px */
    --radius-full: 9999px;
    
    /* ========== 阴影系统 ========== */
    
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    
    /* 彩色阴影 */
    --shadow-primary: 0 10px 15px -3px rgba(59, 130, 246, 0.3);
    --shadow-success: 0 10px 15px -3px rgba(16, 185, 129, 0.3);
    --shadow-warning: 0 10px 15px -3px rgba(245, 158, 11, 0.3);
    --shadow-danger: 0 10px 15px -3px rgba(239, 68, 68, 0.3);
    
    /* ========== 过渡系统 ========== */
    
    --transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    --transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
    --transition-duration: 150ms;
    
    --transition-all: all 0.3s var(--transition-timing);
    --transition-colors: background-color 0.3s var(--transition-timing), border-color 0.3s var(--transition-timing), color 0.3s var(--transition-timing);
    --transition-opacity: opacity 0.3s var(--transition-timing);
    --transition-shadow: box-shadow 0.3s var(--transition-timing);
    --transition-transform: transform 0.3s var(--transition-timing);
    
    /* ========== 容器系统 ========== */
    
    --container-sm: 640px;
    --container-md: 768px;
    --container-lg: 1024px;
    --container-xl: 1280px;
    --container-2xl: 1536px;
    
    /* ========== Z轴层级系统 ========== */
    
    --z-0: 0;
    --z-10: 10;
    --z-20: 20;
    --z-30: 30;
    --z-40: 40;
    --z-50: 50;
    --z-auto: auto;
    
    /* 特殊层级 */
    --z-dropdown: 1000;
    --z-sticky: 1100;
    --z-fixed: 1200;
    --z-modal: 1300;
    --z-popover: 1400;
    --z-tooltip: 1500;
    
    /* ========== 主题特定变量 ========== */
    
    /* 亮色主题 */
    --bg-body: var(--gray-50);
    --bg-card: white;
    --bg-input: white;
    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-600);
    --text-muted: var(--gray-400);
    --border-color: var(--gray-200);
}

/* 暗色主题变量 */
[data-bs-theme="dark"] {
    --bg-body: var(--gray-900);
    --bg-card: var(--gray-800);
    --bg-input: var(--gray-700);
    --text-primary: var(--gray-50);
    --text-secondary: var(--gray-300);
    --text-muted: var(--gray-500);
    --border-color: var(--gray-700);
    
    /* 暗色主题阴影调整 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.4);
}

/* ========== 实用工具类 ========== */

/* 文本渐变效果 */
.text-gradient-primary {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-gradient-secondary {
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 透明背景效果 */
.bg-glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.bg-glass-dark {
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 渐变背景 */
.bg-gradient-primary {
    background: var(--gradient-primary);
}

.bg-gradient-secondary {
    background: var(--gradient-secondary);
}

.bg-gradient-success {
    background: var(--gradient-success);
}

.bg-gradient-warning {
    background: var(--gradient-warning);
}

.bg-gradient-danger {
    background: var(--gradient-danger);
}

/* 动画类 */
.animate-fade-in {
    animation: fadeIn 0.5s ease-out;
}

.animate-slide-up {
    animation: slideUp 0.5s ease-out;
}

.animate-slide-down {
    animation: slideDown 0.5s ease-out;
}

.animate-slide-left {
    animation: slideLeft 0.5s ease-out;
}

.animate-slide-right {
    animation: slideRight 0.5s ease-out;
}

.animate-scale {
    animation: scale 0.5s ease-out;
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
    animation: bounce 1s ease infinite;
}

/* 交互状态类 */
.hover-scale {
    transition: var(--transition-transform);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-lift {
    transition: var(--transition-transform), var(--transition-shadow);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.hover-glow {
    transition: var(--transition-shadow);
}

.hover-glow:hover {
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
}

/* ========== 动画关键帧 ========== */

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideLeft {
    from { transform: translateX(20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideRight {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes scale {
    from { transform: scale(0.9); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

/* ========== 响应式设计 ========== */

/* Mobile First 设计策略 */
@media (min-width: 640px) {
    /* Small (sm) */
}

@media (min-width: 768px) {
    /* Medium (md) */
}

@media (min-width: 1024px) {
    /* Large (lg) */
}

@media (min-width: 1280px) {
    /* Extra Large (xl) */
}

@media (min-width: 1536px) {
    /* 2X Extra Large (2xl) */
}

/* ========== 打印样式 ========== */

@media print {
    body {
        background: white !important;
        color: black !important;
    }
    
    .no-print {
        display: none !important;
    }
    
    a {
        text-decoration: none !important;
    }
    
    .container {
        width: 100% !important;
        max-width: 100% !important;
        padding: 0 !important;
        margin: 0 !important;
    }
    
    .page-break {
        page-break-after: always;
    }
}

/* ========== 可访问性增强 ========== */

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}

:focus-visible {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

/* 减少动画（尊重用户偏好） */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
} 