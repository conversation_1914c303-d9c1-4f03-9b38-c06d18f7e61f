{% extends "base_modern.html" %}

{% block title %}支付成功 - {{ site_config.site_name }}{% endblock %}
{% block description %}订单支付成功{% endblock %}

{% block extra_css %}
<style>
    /* ========== Payment Success Styles ========== */
    .payment-success-section {
        padding: 4rem 0;
        background: var(--gradient-light);
        min-height: 100vh;
        display: flex;
        align-items: center;
    }
    
    .success-container {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-radius: var(--border-radius-2xl);
        padding: 3rem;
        box-shadow: var(--shadow-xl);
        border: 1px solid rgba(255, 255, 255, 0.3);
        text-align: center;
        position: relative;
        overflow: hidden;
    }
    
    .success-icon {
        width: 120px;
        height: 120px;
        background: var(--success-100);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2rem;
        box-shadow: var(--shadow-lg);
        border: 5px solid var(--success-200);
    }
    
    .success-icon i {
        font-size: 4rem;
        color: var(--success);
    }
    
    .success-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--gray-800);
        margin-bottom: 1rem;
    }
    
    .success-message {
        font-size: 1.1rem;
        color: var(--gray-600);
        margin-bottom: 2rem;
    }
    
    .order-info {
        background: var(--gray-100);
        border-radius: var(--border-radius-lg);
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid var(--gray-200);
    }
    
    .order-info-row {
        display: flex;
        justify-content: space-between;
        padding: 0.75rem 0;
        border-bottom: 1px solid var(--gray-200);
    }
    
    .order-info-row:last-child {
        border-bottom: none;
    }
    
    .order-info-label {
        font-weight: 600;
        color: var(--gray-600);
    }
    
    .order-info-value {
        font-weight: 700;
        color: var(--gray-800);
    }
    
    .amount-value {
        color: var(--primary);
        font-size: 1.2rem;
    }
    
    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
    }
    
    .btn-modern {
        background: var(--primary-gradient);
        color: white;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: var(--border-radius-xl);
        font-weight: 600;
        text-decoration: none;
        transition: var(--transition-base);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        color: white;
    }
    
    .btn-secondary {
        background: var(--gradient-light);
        color: var(--gray-700);
        border: 2px solid var(--gray-300);
    }
    
    .btn-secondary:hover {
        background: var(--gray-100);
        border-color: var(--gray-400);
        color: var(--gray-800);
    }
    
    .confetti {
        position: absolute;
        width: 10px;
        height: 10px;
        background: var(--primary);
        opacity: 0.8;
        animation: confetti 5s ease-in-out infinite;
    }
    
    @keyframes confetti {
        0% { transform: translateY(0) rotate(0deg); opacity: 1; }
        100% { transform: translateY(100vh) rotate(720deg); opacity: 0; }
    }
    
    /* ========== Responsive Design ========== */
    @media (max-width: 768px) {
        .payment-success-section {
            padding: 2rem 0;
        }
        
        .success-container {
            padding: 2rem 1rem;
        }
        
        .success-icon {
            width: 80px;
            height: 80px;
            margin-bottom: 1.5rem;
        }
        
        .success-icon i {
            font-size: 2.5rem;
        }
        
        .success-title {
            font-size: 1.5rem;
        }
        
        .action-buttons {
            flex-direction: column;
        }
        
        .btn-modern {
            width: 100%;
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="payment-success-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="success-container">
                    <!-- 装饰性彩带效果 -->
                    <div id="confetti-container"></div>
                    
                    <!-- 成功图标 -->
                    <div class="success-icon">
                        <i class="fas fa-check"></i>
                    </div>
                    
                    <!-- 成功信息 -->
                    <h1 class="success-title">支付成功</h1>
                    <p class="success-message">感谢您的购买！您的订单已成功支付，我们将尽快为您处理。</p>
                    
                    <!-- 订单信息 -->
                    <div class="order-info">
                        <div class="order-info-row">
                            <span class="order-info-label">订单编号</span>
                            <span class="order-info-value">{{ order.order_no }}</span>
                        </div>
                        <div class="order-info-row">
                            <span class="order-info-label">支付方式</span>
                            <span class="order-info-value">{{ order.payment_method or '在线支付' }}</span>
                        </div>
                        <div class="order-info-row">
                            <span class="order-info-label">支付时间</span>
                            <span class="order-info-value">{{ format_china_time(order.updated_at) }}</span>
                        </div>
                        <div class="order-info-row">
                            <span class="order-info-label">支付金额</span>
                            <span class="order-info-value amount-value">¥{{ "%.2f"|format(order.final_amount) }}</span>
                        </div>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="action-buttons">
                        <a href="{{ url_for('main.order_detail', id=order.id) }}" class="btn-modern">
                            <i class="fas fa-file-invoice"></i>
                            查看订单详情
                        </a>
                        <a href="{{ url_for('main.orders') }}" class="btn-modern btn-secondary">
                            <i class="fas fa-list"></i>
                            我的订单列表
                        </a>
                        <a href="{{ url_for('main.products') }}" class="btn-modern btn-secondary">
                            <i class="fas fa-shopping-bag"></i>
                            继续购物
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    // 创建彩带效果
    document.addEventListener('DOMContentLoaded', function() {
        const confettiContainer = document.getElementById('confetti-container');
        const colors = ['#4F46E5', '#10B981', '#F59E0B', '#EF4444', '#EC4899'];
        
        // 创建彩带元素
        for (let i = 0; i < 50; i++) {
            const confetti = document.createElement('div');
            confetti.className = 'confetti';
            confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
            confetti.style.left = Math.random() * 100 + '%';
            confetti.style.width = Math.random() * 10 + 5 + 'px';
            confetti.style.height = Math.random() * 10 + 5 + 'px';
            confetti.style.animationDelay = Math.random() * 5 + 's';
            confetti.style.animationDuration = Math.random() * 3 + 2 + 's';
            
            confettiContainer.appendChild(confetti);
        }
        
        // 显示成功通知
        if (window.ModernUI && window.ModernUI.notification) {
            window.ModernUI.notification.success('订单支付成功！');
        } else if (window.ModernNotification) {
            window.ModernNotification.success('订单支付成功！');
        }
    });
</script>
{% endblock %} 