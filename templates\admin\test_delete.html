{% extends "admin/base.html" %}

{% block title %}测试删除功能 - 管理后台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">🧪 商品删除功能测试</h1>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">1. 检查CSRF Token</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-primary" onclick="checkCSRFToken()">检查CSRF Token</button>
                <div id="csrf-result" class="mt-3"></div>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">2. 测试删除请求</h5>
            </div>
            <div class="card-body">
                <p>请先确保：</p>
                <ul>
                    <li>已登录管理员账户</li>
                    <li>数据库中有商品数据</li>
                    <li>Flask应用正在运行</li>
                </ul>
                <div class="input-group mb-3">
                    <input type="number" class="form-control" id="product-id" placeholder="输入商品ID" value="1">
                    <button class="btn btn-danger" onclick="testDelete()">测试删除</button>
                </div>
                <div id="delete-result" class="mt-3"></div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">3. 网络请求日志</h5>
                <button class="btn btn-sm btn-outline-secondary" onclick="clearLog()">清空日志</button>
            </div>
            <div class="card-body">
                <div id="network-log" style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 0.9rem; max-height: 400px; overflow-y: auto;"></div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">📋 测试说明</h5>
            </div>
            <div class="card-body">
                <h6>测试步骤：</h6>
                <ol>
                    <li>点击"检查CSRF Token"确认token正常</li>
                    <li>输入一个存在的商品ID</li>
                    <li>点击"测试删除"执行删除操作</li>
                    <li>查看日志了解详细过程</li>
                </ol>
                
                <h6 class="mt-3">可能的问题：</h6>
                <ul>
                    <li>CSRF Token未找到</li>
                    <li>商品ID不存在</li>
                    <li>权限不足</li>
                    <li>网络请求失败</li>
                </ul>
                
                <h6 class="mt-3">快速链接：</h6>
                <a href="{{ url_for('admin.products') }}" class="btn btn-sm btn-outline-primary">商品管理</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let logMessages = [];

function log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const typeIcon = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
    logMessages.push(`[${timestamp}] ${typeIcon} ${message}`);
    updateLog();
    console.log(message);
}

function updateLog() {
    const logDiv = document.getElementById('network-log');
    logDiv.innerHTML = logMessages.join('<br>');
    logDiv.scrollTop = logDiv.scrollHeight;
}

function clearLog() {
    logMessages = [];
    updateLog();
}

function checkCSRFToken() {
    const resultDiv = document.getElementById('csrf-result');
    
    // 检查meta标签
    const csrfMeta = document.querySelector('meta[name=csrf-token]');
    if (csrfMeta) {
        const token = csrfMeta.getAttribute('content');
        resultDiv.innerHTML = `
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>找到CSRF Token<br>
                <small>Token: ${token.substring(0, 20)}... (长度: ${token.length})</small>
            </div>
        `;
        log(`CSRF Token found: ${token.substring(0, 20)}...`, 'success');
    } else {
        resultDiv.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>未找到CSRF Token meta标签<br>
                <small>请确保页面包含: &lt;meta name="csrf-token" content="..."&gt;</small>
            </div>
        `;
        log('CSRF Token not found in meta tag', 'error');
    }
}

async function testDelete() {
    const productId = document.getElementById('product-id').value;
    const resultDiv = document.getElementById('delete-result');
    
    if (!productId) {
        resultDiv.innerHTML = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>请输入商品ID</div>';
        return;
    }
    
    log(`开始测试删除商品 ID: ${productId}`);
    
    // 获取CSRF token
    const csrfMeta = document.querySelector('meta[name=csrf-token]');
    if (!csrfMeta) {
        resultDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-circle me-2"></i>未找到CSRF Token</div>';
        log('CSRF Token not found', 'error');
        return;
    }
    
    const token = csrfMeta.getAttribute('content');
    const deleteUrl = `/admin/products/${productId}/delete`;
    
    log(`Delete URL: ${deleteUrl}`);
    log(`CSRF Token: ${token.substring(0, 20)}...`);
    
    // 显示加载状态
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin me-2"></i>正在发送删除请求...</div>';
    
    try {
        // 创建FormData
        const formData = new FormData();
        formData.append('csrf_token', token);
        
        log('发送删除请求...');
        
        const response = await fetch(deleteUrl, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': token
            }
        });
        
        log(`响应状态: ${response.status} ${response.statusText}`);
        
        if (response.ok) {
            const data = await response.json();
            log(`响应数据: ${JSON.stringify(data)}`);
            
            if (data.success) {
                resultDiv.innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>删除成功<br>
                        <small>消息: ${data.message}</small>
                    </div>
                `;
                log('删除成功', 'success');
            } else {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>删除失败<br>
                        <small>错误: ${data.message}</small>
                    </div>
                `;
                log(`删除失败: ${data.message}`, 'error');
            }
        } else {
            const errorText = await response.text();
            log(`错误响应: ${errorText}`, 'error');
            
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>请求失败<br>
                    <small>状态码: ${response.status}<br>
                    错误: ${errorText.substring(0, 200)}${errorText.length > 200 ? '...' : ''}</small>
                </div>
            `;
        }
        
    } catch (error) {
        log(`请求异常: ${error.message}`, 'error');
        resultDiv.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>请求异常<br>
                <small>错误: ${error.message}</small>
            </div>
        `;
    }
}

// 页面加载时自动检查CSRF Token
document.addEventListener('DOMContentLoaded', function() {
    log('页面加载完成');
    checkCSRFToken();
});
</script>
{% endblock %}
