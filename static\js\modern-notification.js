/**
 * 现代化通知系统
 * 支持右侧悬浮通知，自动消失，动画效果
 */

class ModernNotification {
    constructor() {
        this.container = null;
        this.notifications = [];
        this.init();
    }

    init() {
        // 创建通知容器
        this.container = document.createElement('div');
        this.container.className = 'modern-notification-container';
        this.container.innerHTML = `
            <style>
                .modern-notification-container {
                    position: fixed;
                    top: 1rem;
                    right: 1rem;
                    z-index: 9999;
                    pointer-events: none;
                    max-width: 400px;
                    width: 100%;
                }
                
                .modern-notification {
                    background: rgba(255, 255, 255, 0.95);
                    backdrop-filter: blur(20px);
                    -webkit-backdrop-filter: blur(20px);
                    border-radius: 1rem;
                    padding: 1rem 1.25rem;
                    margin-bottom: 0.75rem;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    pointer-events: auto;
                    transform: translateX(100%);
                    opacity: 0;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    overflow: hidden;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }
                
                .modern-notification.show {
                    transform: translateX(0);
                    opacity: 1;
                }
                
                .modern-notification.success {
                    border-left: 4px solid #10b981;
                }
                
                .modern-notification.error {
                    border-left: 4px solid #ef4444;
                }
                
                .modern-notification.warning {
                    border-left: 4px solid #f59e0b;
                }
                
                .modern-notification.info {
                    border-left: 4px solid #3b82f6;
                }
                
                .notification-icon {
                    font-size: 1.25rem;
                    flex-shrink: 0;
                }
                
                .notification-icon.success {
                    color: #10b981;
                }
                
                .notification-icon.error {
                    color: #ef4444;
                }
                
                .notification-icon.warning {
                    color: #f59e0b;
                }
                
                .notification-icon.info {
                    color: #3b82f6;
                }
                
                .notification-content {
                    flex: 1;
                    min-width: 0;
                }
                
                .notification-title {
                    font-weight: 600;
                    font-size: 0.95rem;
                    color: #1f2937;
                    margin-bottom: 0.25rem;
                }
                
                .notification-message {
                    font-size: 0.875rem;
                    color: #6b7280;
                    line-height: 1.4;
                }
                
                .notification-close {
                    background: none;
                    border: none;
                    color: #9ca3af;
                    font-size: 1.125rem;
                    cursor: pointer;
                    padding: 0.25rem;
                    border-radius: 0.5rem;
                    transition: all 0.2s;
                    flex-shrink: 0;
                }
                
                .notification-close:hover {
                    color: #6b7280;
                    background: rgba(0, 0, 0, 0.05);
                }
                
                .notification-progress {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    height: 3px;
                    background: linear-gradient(90deg, #3b82f6, #10b981);
                    border-radius: 0 0 1rem 1rem;
                    transition: width linear;
                }
                
                .notification-progress.success {
                    background: #10b981;
                }
                
                .notification-progress.error {
                    background: #ef4444;
                }
                
                .notification-progress.warning {
                    background: #f59e0b;
                }
                
                .notification-progress.info {
                    background: #3b82f6;
                }
                
                @media (max-width: 768px) {
                    .modern-notification-container {
                        top: 0.5rem;
                        right: 0.5rem;
                        left: 0.5rem;
                        max-width: none;
                    }
                    
                    .modern-notification {
                        padding: 0.875rem 1rem;
                        margin-bottom: 0.5rem;
                    }
                    
                    .notification-title {
                        font-size: 0.9rem;
                    }
                    
                    .notification-message {
                        font-size: 0.8rem;
                    }
                }
                
                /* 进入动画 */
                @keyframes slideIn {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
                
                /* 退出动画 */
                @keyframes slideOut {
                    from {
                        transform: translateX(0);
                        opacity: 1;
                    }
                    to {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                }
                
                .modern-notification.entering {
                    animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }
                
                .modern-notification.exiting {
                    animation: slideOut 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }
            </style>
        `;
        document.body.appendChild(this.container);
    }

    show(options = {}) {
        const {
            type = 'info',
            title = '',
            message = '',
            duration = 3000,
            closable = true
        } = options;

        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `modern-notification ${type}`;
        
        const iconMap = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-triangle',
            warning: 'fas fa-exclamation-circle',
            info: 'fas fa-info-circle'
        };

        const titleMap = {
            success: '成功',
            error: '错误',
            warning: '警告',
            info: '提示'
        };

        const finalTitle = title || titleMap[type];
        const icon = iconMap[type];

        notification.innerHTML = `
            <div class="notification-icon ${type}">
                <i class="${icon}"></i>
            </div>
            <div class="notification-content">
                <div class="notification-title">${finalTitle}</div>
                <div class="notification-message">${message}</div>
            </div>
            ${closable ? '<button class="notification-close"><i class="fas fa-times"></i></button>' : ''}
            ${duration > 0 ? `<div class="notification-progress ${type}"></div>` : ''}
        `;

        // 添加到容器
        this.container.appendChild(notification);
        this.notifications.push(notification);

        // 处理关闭按钮
        if (closable) {
            const closeBtn = notification.querySelector('.notification-close');
            closeBtn.addEventListener('click', () => {
                this.remove(notification);
            });
        }

        // 显示动画
        requestAnimationFrame(() => {
            notification.classList.add('show', 'entering');
            setTimeout(() => {
                notification.classList.remove('entering');
            }, 300);
        });

        // 进度条动画
        if (duration > 0) {
            const progress = notification.querySelector('.notification-progress');
            if (progress) {
                progress.style.width = '100%';
                progress.style.transitionDuration = duration + 'ms';
                requestAnimationFrame(() => {
                    progress.style.width = '0%';
                });
            }

            // 自动移除
            setTimeout(() => {
                if (this.notifications.includes(notification)) {
                    this.remove(notification);
                }
            }, duration);
        }

        return notification;
    }

    remove(notification) {
        if (!this.notifications.includes(notification)) return;

        // 移除动画
        notification.classList.add('exiting');
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            const index = this.notifications.indexOf(notification);
            if (index > -1) {
                this.notifications.splice(index, 1);
            }
        }, 300);
    }

    success(message, title = '', options = {}) {
        return this.show({
            type: 'success',
            title,
            message,
            ...options
        });
    }

    error(message, title = '', options = {}) {
        return this.show({
            type: 'error',
            title,
            message,
            duration: 5000, // 错误消息显示更长时间
            ...options
        });
    }

    warning(message, title = '', options = {}) {
        return this.show({
            type: 'warning',
            title,
            message,
            ...options
        });
    }

    info(message, title = '', options = {}) {
        return this.show({
            type: 'info',
            title,
            message,
            ...options
        });
    }

    clear() {
        this.notifications.forEach(notification => {
            this.remove(notification);
        });
    }
}

// 创建全局实例
window.ModernNotification = new ModernNotification();

// 兼容性别名
if (typeof window.ModernUI === 'undefined') {
    window.ModernUI = {};
}
window.ModernUI.notification = window.ModernNotification; 