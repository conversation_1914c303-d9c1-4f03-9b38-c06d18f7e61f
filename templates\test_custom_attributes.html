<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自定义属性测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-cogs me-2"></i>自定义属性功能测试</h4>
                    </div>
                    <div class="card-body">
                        <!-- 测试说明 -->
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>测试说明</h6>
                            <p class="mb-0">此页面用于测试自定义属性的存储和显示功能。请按照以下步骤进行测试：</p>
                            <ol class="mt-2 mb-0">
                                <li>选择一个支持自定义输入的商品</li>
                                <li>在属性组中选择"自定义"选项</li>
                                <li>输入自定义值</li>
                                <li>创建订单</li>
                                <li>查看订单详情中的自定义数据显示</li>
                            </ol>
                        </div>

                        <!-- 功能状态检查 -->
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-body text-center">
                                        <i class="fas fa-database fa-2x text-success mb-2"></i>
                                        <h6>数据库存储</h6>
                                        <span class="badge bg-success">✓ 已实现</span>
                                        <p class="small text-muted mt-2">
                                            OrderItem.product_attributes (JSON)
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-body text-center">
                                        <i class="fas fa-calculator fa-2x text-success mb-2"></i>
                                        <h6>价格计算</h6>
                                        <span class="badge bg-success">✓ 已实现</span>
                                        <p class="small text-muted mt-2">
                                            支持公式、固定值、百分比
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-body text-center">
                                        <i class="fas fa-eye fa-2x text-success mb-2"></i>
                                        <h6>订单显示</h6>
                                        <span class="badge bg-success">✓ 已优化</span>
                                        <p class="small text-muted mt-2">
                                            自定义数据特殊标记显示
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-body text-center">
                                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                        <h6>数据验证</h6>
                                        <span class="badge bg-success">✓ 已实现</span>
                                        <p class="small text-muted mt-2">
                                            前端+后端双重验证
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 测试工具 -->
                        <div class="mt-4">
                            <h6><i class="fas fa-tools me-2"></i>测试工具</h6>
                            
                            <!-- 订单属性查看器 -->
                            <div class="card border-light">
                                <div class="card-body">
                                    <h6>订单属性查看器</h6>
                                    <div class="input-group mb-3">
                                        <input type="number" class="form-control" id="orderIdInput" placeholder="输入订单ID">
                                        <button class="btn btn-outline-primary" onclick="debugOrderAttributes()">
                                            <i class="fas fa-search me-1"></i>查看属性
                                        </button>
                                    </div>
                                    <div id="debugResult" class="mt-3"></div>
                                </div>
                            </div>

                            <!-- 自定义输入验证器 -->
                            <div class="card border-light mt-3">
                                <div class="card-body">
                                    <h6>自定义输入验证器</h6>
                                    <div class="row g-2">
                                        <div class="col-md-4">
                                            <input type="text" class="form-control" id="groupNameInput" placeholder="属性组名称">
                                        </div>
                                        <div class="col-md-4">
                                            <input type="text" class="form-control" id="customValueInput" placeholder="自定义值">
                                        </div>
                                        <div class="col-md-4">
                                            <button class="btn btn-outline-success w-100" onclick="validateCustomInput()">
                                                <i class="fas fa-check me-1"></i>验证
                                            </button>
                                        </div>
                                    </div>
                                    <div id="validateResult" class="mt-3"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 快速链接 -->
                        <div class="mt-4">
                            <h6><i class="fas fa-link me-2"></i>快速链接</h6>
                            <div class="d-grid gap-2 d-md-flex">
                                <a href="{{ url_for('main.products') }}" class="btn btn-primary">
                                    <i class="fas fa-shopping-bag me-1"></i>商品列表
                                </a>
                                <a href="{{ url_for('main.orders') }}" class="btn btn-info">
                                    <i class="fas fa-list me-1"></i>我的订单
                                </a>
                                <a href="{{ url_for('admin.attribute_groups') }}" class="btn btn-secondary">
                                    <i class="fas fa-cog me-1"></i>属性管理
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 调试订单属性
        async function debugOrderAttributes() {
            const orderId = document.getElementById('orderIdInput').value;
            const resultDiv = document.getElementById('debugResult');
            
            if (!orderId) {
                resultDiv.innerHTML = '<div class="alert alert-warning">请输入订单ID</div>';
                return;
            }
            
            try {
                const response = await fetch(`/api/debug/order-attributes/${orderId}`);
                const result = await response.json();
                
                if (result.success) {
                    let html = '<div class="alert alert-success">订单属性数据：</div>';
                    html += '<pre class="bg-light p-3 rounded">' + JSON.stringify(result.data, null, 2) + '</pre>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-danger">错误：${result.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="alert alert-danger">请求失败：${error.message}</div>`;
            }
        }
        
        // 验证自定义输入
        async function validateCustomInput() {
            const groupName = document.getElementById('groupNameInput').value;
            const customValue = document.getElementById('customValueInput').value;
            const resultDiv = document.getElementById('validateResult');
            
            if (!groupName || !customValue) {
                resultDiv.innerHTML = '<div class="alert alert-warning">请输入属性组名称和自定义值</div>';
                return;
            }
            
            try {
                const response = await fetch('/api/validate-custom-input', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        group_name: groupName,
                        custom_value: customValue
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `<div class="alert alert-success">验证通过：${result.message}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-danger">验证失败：${result.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="alert alert-danger">请求失败：${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
