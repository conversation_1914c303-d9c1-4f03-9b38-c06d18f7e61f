{% extends "base_modern.html" %}

{% block title %}订单支付 - {{ site_config.site_name }}{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/payment.css') }}" rel="stylesheet">
<style>
    /* 支付页面专用样式 */
    .payment-container {
        max-width: 800px;
        margin: 2rem auto;
        padding: 0 1rem;
    }
    
    .payment-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-radius: var(--border-radius-xl);
        box-shadow: var(--shadow-xl);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
    }
    
    .payment-header {
        background: var(--primary-gradient);
        color: white;
        padding: 2rem;
        text-align: center;
        position: relative;
    }
    
    .payment-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cdefs%3E%3Cpattern id='grain' width='100' height='100' patternUnits='userSpaceOnUse'%3E%3Ccircle cx='25' cy='25' r='1' fill='white' opacity='0.1'/%3E%3Ccircle cx='75' cy='75' r='1' fill='white' opacity='0.1'/%3E%3Ccircle cx='50' cy='10' r='0.5' fill='white' opacity='0.05'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='100' height='100' fill='url(%23grain)'/%3E%3C/svg%3E");
        pointer-events: none;
    }
    
    .payment-amount {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0.5rem 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .order-info {
        padding: 2rem;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }
    
    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }
    
    .info-row:last-child {
        border-bottom: none;
        font-weight: 600;
        font-size: 1.1rem;
    }
    
    .info-label {
        color: #6b7280;
        font-weight: 500;
    }
    
    .info-value {
        color: #1f2937;
        font-weight: 600;
    }
    
    .payment-methods {
        padding: 2rem;
    }
    
    .method-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }
    
    .payment-method {
        background: rgba(255, 255, 255, 0.8);
        border: 2px solid transparent;
        border-radius: var(--border-radius-lg);
        padding: 1.5rem;
        cursor: pointer;
        transition: var(--transition-base);
        position: relative;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
    
    .payment-method::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
        pointer-events: none;
    }
    
    .payment-method:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        border-color: rgba(102, 126, 234, 0.3);
    }
    
    .payment-method.selected {
        border-color: #667eea;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }
    
    .method-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .method-name {
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 0.5rem;
    }
    
    .method-desc {
        font-size: 0.875rem;
        color: #6b7280;
    }
    
    .payment-actions {
        padding: 2rem;
        background: rgba(248, 250, 252, 0.8);
        display: flex;
        gap: 1rem;
        justify-content: center;
    }
    
    .btn-payment {
        background: var(--primary-gradient);
        color: white;
        border: none;
        border-radius: var(--border-radius-lg);
        padding: 1rem 2rem;
        font-weight: 600;
        font-size: 1.1rem;
        cursor: pointer;
        transition: var(--transition-base);
        position: relative;
        overflow: hidden;
        min-width: 150px;
    }
    
    .btn-payment::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: var(--transition-base);
    }
    
    .btn-payment:hover::before {
        left: 100%;
    }
    
    .btn-payment:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
    }
    
    .btn-payment:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }
    
    .btn-cancel {
        background: rgba(107, 114, 128, 0.1);
        color: #6b7280;
        border: 1px solid rgba(107, 114, 128, 0.2);
        border-radius: var(--border-radius-lg);
        padding: 1rem 2rem;
        font-weight: 500;
        cursor: pointer;
        transition: var(--transition-base);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
    
    .btn-cancel:hover {
        background: rgba(107, 114, 128, 0.2);
        color: #4b5563;
        text-decoration: none;
        transform: translateY(-1px);
    }
    
    /* 加载状态 */
    .payment-loading {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(5px);
        z-index: 9999;
        align-items: center;
        justify-content: center;
    }
    
    .loading-content {
        background: white;
        border-radius: var(--border-radius-xl);
        padding: 2rem;
        text-align: center;
        box-shadow: var(--shadow-2xl);
        max-width: 300px;
    }
    
    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid #f3f4f6;
        border-top: 4px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 1rem;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
        .payment-container {
            margin: 1rem auto;
            padding: 0 0.5rem;
        }
        
        .payment-header {
            padding: 1.5rem 1rem;
        }
        
        .payment-amount {
            font-size: 2rem;
        }
        
        .order-info,
        .payment-methods,
        .payment-actions {
            padding: 1.5rem 1rem;
        }
        
        .method-grid {
            grid-template-columns: 1fr;
        }
        
        .payment-actions {
            flex-direction: column;
        }
        
        .btn-payment,
        .btn-cancel {
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="payment-container page-enter">
    <div class="payment-card">
        <!-- 支付头部 -->
        <div class="payment-header">
            <div style="position: relative; z-index: 1;">
                <i class="fas fa-credit-card" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.9;"></i>
                <h1 style="margin: 0; font-size: 1.8rem; font-weight: 600;">订单支付</h1>
                <div class="payment-amount">¥{{ "%.2f"|format(order.final_amount) }}</div>
                <p style="opacity: 0.9; margin: 0;">订单号：{{ order.order_no }}</p>
            </div>
        </div>
        
        <!-- 订单信息 -->
        <div class="order-info">
            <h3 style="margin-bottom: 1rem; color: #1f2937;">
                <i class="fas fa-file-invoice" style="margin-right: 0.5rem; color: #667eea;"></i>
                订单详情
            </h3>
            
            {% for item in order.order_items %}
            <div class="info-row">
                <span class="info-label">商品名称</span>
                <span class="info-value">{{ item.product_name }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">商品规格</span>
                <span class="info-value">
                    {% if item.product_attributes %}
                        {% for attr_name, attr_value in item.product_attributes.items() %}
                            {{ attr_name }}: {{ attr_value }}{% if not loop.last %} | {% endif %}
                        {% endfor %}
                    {% else %}
                        默认规格
                    {% endif %}
                </span>
            </div>
            <div class="info-row">
                <span class="info-label">购买数量</span>
                <span class="info-value">{{ item.quantity }} 件</span>
            </div>
            <div class="info-row">
                <span class="info-label">单价</span>
                <span class="info-value">¥{{ "%.2f"|format(item.unit_price) }}</span>
            </div>
            {% endfor %}
            
            <div class="info-row">
                <span class="info-label">收货人</span>
                <span class="info-value">{{ order.shipping_name }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">联系电话</span>
                <span class="info-value">{{ order.shipping_phone }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">收货地址</span>
                <span class="info-value">{{ order.shipping_address }}</span>
            </div>
            <div class="info-row" style="color: #667eea;">
                <span class="info-label">应付金额</span>
                <span class="info-value">¥{{ "%.2f"|format(order.final_amount) }}</span>
            </div>
        </div>
        
        <!-- 支付方式选择 -->
        <div class="payment-methods">
            <h3 style="margin-bottom: 1rem; color: #1f2937;">
                <i class="fas fa-wallet" style="margin-right: 0.5rem; color: #667eea;"></i>
                选择支付方式
            </h3>
            
            <div class="method-grid">
                {% for payment_type in payment_types %}
                <div class="payment-method" data-payment-type="{{ payment_type.code }}">
                    <i class="{{ payment_type.icon }} method-icon" style="color: {{ payment_type.color }};"></i>
                    <div class="method-name">{{ payment_type.name }}</div>
                    <div class="method-desc">安全便捷的支付方式</div>
                </div>
                {% endfor %}
            </div>
        </div>
        
        <!-- 支付操作 -->
        <div class="payment-actions">
            <a href="{{ url_for('main.order_detail', id=order.id) }}" class="btn-cancel">
                <i class="fas fa-arrow-left" style="margin-right: 0.5rem;"></i>
                返回订单
            </a>
            <button id="payBtn" class="btn-payment" disabled>
                <i class="fas fa-lock" style="margin-right: 0.5rem;"></i>
                确认支付
            </button>
        </div>
    </div>
</div>

<!-- 支付加载状态 -->
<div id="paymentLoading" class="payment-loading">
    <div class="loading-content">
        <div class="loading-spinner"></div>
        <h4 style="margin: 0; color: #1f2937;">正在跳转支付页面</h4>
        <p style="margin: 0.5rem 0 0; color: #6b7280;">请稍候...</p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/payment.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 支付方式选择
    const paymentMethods = document.querySelectorAll('.payment-method');
    const payBtn = document.getElementById('payBtn');
    const paymentLoading = document.getElementById('paymentLoading');
    let selectedPaymentType = null;
    
    // 支付方式选择事件
    paymentMethods.forEach(method => {
        method.addEventListener('click', function() {
            // 移除其他选中状态
            paymentMethods.forEach(m => m.classList.remove('selected'));
            
            // 添加选中状态
            this.classList.add('selected');
            selectedPaymentType = this.dataset.paymentType;
            
            // 启用支付按钮
            payBtn.disabled = false;
            payBtn.innerHTML = `<i class="fas fa-credit-card" style="margin-right: 0.5rem;"></i>使用${this.querySelector('.method-name').textContent}支付`;
        });
    });
    
    // 支付按钮点击事件
    payBtn.addEventListener('click', function() {
        if (!selectedPaymentType) {
            alert('请选择支付方式');
            return;
        }
        
        // 显示加载状态
        paymentLoading.style.display = 'flex';
        
        // 发起支付请求
        fetch(`/api/orders/{{ order.id }}/pay`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                payment_type: selectedPaymentType
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 跳转到支付页面
                window.location.href = data.payment_url;
            } else {
                // 隐藏加载状态
                paymentLoading.style.display = 'none';
                alert(data.message || '支付失败，请重试');
            }
        })
        .catch(error => {
            console.error('支付请求失败:', error);
            paymentLoading.style.display = 'none';
            alert('网络错误，请重试');
        });
    });
    
    // 默认选择第一个支付方式
    if (paymentMethods.length > 0) {
        paymentMethods[0].click();
    }
});
</script>
{% endblock %} 