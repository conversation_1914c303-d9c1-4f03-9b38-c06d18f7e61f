{% extends "base_modern.html" %}

{% block title %}订单详情 - {{ order.order_no }} - {{ site_config.site_name }}{% endblock %}
{% block description %}订单详情 - {{ order.order_no }}{% endblock %}

{% block extra_css %}
<style>
/* 自定义属性样式 */
.attribute-details .badge {
    font-size: 0.85em;
    margin-right: 0.5rem;
    margin-bottom: 0.25rem;
}

.attribute-details .badge.bg-warning {
    color: #000 !important;
    border: 1px solid #ffc107;
}

.attribute-details .badge.bg-light {
    border: 1px solid #dee2e6;
}

.product-specs {
    line-height: 1.6;
}

.product-specs .badge {
    font-size: 0.75em;
    vertical-align: middle;
}
    /* ========== Order Detail Styles ========== */
    .order-detail-section {
        padding: 2rem 0 4rem;
        background: var(--gradient-light);
        min-height: 100vh;
    }
    
    .breadcrumb-modern {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-radius: var(--border-radius-xl);
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: var(--shadow-sm);
    }
    
    .breadcrumb-modern .breadcrumb {
        margin: 0;
        background: none;
        padding: 0;
    }
    
    .breadcrumb-modern .breadcrumb-item a {
        color: var(--primary-600);
        text-decoration: none;
        transition: var(--transition-base);
    }
    
    .breadcrumb-modern .breadcrumb-item a:hover {
        color: var(--primary-700);
    }
    
    .order-container {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-radius: var(--border-radius-2xl);
        padding: 2rem;
        box-shadow: var(--shadow-xl);
        border: 1px solid rgba(255, 255, 255, 0.3);
        margin-bottom: 2rem;
    }
    
    .order-header {
        background: var(--primary-gradient);
        color: white;
        border-radius: var(--border-radius-xl);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }
    
    .order-header::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 200px;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        transform: skewX(-20deg);
        transform-origin: top right;
    }
    
    .order-number {
        font-family: 'Courier New', monospace;
        font-size: 1.5rem;
        font-weight: bold;
        letter-spacing: 2px;
    }
    
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.5rem 1rem;
        border-radius: var(--border-radius-xl);
        font-weight: 600;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .status-pending { background: rgba(255, 193, 7, 0.2); color: #856404; border: 2px solid rgba(255, 193, 7, 0.5); }
    .status-paid { background: rgba(13, 202, 240, 0.2); color: #055160; border: 2px solid rgba(13, 202, 240, 0.5); }
    .status-processing { background: rgba(102, 126, 234, 0.2); color: #3730a3; border: 2px solid rgba(102, 126, 234, 0.5); }
    .status-shipped { background: rgba(108, 117, 125, 0.2); color: #495057; border: 2px solid rgba(108, 117, 125, 0.5); }
    .status-delivered { background: rgba(25, 135, 84, 0.2); color: #0f5132; border: 2px solid rgba(25, 135, 84, 0.5); }
    .status-cancelled { background: rgba(220, 53, 69, 0.2); color: #842029; border: 2px solid rgba(220, 53, 69, 0.5); }
    
    .info-card {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-radius: var(--border-radius-xl);
        padding: 1.5rem;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: var(--shadow-sm);
        height: 100%;
    }
    
    .info-card-header {
        color: var(--gray-800);
        font-weight: 600;
        font-size: 1.1rem;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid var(--primary-100);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .info-row {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        gap: 1rem;
    }
    
    .info-row:last-child {
        border-bottom: none;
    }
    
    .info-label {
        color: var(--gray-600);
        font-weight: 500;
        min-width: fit-content;
        white-space: nowrap;
    }

    .info-value {
        color: var(--gray-800);
        font-weight: 600;
        flex: 1;
    }
    
    .price-highlight {
        color: var(--primary-600);
        font-size: 1.2rem;
        font-weight: 700;
    }
    
    .product-card {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-radius: var(--border-radius-xl);
        padding: 1.5rem;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: var(--shadow-sm);
        margin-bottom: 1rem;
        transition: var(--transition-base);
    }
    
    .product-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }
    
    .product-image {
        width: 80px;
        height: 80px;
        border-radius: var(--border-radius-lg);
        object-fit: cover;
        background: var(--gradient-light);
    }
    
    .product-info {
        flex: 1;
        padding-left: 1rem;
    }
    
    .product-name {
        font-weight: 600;
        color: var(--gray-800);
        margin-bottom: 0.5rem;
        font-size: 1.1rem;
    }
    
    .product-specs {
        color: var(--gray-600);
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }
    
    .product-quantity {
        color: var(--gray-700);
        font-weight: 500;
    }
    
    .product-price {
        text-align: right;
        padding-left: 1rem;
    }
    
    .unit-price {
        color: var(--gray-600);
        font-size: 0.9rem;
    }
    
    .total-price {
        color: var(--primary-600);
        font-weight: 700;
        font-size: 1.2rem;
    }
    
    .address-card {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(255, 255, 255, 0.9));
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: var(--border-radius-xl);
        padding: 1.5rem;
        position: relative;
        overflow: hidden;
    }
    
    .address-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-gradient);
    }

    /* 收货信息特殊样式 */
    .address-card .info-row {
        border-bottom: none;
        padding: 0.5rem 0;
    }

    .address-card .info-label {
        min-width: 80px;
        margin-right: 0.5rem;
    }

    .address-card .info-value {
        flex: none;
    }
    
    .action-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        justify-content: center;
        margin-top: 2rem;
    }
    
    .btn-modern {
        background: var(--primary-gradient);
        color: white;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: var(--border-radius-xl);
        font-weight: 600;
        text-decoration: none;
        transition: var(--transition-base);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        color: white;
    }
    
    .btn-secondary {
        background: var(--gradient-light);
        color: var(--gray-700);
        border: 2px solid var(--gray-300);
    }
    
    .btn-secondary:hover {
        background: var(--gray-100);
        border-color: var(--gray-400);
        color: var(--gray-800);
    }
    
    /* ========== Responsive Design ========== */
    @media (max-width: 768px) {
        .order-container {
            padding: 1rem;
        }
        
        .order-header {
            padding: 1.5rem;
        }
        
        .order-number {
            font-size: 1.2rem;
        }
        
        .product-card {
            padding: 1rem;
        }
        
        .product-image {
            width: 60px;
            height: 60px;
        }
        
        .product-info {
            padding-left: 0.75rem;
        }
        
        .action-buttons {
            flex-direction: column;
        }
        
        .btn-modern {
            width: 100%;
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="order-detail-section">
    <div class="container">
        <!-- 面包屑导航 -->
        <nav aria-label="breadcrumb" class="animate-on-scroll">
            <div class="breadcrumb-modern">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('main.index') }}">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('main.orders') }}">我的订单</a>
                    </li>
                    <li class="breadcrumb-item active">订单详情</li>
                </ol>
            </div>
        </nav>
        
        <!-- 订单头部 -->
        <div class="order-container animate-on-scroll delay-1">
            <div class="order-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="mb-2">订单详情</h1>
                        <div class="order-number">{{ order.order_no }}</div>
                        <p class="mb-0 mt-2 opacity-90">下单时间：{{ format_china_date(order.created_at) }}</p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="status-badge status-{{ order.status }}">
                            <i class="fas fa-circle me-2" style="font-size: 0.5rem;"></i>
                            {{ order.get_status_display() }}
                        </div>
                        <div class="mt-2">
                            <div class="status-badge" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid rgba(255,255,255,0.3);">
                                <i class="fas fa-credit-card me-2"></i>
                                {{ order.get_payment_status_display() }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 订单信息 -->
            <div class="row mb-4">
                <div class="col-md-6 mb-3">
                    <div class="info-card">
                        <div class="info-card-header">
                            <i class="fas fa-receipt text-primary"></i>
                            订单信息
                        </div>
                        <div class="info-row">
                            <span class="info-label">订单编号</span>
                            <span class="info-value">{{ order.order_no }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">订单状态</span>
                            <span class="info-value">{{ order.get_status_display() }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">支付状态</span>
                            <span class="info-value">{{ order.get_payment_status_display() }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">下单时间</span>
                            <span class="info-value">{{ format_china_time(order.created_at) }}</span>
                        </div>
                        {% if order.payment_method %}
                        <div class="info-row">
                            <span class="info-label">支付方式</span>
                            <span class="info-value">{{ order.payment_method }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <div class="info-card">
                        <div class="info-card-header">
                            <i class="fas fa-calculator text-success"></i>
                            费用明细
                        </div>
                        <div class="info-row">
                            <span class="info-label">商品总额</span>
                            <span class="info-value">¥{{ "%.2f"|format(order.total_amount) }}</span>
                        </div>
                        {% if order.discount_amount > 0 %}
                        <div class="info-row">
                            <span class="info-label">优惠金额</span>
                            <span class="info-value text-success">-¥{{ "%.2f"|format(order.discount_amount) }}</span>
                        </div>
                        {% endif %}
                        <div class="info-row">
                            <span class="info-label">运费</span>
                            <span class="info-value">
                                {% set shipping_fee = order.final_amount - order.total_amount + order.discount_amount %}
                                {% if shipping_fee > 0 %}
                                    ¥{{ "%.2f"|format(shipping_fee) }}
                                {% else %}
                                    免运费
                                {% endif %}
                            </span>
                        </div>
                        <div class="info-row" style="border-top: 2px solid var(--primary-200); margin-top: 0.5rem; padding-top: 1rem;">
                            <span class="info-label" style="font-size: 1.1rem; font-weight: 600;">实付金额</span>
                            <span class="info-value price-highlight">¥{{ "%.2f"|format(order.final_amount) }}</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 收货地址 -->
            {% if order.shipping_address %}
            <div class="address-card mb-4">
                <div class="info-card-header">
                    <i class="fas fa-map-marker-alt text-primary"></i>
                    收货信息
                </div>
                <div class="row">
                    <div class="col-md-2">
                        <div class="info-row">
                            <span class="info-label">收货人</span>
                            <span class="info-value">{{ order.shipping_name or order.recipient_name }}</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-row">
                            <span class="info-label">联系电话</span>
                            <span class="info-value">{{ order.shipping_phone or order.recipient_phone }}</span>
                        </div>
                    </div>
                    <div class="col-md-7">
                        <div class="info-row">
                            <span class="info-label">收货地址</span>
                            <span class="info-value">{{ order.get_address_parts().full_address }}</span>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
            
            <!-- 订单商品 -->
            <div class="info-card-header">
                <i class="fas fa-shopping-bag text-primary"></i>
                订单商品
            </div>
            
            {% for item in order.order_items %}
            <div class="product-card">
                <div class="d-flex align-items-center">
                    {% if item.product %}
                    <img src="{{ item.product.get_main_image() }}" class="product-image" alt="{{ item.product_name }}">
                    {% else %}
                    <div class="product-image d-flex align-items-center justify-content-center bg-light">
                        <i class="fas fa-image text-muted"></i>
                    </div>
                    {% endif %}
                    
                    <div class="product-info">
                        <div class="product-name">{{ item.product_name }}</div>
                        {% if item.get_attributes_display() %}
                        <!-- 商品规格显示 -->
                        {% set attr_details = item.get_attributes_detail() %}
                        {% if attr_details %}
                        <div class="product-specs">
                            <strong>规格：</strong>
                            {% if item.has_custom_attributes() %}
                            <span class="badge bg-info ms-2">包含自定义</span>
                            {% endif %}
                        </div>
                        <div class="attribute-details mt-2">
                            <div class="row g-2">
                                {% for attr in attr_details %}
                                <div class="col-auto">
                                    {% if attr.is_custom %}
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-edit me-1"></i>{{ attr.name }}: {{ attr.display_value }}
                                    </span>
                                    {% else %}
                                    <span class="badge bg-light text-dark">
                                        {{ attr.name }}: {{ attr.value }}
                                    </span>
                                    {% endif %}
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% else %}
                        <!-- 如果没有详细属性，显示基本信息 -->
                        <div class="product-specs">
                            <strong>规格：</strong>{{ item.get_attributes_display() }}
                            {% if item.has_custom_attributes() %}
                            <span class="badge bg-info ms-2">包含自定义</span>
                            {% endif %}
                        </div>
                        {% endif %}
                        {% endif %}
                        <div class="product-quantity">数量：{{ item.quantity }} 件</div>
                        {% if not item.product %}
                        <small class="text-danger">商品已删除</small>
                        {% endif %}
                    </div>
                    
                    <div class="product-price">
                        <div class="unit-price">单价：¥{{ "%.2f"|format(item.unit_price) }}</div>
                        <div class="total-price">¥{{ "%.2f"|format(item.total_price) }}</div>
                    </div>
                </div>
            </div>
            {% endfor %}
            
            <!-- 订单备注 -->
            {% if order.notes %}
            <div class="info-card mt-4">
                <div class="info-card-header">
                    <i class="fas fa-sticky-note text-warning"></i>
                    订单备注
                </div>
                <p class="mb-0 text-muted">{{ order.notes }}</p>
            </div>
            {% endif %}
            
            <!-- 操作按钮 -->
            <div class="action-buttons">
                <a href="{{ url_for('main.orders') }}" class="btn-modern btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回订单列表
                </a>
                
                {% if order.can_pay() %}
                <a href="{{ url_for('main.payment_page', order_id=order.id) }}" class="btn-modern">
                    <i class="fas fa-credit-card"></i>
                    立即支付
                </a>
                <button onclick="syncPaymentStatus('{{ order.id }}')" class="btn-modern btn-secondary">
                    <i class="fas fa-sync-alt"></i>
                    同步支付状态
                </button>
                {% endif %}
                
                {% if order.can_cancel() %}
                <a href="#" class="btn-modern btn-secondary" onclick="cancelOrder('{{ order.id }}')">
                    <i class="fas fa-times"></i>
                    取消订单
                </a>
                {% endif %}
                
                <a href="#" class="btn-modern btn-secondary" onclick="contactService()">
                    <i class="fas fa-comments"></i>
                    联系客服
                </a>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
// 同步支付状态
function syncPaymentStatus(orderId) {
    const button = event.target;
    const originalText = button.innerHTML;
    
    // 更新按钮状态
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 同步中...';
    
    fetch(`/api/orders/${orderId}/sync-payment`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || 
                          '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (data.updated) {
                showNotification('success', `支付状态同步成功！订单状态已更新为：${data.new_status}`);
                setTimeout(() => {
                    location.reload();
                }, 2000);
            } else if (data.already_paid) {
                showNotification('info', '订单已经是已支付状态');
            } else {
                showNotification('info', data.message);
            }
        } else {
            showNotification('error', data.message || '同步失败');
        }
    })
    .catch(error => {
        console.error('同步支付状态失败:', error);
        showNotification('error', '网络错误，请重试');
    })
    .finally(() => {
        // 恢复按钮状态
        button.disabled = false;
        button.innerHTML = originalText;
    });
}

// 取消订单
function cancelOrder(orderId) {
    if (confirm('确认要取消此订单吗？取消后不可恢复！')) {
        fetch(`/api/orders/${orderId}/cancel`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || 
                              '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('success', '订单已取消');
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showNotification('error', data.message || '取消失败');
            }
        })
        .catch(error => {
            console.error('取消订单失败:', error);
            showNotification('error', '取消失败，请重试');
        });
    }
}

// 联系客服
function contactService() {
    const phone = '{{ site_config.contact_phone }}';
    const email = '{{ site_config.contact_email }}';
    
    const message = `您可以通过以下方式联系我们：\n\n电话：${phone}\n邮箱：${email}`;
    
    if (confirm(message + '\n\n点击确定拨打客服电话')) {
        window.open(`tel:${phone}`);
    }
}

function showNotification(type, message) {
    if (window.ModernUI && window.ModernUI.notification) {
        window.ModernUI.notification[type](message);
    } else if (window.ModernNotification) {
        window.ModernNotification[type](message);
    } else {
        // 简化版通知，使用浮动提示
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 9999;
            min-width: 250px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
            transform: translateX(100%);
        `;
        
        if (type === 'success') {
            notification.style.background = 'linear-gradient(135deg, #4ade80, #22c55e)';
            notification.innerHTML = `<i class="fas fa-check-circle" style="margin-right: 8px;"></i>${message}`;
        } else if (type === 'error') {
            notification.style.background = 'linear-gradient(135deg, #ef4444, #dc2626)';
            notification.innerHTML = `<i class="fas fa-exclamation-circle" style="margin-right: 8px;"></i>${message}`;
        } else if (type === 'info') {
            notification.style.background = 'linear-gradient(135deg, #3b82f6, #2563eb)';
            notification.innerHTML = `<i class="fas fa-info-circle" style="margin-right: 8px;"></i>${message}`;
        } else if (type === 'warning') {
            notification.style.background = 'linear-gradient(135deg, #f59e0b, #d97706)';
            notification.innerHTML = `<i class="fas fa-exclamation-triangle" style="margin-right: 8px;"></i>${message}`;
        } else {
            notification.style.background = 'linear-gradient(135deg, #6b7280, #4b5563)';
            notification.innerHTML = `<i class="fas fa-bell" style="margin-right: 8px;"></i>${message}`;
        }
        
        document.body.appendChild(notification);
        
        // 动画显示
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // 自动消失
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, 4000);
    }
}

// 滚动动画
document.addEventListener('DOMContentLoaded', function() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animated');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
    });
});
</script>
<script src="{{ url_for('static', filename='js/custom-attributes-display.js') }}"></script>
{% endblock %}
