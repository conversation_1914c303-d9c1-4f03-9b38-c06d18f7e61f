{% extends "admin/base.html" %}

{% block title %}仪表盘 - 管理后台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">仪表盘</h1>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            总用户数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_users }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            商品总数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_products }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-box fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            订单总数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_orders }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            分类总数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_categories }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-tags fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 最近订单 -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">最近订单</h6>
                <a href="{{ url_for('admin.orders') }}" class="btn btn-sm btn-primary">查看全部</a>
            </div>
            <div class="card-body">
                {% if recent_orders %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>订单号</th>
                                <th>用户</th>
                                <th>金额</th>
                                <th>状态</th>
                                <th>时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in recent_orders %}
                            <tr>
                                <td>{{ order.order_no }}</td>
                                <td>{{ order.user.real_name or order.user.username }}</td>
                                <td>¥{{ "%.2f"|format(order.final_amount) }}</td>
                                <td>
                                    <span class="badge bg-{{ 'success' if order.status == 'paid' else 'warning' }}">
                                        {{ order.get_status_display() }}
                                    </span>
                                </td>
                                <td>{{ format_china_datetime_short(order.created_at) }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-3">
                    <i class="fas fa-inbox fa-2x mb-2"></i>
                    <p>暂无订单</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- 最新商品 -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">最新商品</h6>
                <a href="{{ url_for('admin.products') }}" class="btn btn-sm btn-primary">查看全部</a>
            </div>
            <div class="card-body">
                {% if recent_products %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>商品名称</th>
                                <th>分类</th>
                                <th>价格</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in recent_products %}
                            <tr>
                                <td>{{ product.name }}</td>
                                <td>{{ product.category.name if product.category else '-' }}</td>
                                <td>¥{{ "%.2f"|format(product.base_price) }}</td>
                                <td>
                                    <span class="badge bg-{{ 'success' if product.is_active else 'secondary' }}">
                                        {{ '启用' if product.is_active else '禁用' }}
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-3">
                    <i class="fas fa-box fa-2x mb-2"></i>
                    <p>暂无商品</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
