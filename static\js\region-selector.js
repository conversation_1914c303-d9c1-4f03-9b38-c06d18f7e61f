/**
 * 标准化省市县三级联动选择器组件
 * 使用数据库中的真实数据，通过AJAX加载
 */
class RegionSelector {
    /**
     * 初始化省市县选择器
     * @param {Object} options 配置选项
     * @param {string} options.provinceSelector 省份选择器
     * @param {string} options.citySelector 城市选择器
     * @param {string} options.districtSelector 区县选择器
     * @param {Function} options.onChange 选择变化时的回调函数
     * @param {boolean} options.useCache 是否使用缓存，默认为true
     * @param {string} options.provincePlaceholder 省份选择框占位文字
     * @param {string} options.cityPlaceholder 城市选择框占位文字
     * @param {string} options.districtPlaceholder 区县选择框占位文字
     * @param {string} options.provinceValue 省份初始值
     * @param {string} options.cityValue 城市初始值
     * @param {string} options.districtValue 区县初始值
     */
    constructor(options) {
        this.options = Object.assign({
            provinceSelector: '#province',
            citySelector: '#city',
            districtSelector: '#district',
            onChange: null,
            useCache: true,
            provincePlaceholder: '请选择省份',
            cityPlaceholder: '请选择城市',
            districtPlaceholder: '请选择区县',
            provinceValue: '',
            cityValue: '',
            districtValue: ''
        }, options);

        this.provinceSelect = document.querySelector(this.options.provinceSelector);
        this.citySelect = document.querySelector(this.options.citySelector);
        this.districtSelect = document.querySelector(this.options.districtSelector);

        if (!this.provinceSelect || !this.citySelect || !this.districtSelect) {
            console.error('RegionSelector: 未找到选择器元素');
            return;
        }

        // 数据缓存
        this.cache = {
            provinces: [],
            cities: {},
            districts: {}
        };

        this.init();
    }

    /**
     * 初始化选择器
     */
    init() {
        // 初始化选择框
        this.initSelect(this.provinceSelect, this.options.provincePlaceholder);
        this.initSelect(this.citySelect, this.options.cityPlaceholder);
        this.initSelect(this.districtSelect, this.options.districtPlaceholder);

        // 禁用城市和区县选择框
        this.citySelect.disabled = true;
        this.districtSelect.disabled = true;

        // 绑定事件
        this.provinceSelect.addEventListener('change', () => this.onProvinceChange());
        this.citySelect.addEventListener('change', () => this.onCityChange());
        this.districtSelect.addEventListener('change', () => this.onDistrictChange());

        // 加载省份数据
        this.loadProvinces();
    }

    /**
     * 初始化选择框
     * @param {HTMLSelectElement} select 选择框元素
     * @param {string} placeholder 占位文字
     */
    initSelect(select, placeholder) {
        select.innerHTML = '';
        const option = document.createElement('option');
        option.value = '';
        option.textContent = placeholder;
        select.appendChild(option);
    }

    /**
     * 加载省份数据
     */
    loadProvinces() {
        console.log('开始加载省份数据...');

        if (this.options.useCache && this.cache.provinces.length > 0) {
            console.log('使用缓存的省份数据:', this.cache.provinces.length, '个省份');
            this.renderProvinces(this.cache.provinces);
            return;
        }

        this.showLoading(this.provinceSelect);
        console.log('发送请求到: /api/regions/provinces');

        fetch('/api/regions/provinces')
            .then(response => {
                console.log('收到响应:', response.status, response.statusText);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('省份数据加载成功:', data.length, '个省份');
                this.hideLoading(this.provinceSelect);
                this.cache.provinces = data;
                this.renderProvinces(data);
            })
            .catch(error => {
                console.error('加载省份数据失败:', error);
                this.hideLoading(this.provinceSelect);
                this.showError(this.provinceSelect, '加载省份数据失败: ' + error.message);
            });
    }

    /**
     * 渲染省份数据
     * @param {Array} provinces 省份数据
     */
    renderProvinces(provinces) {
        this.initSelect(this.provinceSelect, this.options.provincePlaceholder);
        
        const fragment = document.createDocumentFragment();
        provinces.forEach(province => {
            const option = document.createElement('option');
            option.value = province.id;
            option.textContent = province.name;
            fragment.appendChild(option);
        });
        
        this.provinceSelect.appendChild(fragment);
        
        // 如果有初始值，则选中对应的选项
        if (this.options.provinceValue) {
            // 先尝试通过ID匹配
            let found = false;
            for (let i = 0; i < this.provinceSelect.options.length; i++) {
                if (this.provinceSelect.options[i].value === this.options.provinceValue) {
                    this.provinceSelect.selectedIndex = i;
                    found = true;
                    break;
                }
            }
            
            // 如果通过ID没找到，则尝试通过名称匹配
            if (!found) {
                for (let i = 0; i < this.provinceSelect.options.length; i++) {
                    if (this.provinceSelect.options[i].textContent === this.options.provinceValue) {
                        this.provinceSelect.selectedIndex = i;
                        found = true;
                        break;
                    }
                }
            }
            
            // 如果找到了对应的省份，则触发change事件
            if (found) {
                this.onProvinceChange();
            }
        }
    }

    /**
     * 省份选择变化事件
     */
    onProvinceChange() {
        const provinceId = this.provinceSelect.value;
        
        // 重置城市和区县选择框
        this.initSelect(this.citySelect, this.options.cityPlaceholder);
        this.initSelect(this.districtSelect, this.options.districtPlaceholder);
        
        // 禁用城市和区县选择框
        this.citySelect.disabled = true;
        this.districtSelect.disabled = true;
        
        if (!provinceId) {
            this.triggerChange();
            return;
        }
        
        this.loadCities(provinceId);
    }

    /**
     * 加载城市数据
     * @param {string} provinceId 省份ID
     */
    loadCities(provinceId) {
        console.log('开始加载城市数据，省份ID:', provinceId);

        if (this.options.useCache && this.cache.cities[provinceId]) {
            console.log('使用缓存的城市数据:', this.cache.cities[provinceId].length, '个城市');
            this.renderCities(this.cache.cities[provinceId]);
            return;
        }

        this.showLoading(this.citySelect);
        const url = `/api/regions/cities/${provinceId}`;
        console.log('发送请求到:', url);

        fetch(url)
            .then(response => {
                console.log('城市数据响应:', response.status, response.statusText);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('城市数据加载成功:', data.length, '个城市');
                this.hideLoading(this.citySelect);
                this.cache.cities[provinceId] = data;
                this.renderCities(data);
            })
            .catch(error => {
                console.error('加载城市数据失败:', error);
                this.hideLoading(this.citySelect);
                this.showError(this.citySelect, '加载城市数据失败: ' + error.message);
            });
    }

    /**
     * 渲染城市数据
     * @param {Array} cities 城市数据
     */
    renderCities(cities) {
        this.initSelect(this.citySelect, this.options.cityPlaceholder);
        
        const fragment = document.createDocumentFragment();
        cities.forEach(city => {
            const option = document.createElement('option');
            option.value = city.id;
            option.textContent = city.name;
            fragment.appendChild(option);
        });
        
        this.citySelect.appendChild(fragment);
        this.citySelect.disabled = cities.length === 0;
        
        // 如果有初始值，则选中对应的选项
        if (this.options.cityValue) {
            // 先尝试通过ID匹配
            let found = false;
            for (let i = 0; i < this.citySelect.options.length; i++) {
                if (this.citySelect.options[i].value === this.options.cityValue) {
                    this.citySelect.selectedIndex = i;
                    found = true;
                    break;
                }
            }
            
            // 如果通过ID没找到，则尝试通过名称匹配
            if (!found) {
                for (let i = 0; i < this.citySelect.options.length; i++) {
                    if (this.citySelect.options[i].textContent === this.options.cityValue) {
                        this.citySelect.selectedIndex = i;
                        found = true;
                        break;
                    }
                }
            }
            
            // 如果找到了对应的城市，则触发change事件
            if (found) {
                this.onCityChange();
            }
        }
        
        this.triggerChange();
    }

    /**
     * 城市选择变化事件
     */
    onCityChange() {
        const cityId = this.citySelect.value;
        
        // 重置区县选择框
        this.initSelect(this.districtSelect, this.options.districtPlaceholder);
        
        // 禁用区县选择框
        this.districtSelect.disabled = true;
        
        if (!cityId) {
            this.triggerChange();
            return;
        }
        
        this.loadDistricts(cityId);
    }

    /**
     * 加载区县数据
     * @param {string} cityId 城市ID
     */
    loadDistricts(cityId) {
        console.log('开始加载区县数据，城市ID:', cityId);

        if (this.options.useCache && this.cache.districts[cityId]) {
            console.log('使用缓存的区县数据:', this.cache.districts[cityId].length, '个区县');
            this.renderDistricts(this.cache.districts[cityId]);
            return;
        }

        this.showLoading(this.districtSelect);
        const url = `/api/regions/districts/${cityId}`;
        console.log('发送请求到:', url);

        fetch(url)
            .then(response => {
                console.log('区县数据响应:', response.status, response.statusText);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('区县数据加载成功:', data.length, '个区县');
                this.hideLoading(this.districtSelect);
                this.cache.districts[cityId] = data;
                this.renderDistricts(data);
            })
            .catch(error => {
                console.error('加载区县数据失败:', error);
                this.hideLoading(this.districtSelect);
                this.showError(this.districtSelect, '加载区县数据失败: ' + error.message);
            });
    }

    /**
     * 渲染区县数据
     * @param {Array} districts 区县数据
     */
    renderDistricts(districts) {
        this.initSelect(this.districtSelect, this.options.districtPlaceholder);
        
        const fragment = document.createDocumentFragment();
        districts.forEach(district => {
            const option = document.createElement('option');
            option.value = district.id;
            option.textContent = district.name;
            fragment.appendChild(option);
        });
        
        this.districtSelect.appendChild(fragment);
        this.districtSelect.disabled = districts.length === 0;
        
        // 如果有初始值，则选中对应的选项
        if (this.options.districtValue) {
            // 先尝试通过ID匹配
            let found = false;
            for (let i = 0; i < this.districtSelect.options.length; i++) {
                if (this.districtSelect.options[i].value === this.options.districtValue) {
                    this.districtSelect.selectedIndex = i;
                    found = true;
                    break;
                }
            }
            
            // 如果通过ID没找到，则尝试通过名称匹配
            if (!found) {
                for (let i = 0; i < this.districtSelect.options.length; i++) {
                    if (this.districtSelect.options[i].textContent === this.options.districtValue) {
                        this.districtSelect.selectedIndex = i;
                        found = true;
                        break;
                    }
                }
            }
        }
        
        this.triggerChange();
    }

    /**
     * 区县选择变化事件
     */
    onDistrictChange() {
        this.triggerChange();
    }

    /**
     * 触发选择变化事件
     */
    triggerChange() {
        if (typeof this.options.onChange === 'function') {
            const province = this.provinceSelect.options[this.provinceSelect.selectedIndex];
            const city = this.citySelect.options[this.citySelect.selectedIndex];
            const district = this.districtSelect.options[this.districtSelect.selectedIndex];
            
            this.options.onChange({
                province: {
                    id: this.provinceSelect.value,
                    name: province ? province.textContent : ''
                },
                city: {
                    id: this.citySelect.value,
                    name: city ? city.textContent : ''
                },
                district: {
                    id: this.districtSelect.value,
                    name: district ? district.textContent : ''
                }
            });
        }
    }

    /**
     * 显示加载中状态
     * @param {HTMLSelectElement} select 选择框元素
     */
    showLoading(select) {
        select.classList.add('loading');
        select.disabled = true;
    }

    /**
     * 隐藏加载中状态
     * @param {HTMLSelectElement} select 选择框元素
     */
    hideLoading(select) {
        select.classList.remove('loading');
        select.disabled = false;
    }

    /**
     * 显示错误信息
     * @param {HTMLSelectElement} select 选择框元素
     * @param {string} message 错误信息
     */
    showError(select, message) {
        const errorElement = document.createElement('div');
        errorElement.className = 'region-selector-error';
        errorElement.textContent = message;
        
        const parent = select.parentNode;
        const existingError = parent.querySelector('.region-selector-error');
        if (existingError) {
            parent.removeChild(existingError);
        }
        
        parent.appendChild(errorElement);
        
        // 3秒后自动隐藏错误信息
        setTimeout(() => {
            if (errorElement.parentNode) {
                errorElement.parentNode.removeChild(errorElement);
            }
        }, 3000);
    }

    /**
     * 获取当前选中的区域数据
     * @returns {Object} 区域数据
     */
    getSelectedRegion() {
        const province = this.provinceSelect.options[this.provinceSelect.selectedIndex];
        const city = this.citySelect.options[this.citySelect.selectedIndex];
        const district = this.districtSelect.options[this.districtSelect.selectedIndex];
        
        return {
            province: {
                id: this.provinceSelect.value,
                name: province ? province.textContent : ''
            },
            city: {
                id: this.citySelect.value,
                name: city ? city.textContent : ''
            },
            district: {
                id: this.districtSelect.value,
                name: district ? district.textContent : ''
            }
        };
    }

    /**
     * 设置选中的区域
     * @param {Object} region 区域数据
     * @param {Object} region.province 省份数据
     * @param {Object} region.city 城市数据
     * @param {Object} region.district 区县数据
     */
    setSelectedRegion(region) {
        if (region.province) {
            this.options.provinceValue = region.province.id || region.province.name || '';
        }
        
        if (region.city) {
            this.options.cityValue = region.city.id || region.city.name || '';
        }
        
        if (region.district) {
            this.options.districtValue = region.district.id || region.district.name || '';
        }
        
        // 重新加载省份数据
        this.loadProvinces();
    }

    /**
     * 重置选择器
     */
    reset() {
        this.options.provinceValue = '';
        this.options.cityValue = '';
        this.options.districtValue = '';
        
        this.initSelect(this.provinceSelect, this.options.provincePlaceholder);
        this.initSelect(this.citySelect, this.options.cityPlaceholder);
        this.initSelect(this.districtSelect, this.options.districtPlaceholder);
        
        this.citySelect.disabled = true;
        this.districtSelect.disabled = true;
        
        this.loadProvinces();
    }
}

// 如果在浏览器环境中，则将RegionSelector挂载到window对象上
if (typeof window !== 'undefined') {
    window.RegionSelector = RegionSelector;
} 