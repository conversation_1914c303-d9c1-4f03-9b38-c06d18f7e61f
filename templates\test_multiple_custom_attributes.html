<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多个自定义属性测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-10 mx-auto">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4><i class="fas fa-cogs me-2"></i>多个自定义属性测试</h4>
                    </div>
                    <div class="card-body">
                        <!-- 测试说明 -->
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>测试目标</h6>
                            <p class="mb-0">验证系统能否正确处理多个不同的自定义属性组，包括：</p>
                            <ul class="mt-2 mb-0">
                                <li>多个属性组同时支持自定义输入</li>
                                <li>不同类型的自定义输入（数字、文本、选择）</li>
                                <li>自定义数据的正确存储和显示</li>
                                <li>价格计算的准确性</li>
                            </ul>
                        </div>

                        <!-- 模拟商品属性选择 -->
                        <div class="card border-light mb-4">
                            <div class="card-header">
                                <h6><i class="fas fa-shopping-bag me-2"></i>模拟商品：测试印刷品</h6>
                            </div>
                            <div class="card-body">
                                <!-- 尺寸属性组 -->
                                <div class="mb-4">
                                    <label class="form-label"><strong>尺寸规格</strong></label>
                                    <div class="row g-2">
                                        <div class="col-md-6">
                                            <select class="form-select" id="sizeSelect" onchange="handleSizeChange()">
                                                <option value="">请选择尺寸</option>
                                                <option value="A4">A4 (210×297mm)</option>
                                                <option value="A3">A3 (297×420mm)</option>
                                                <option value="custom">自定义尺寸</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6" id="customSizeInput" style="display: none;">
                                            <input type="text" class="form-control custom-input-field" 
                                                   data-group="尺寸" placeholder="请输入自定义尺寸 (如: 300×400mm)">
                                        </div>
                                    </div>
                                </div>

                                <!-- 材质属性组 -->
                                <div class="mb-4">
                                    <label class="form-label"><strong>材质规格</strong></label>
                                    <div class="row g-2">
                                        <div class="col-md-6">
                                            <select class="form-select" id="materialSelect" onchange="handleMaterialChange()">
                                                <option value="">请选择材质</option>
                                                <option value="铜版纸">铜版纸 300g</option>
                                                <option value="哑粉纸">哑粉纸 250g</option>
                                                <option value="custom">自定义材质</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6" id="customMaterialInput" style="display: none;">
                                            <input type="text" class="form-control custom-input-field" 
                                                   data-group="材质" placeholder="请输入自定义材质">
                                        </div>
                                    </div>
                                </div>

                                <!-- 数量属性组 -->
                                <div class="mb-4">
                                    <label class="form-label"><strong>印刷数量</strong></label>
                                    <div class="row g-2">
                                        <div class="col-md-6">
                                            <select class="form-select" id="quantitySelect" onchange="handleQuantityChange()">
                                                <option value="">请选择数量</option>
                                                <option value="100">100张</option>
                                                <option value="500">500张</option>
                                                <option value="1000">1000张</option>
                                                <option value="custom">自定义数量</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6" id="customQuantityInput" style="display: none;">
                                            <input type="number" class="form-control custom-input-field" 
                                                   data-group="数量" placeholder="请输入自定义数量" min="1">
                                        </div>
                                    </div>
                                </div>

                                <!-- 工艺属性组 -->
                                <div class="mb-4">
                                    <label class="form-label"><strong>后道工艺</strong></label>
                                    <div class="row g-2">
                                        <div class="col-md-6">
                                            <select class="form-select" id="processSelect" onchange="handleProcessChange()">
                                                <option value="">请选择工艺</option>
                                                <option value="覆膜">覆膜</option>
                                                <option value="烫金">烫金</option>
                                                <option value="UV">UV上光</option>
                                                <option value="custom">自定义工艺</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6" id="customProcessInput" style="display: none;">
                                            <textarea class="form-control custom-input-field" 
                                                      data-group="工艺" placeholder="请详细描述自定义工艺要求" rows="2"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 收集的数据显示 -->
                        <div class="card border-success mb-4">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0"><i class="fas fa-database me-2"></i>收集的自定义数据</h6>
                            </div>
                            <div class="card-body">
                                <div id="collectedData">
                                    <p class="text-muted">请选择属性以查看收集的数据...</p>
                                </div>
                            </div>
                        </div>

                        <!-- 测试按钮 -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button class="btn btn-outline-secondary" onclick="clearAll()">
                                <i class="fas fa-trash me-1"></i>清空所有
                            </button>
                            <button class="btn btn-primary" onclick="testDataCollection()">
                                <i class="fas fa-vial me-1"></i>测试数据收集
                            </button>
                            <button class="btn btn-warning" onclick="testBackendProcessing()">
                                <i class="fas fa-server me-1"></i>测试后端处理
                            </button>
                            <button class="btn btn-success" onclick="simulateOrderCreation()">
                                <i class="fas fa-shopping-cart me-1"></i>模拟创建订单
                            </button>
                        </div>

                        <!-- 测试结果 -->
                        <div id="testResults" class="mt-4"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let selectedAttributes = [];
        let customData = {};

        // 处理尺寸选择
        function handleSizeChange() {
            const select = document.getElementById('sizeSelect');
            const customInput = document.getElementById('customSizeInput');
            
            if (select.value === 'custom') {
                customInput.style.display = 'block';
                customInput.querySelector('input').focus();
            } else {
                customInput.style.display = 'none';
                if (select.value) {
                    customData['尺寸'] = select.value;
                } else {
                    delete customData['尺寸'];
                }
            }
            updateDataDisplay();
        }

        // 处理材质选择
        function handleMaterialChange() {
            const select = document.getElementById('materialSelect');
            const customInput = document.getElementById('customMaterialInput');
            
            if (select.value === 'custom') {
                customInput.style.display = 'block';
                customInput.querySelector('input').focus();
            } else {
                customInput.style.display = 'none';
                if (select.value) {
                    customData['材质'] = select.value;
                } else {
                    delete customData['材质'];
                }
            }
            updateDataDisplay();
        }

        // 处理数量选择
        function handleQuantityChange() {
            const select = document.getElementById('quantitySelect');
            const customInput = document.getElementById('customQuantityInput');
            
            if (select.value === 'custom') {
                customInput.style.display = 'block';
                customInput.querySelector('input').focus();
            } else {
                customInput.style.display = 'none';
                if (select.value) {
                    customData['数量'] = select.value;
                } else {
                    delete customData['数量'];
                }
            }
            updateDataDisplay();
        }

        // 处理工艺选择
        function handleProcessChange() {
            const select = document.getElementById('processSelect');
            const customInput = document.getElementById('customProcessInput');
            
            if (select.value === 'custom') {
                customInput.style.display = 'block';
                customInput.querySelector('textarea').focus();
            } else {
                customInput.style.display = 'none';
                if (select.value) {
                    customData['工艺'] = select.value;
                } else {
                    delete customData['工艺'];
                }
            }
            updateDataDisplay();
        }

        // 监听自定义输入
        document.addEventListener('input', function(e) {
            if (e.target.classList.contains('custom-input-field')) {
                const group = e.target.dataset.group;
                const value = e.target.value.trim();
                
                if (value) {
                    customData[group] = value + ' (自定义)';
                } else {
                    delete customData[group];
                }
                updateDataDisplay();
            }
        });

        // 更新数据显示
        function updateDataDisplay() {
            const container = document.getElementById('collectedData');
            
            if (Object.keys(customData).length === 0) {
                container.innerHTML = '<p class="text-muted">请选择属性以查看收集的数据...</p>';
                return;
            }

            let html = '<div class="row g-2">';
            for (const [key, value] of Object.entries(customData)) {
                const isCustom = value.includes('(自定义)');
                const badgeClass = isCustom ? 'bg-warning text-dark' : 'bg-light text-dark';
                const icon = isCustom ? '<i class="fas fa-edit me-1"></i>' : '';
                
                html += `
                    <div class="col-auto">
                        <span class="badge ${badgeClass}">
                            ${icon}${key}: ${value}
                        </span>
                    </div>
                `;
            }
            html += '</div>';
            
            container.innerHTML = html;
        }

        // 测试数据收集
        function testDataCollection() {
            const results = document.getElementById('testResults');
            
            // 模拟前端数据收集逻辑
            const attributeQuantities = {};
            const selectedAttributes = [];
            
            // 收集自定义输入
            document.querySelectorAll('.custom-input-field').forEach(input => {
                const group = input.dataset.group;
                const value = input.value.trim();
                
                if (group && value) {
                    const customKey = `custom_group_${group.replace(/\s+/g, '_')}`;
                    attributeQuantities[customKey] = value;
                    
                    const customAttrId = `custom_${group.replace(/\s+/g, '_')}`;
                    if (!selectedAttributes.includes(customAttrId)) {
                        selectedAttributes.push(customAttrId);
                    }
                }
            });
            
            const testData = {
                customData: customData,
                attributeQuantities: attributeQuantities,
                selectedAttributes: selectedAttributes
            };
            
            results.innerHTML = `
                <div class="card border-info">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">测试结果</h6>
                    </div>
                    <div class="card-body">
                        <h6>收集的数据：</h6>
                        <pre class="bg-light p-3 rounded">${JSON.stringify(testData, null, 2)}</pre>
                        <div class="alert alert-success mt-3">
                            <i class="fas fa-check-circle me-2"></i>
                            成功收集到 ${Object.keys(customData).length} 个属性，
                            其中 ${Object.values(customData).filter(v => v.includes('(自定义)')).length} 个为自定义属性
                        </div>
                    </div>
                </div>
            `;
        }

        // 测试后端处理
        async function testBackendProcessing() {
            if (Object.keys(customData).length === 0) {
                alert('请先选择一些属性！');
                return;
            }

            const results = document.getElementById('testResults');
            results.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> 测试后端处理中...</div>';

            // 收集数据
            const attributeQuantities = {};
            const selectedAttributes = [];

            document.querySelectorAll('.custom-input-field').forEach(input => {
                const group = input.dataset.group;
                const value = input.value.trim();

                if (group && value) {
                    const customKey = `custom_group_${group.replace(/\s+/g, '_')}`;
                    attributeQuantities[customKey] = value;

                    const customAttrId = `custom_${group.replace(/\s+/g, '_')}`;
                    if (!selectedAttributes.includes(customAttrId)) {
                        selectedAttributes.push(customAttrId);
                    }
                }
            });

            const testData = {
                selected_attributes: selectedAttributes,
                attribute_quantities: attributeQuantities
            };

            try {
                const response = await fetch('/api/test/custom-data-processing', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });

                const result = await response.json();

                if (result.success) {
                    results.innerHTML = `
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">后端处理结果</h6>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>
                                    成功处理 ${result.data.summary.total_attributes} 个属性，
                                    其中 ${result.data.summary.custom_attributes} 个自定义属性
                                </div>

                                <h6>最终存储的属性数据：</h6>
                                <pre class="bg-light p-3 rounded">${JSON.stringify(result.data.product_attributes, null, 2)}</pre>

                                <h6 class="mt-3">处理日志：</h6>
                                <div class="accordion" id="logAccordion">
                                    <div class="accordion-item">
                                        <h2 class="accordion-header">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#logDetails">
                                                查看详细处理步骤
                                            </button>
                                        </h2>
                                        <div id="logDetails" class="accordion-collapse collapse" data-bs-parent="#logAccordion">
                                            <div class="accordion-body">
                                                <ol>
                                                    ${result.data.processing_log.processing_steps.map(step => `<li>${step}</li>`).join('')}
                                                </ol>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    results.innerHTML = `
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h6 class="mb-0">后端处理失败</h6>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    ${result.message}
                                </div>
                                ${result.traceback ? `<pre class="bg-light p-3 rounded small">${result.traceback}</pre>` : ''}
                            </div>
                        </div>
                    `;
                }
            } catch (error) {
                results.innerHTML = `
                    <div class="card border-danger">
                        <div class="card-header bg-danger text-white">
                            <h6 class="mb-0">网络错误</h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                请求失败：${error.message}
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        // 模拟创建订单
        function simulateOrderCreation() {
            if (Object.keys(customData).length === 0) {
                alert('请先选择一些属性！');
                return;
            }

            const results = document.getElementById('testResults');

            // 模拟订单数据
            const orderData = {
                product_id: 1,
                quantity: 1,
                selected_attributes: [],
                attribute_quantities: {},
                product_attributes: customData
            };

            // 收集自定义输入
            document.querySelectorAll('.custom-input-field').forEach(input => {
                const group = input.dataset.group;
                const value = input.value.trim();

                if (group && value) {
                    const customKey = `custom_group_${group.replace(/\s+/g, '_')}`;
                    orderData.attribute_quantities[customKey] = value;

                    const customAttrId = `custom_${group.replace(/\s+/g, '_')}`;
                    if (!orderData.selected_attributes.includes(customAttrId)) {
                        orderData.selected_attributes.push(customAttrId);
                    }
                }
            });

            results.innerHTML = `
                <div class="card border-info">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">模拟订单数据</h6>
                    </div>
                    <div class="card-body">
                        <h6>将要发送到后端的数据：</h6>
                        <pre class="bg-light p-3 rounded">${JSON.stringify(orderData, null, 2)}</pre>

                        <h6 class="mt-3">预期的存储格式：</h6>
                        <div class="alert alert-info">
                            <strong>OrderItem.product_attributes:</strong>
                            <pre class="mt-2 mb-0">${JSON.stringify(customData, null, 2)}</pre>
                        </div>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>注意：</strong>实际创建订单需要完整的收货信息和商品信息
                        </div>

                        <div class="mt-3">
                            <button class="btn btn-primary" onclick="testBackendProcessing()">
                                <i class="fas fa-server me-1"></i>测试后端处理这些数据
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        // 清空所有
        function clearAll() {
            // 重置所有选择框
            document.getElementById('sizeSelect').value = '';
            document.getElementById('materialSelect').value = '';
            document.getElementById('quantitySelect').value = '';
            document.getElementById('processSelect').value = '';
            
            // 隐藏所有自定义输入
            document.getElementById('customSizeInput').style.display = 'none';
            document.getElementById('customMaterialInput').style.display = 'none';
            document.getElementById('customQuantityInput').style.display = 'none';
            document.getElementById('customProcessInput').style.display = 'none';
            
            // 清空所有自定义输入值
            document.querySelectorAll('.custom-input-field').forEach(input => {
                input.value = '';
            });
            
            // 重置数据
            customData = {};
            selectedAttributes = [];
            
            // 更新显示
            updateDataDisplay();
            document.getElementById('testResults').innerHTML = '';
        }
    </script>
</body>
</html>
