{% extends "base.html" %}

{% block title %}商品列表 - {{ site_config.site_name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 面包屑导航 -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></li>
            <li class="breadcrumb-item active">商品列表</li>
        </ol>
    </nav>
    
    <div class="row">
        <!-- 侧边栏筛选 -->
        <div class="col-lg-3 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">商品筛选</h6>
                </div>
                <div class="card-body">
                    <!-- 分类筛选下拉菜单 -->
                    <div class="dropdown mb-4">
                        <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-tag me-2"></i>{{ current_category.name if current_category else '我是测试分类' }}
                        </button>
                        <ul class="dropdown-menu category-dropdown-menu">
                            <li><h6 class="dropdown-header">商品分类</h6></li>
                            <li><a class="category-dropdown-item {{ 'active' if not current_category else '' }}" href="{{ url_for('main.products') }}">
                                <i class="fas fa-list"></i>全部商品
                            </a></li>
                            
                            {% for category in categories %}
                            <li><a class="category-dropdown-item {{ 'active' if current_category and current_category.id == category.id else '' }}" 
                                   href="{{ url_for('main.products', category=category.id) }}">
                                <i class="fas fa-tag"></i>{{ category.name }}
                                {% if category.name == '我是测试分类' %}
                                <span class="badge bg-primary ms-2">测试</span>
                                {% endif %}
                            </a></li>
                            {% endfor %}
                            
                            <li><a class="category-dropdown-item testing" href="#">
                                <i class="fas fa-flask"></i>无废纸-两联纸
                            </a></li>
                            
                            <li><a class="category-dropdown-item" href="#">
                                <i class="fas fa-print"></i>名片印刷
                            </a></li>
                            
                            <li><a class="category-dropdown-item" href="#">
                                <i class="fas fa-book"></i>纸箱
                            </a></li>
                            
                            <li><a class="category-dropdown-item" href="#">
                                <i class="fas fa-file-alt"></i>印刷品
                            </a></li>
                            
                            <li><a class="category-dropdown-item" href="#">
                                <i class="fas fa-money-bill"></i>快递单据
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 商品列表 -->
        <div class="col-lg-9">
            <!-- 搜索和排序 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <form method="GET" class="d-flex">
                        {% if current_category %}
                        <input type="hidden" name="category" value="{{ current_category }}">
                        {% endif %}
                        <input type="text" name="search" class="form-control me-2" 
                               placeholder="搜索商品..." value="{{ search }}">
                        <button type="submit" class="btn btn-primary">搜索</button>
                    </form>
                </div>
                <div class="col-md-6">
                    <form method="GET" class="d-flex justify-content-end">
                        {% if current_category %}
                        <input type="hidden" name="category" value="{{ current_category }}">
                        {% endif %}
                        {% if search %}
                        <input type="hidden" name="search" value="{{ search }}">
                        {% endif %}
                        <select name="sort" class="form-select w-auto" onchange="this.form.submit()">
                            <option value="default" {{ 'selected' if sort == 'default' else '' }}>默认排序</option>
                            <option value="price_asc" {{ 'selected' if sort == 'price_asc' else '' }}>价格从低到高</option>
                            <option value="price_desc" {{ 'selected' if sort == 'price_desc' else '' }}>价格从高到低</option>
                            <option value="name" {{ 'selected' if sort == 'name' else '' }}>按名称排序</option>
                            <option value="newest" {{ 'selected' if sort == 'newest' else '' }}>最新商品</option>
                        </select>
                    </form>
                </div>
            </div>
            
            <!-- 商品网格 -->
            {% if products.items %}
            <div class="row">
                {% for product in products.items %}
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card product-card h-100">
                        <div class="product-image">
                            <img src="{{ product.get_main_image() }}" class="card-img-top" 
                                 alt="{{ product.name }}" style="height: 200px; object-fit: cover;">
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title">{{ product.name }}</h6>
                            <p class="card-text text-muted small flex-grow-1">
                                {{ product.description[:80] }}{% if product.description|length > 80 %}...{% endif %}
                            </p>
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="price-tag">¥{{ "%.2f"|format(product.base_price) }}</span>
                                    <small class="text-muted">起/{{ product.unit }}</small>
                                </div>
                                <a href="{{ url_for('main.product_detail', id=product.id) }}" 
                                   class="btn btn-primary w-100">查看详情</a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            
            <!-- 分页 -->
            {% if products.pages > 1 %}
            <nav aria-label="商品分页">
                <ul class="pagination justify-content-center">
                    {% if products.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('main.products', page=products.prev_num, 
                           category=current_category, search=search, sort=sort) }}">上一页</a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in products.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != products.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('main.products', page=page_num, 
                                   category=current_category, search=search, sort=sort) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if products.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('main.products', page=products.next_num, 
                           category=current_category, search=search, sort=sort) }}">下一页</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5>没有找到相关商品</h5>
                <p class="text-muted">请尝试其他搜索条件或浏览其他分类</p>
                <a href="{{ url_for('main.products') }}" class="btn btn-primary">查看全部商品</a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
