{% extends "admin/base.html" %}

{% block title %}用户详情 - {{ user.username }} - 管理后台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">用户详情</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.users') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>返回列表
            </a>
        </div>
    </div>
</div>

<!-- 用户基本信息 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">基本信息</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2">
                        <div class="text-center">
                            <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center mx-auto mb-3" 
                                 style="width: 80px; height: 80px; font-size: 2rem; color: white;">
                                {{ (user.real_name or user.username)[0].upper() }}
                            </div>
                            {% if user.is_admin %}
                            <span class="badge bg-warning text-dark">管理员</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-10">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold" style="width: 120px;">用户名:</td>
                                <td>{{ user.username }}</td>
                                <td class="fw-bold" style="width: 120px;">真实姓名:</td>
                                <td>{{ user.real_name or '未填写' }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">邮箱:</td>
                                <td>
                                    {{ user.email or '未填写' }}
                                    {% if user.email %}
                                    {% if user.email_verified %}
                                    <span class="badge bg-success ms-2">已验证</span>
                                    {% else %}
                                    <span class="badge bg-secondary ms-2">未验证</span>
                                    {% endif %}
                                    {% endif %}
                                </td>
                                <td class="fw-bold">手机号:</td>
                                <td>
                                    {{ user.phone or '未填写' }}
                                    {% if user.phone %}
                                    {% if user.phone_verified %}
                                    <span class="badge bg-success ms-2">已验证</span>
                                    {% else %}
                                    <span class="badge bg-secondary ms-2">未验证</span>
                                    {% endif %}
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">账户状态:</td>
                                <td>
                                    {% if user.is_active %}
                                    <span class="badge bg-success">活跃</span>
                                    {% else %}
                                    <span class="badge bg-danger">禁用</span>
                                    {% endif %}
                                </td>
                                <td class="fw-bold">用户类型:</td>
                                <td>
                                    {% if user.is_admin %}
                                    <span class="badge bg-warning text-dark">管理员</span>
                                    {% else %}
                                    <span class="badge bg-info">普通用户</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">注册时间:</td>
                                <td>{{ format_china_time(user.created_at) }}</td>
                                <td class="fw-bold">最近登录:</td>
                                <td>
                                    {% if user.last_login %}
                                    {{ format_china_time(user.last_login) if user.last_login else '从未登录' }}
                                    {% else %}
                                    <span class="text-muted">从未登录</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">订单统计</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="text-primary">
                            <h4 class="mb-0">{{ order_stats.total_orders }}</h4>
                            <small>总订单数</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="text-success">
                            <h4 class="mb-0">¥{{ "%.2f"|format(order_stats.total_amount) }}</h4>
                            <small>消费总额</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-warning">
                            <h4 class="mb-0">{{ order_stats.pending_orders }}</h4>
                            <small>待处理订单</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-info">
                            <h4 class="mb-0">{{ order_stats.completed_orders }}</h4>
                            <small>已完成订单</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 用户地址 -->
{% if user.addresses %}
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">收货地址</h5>
    </div>
    <div class="card-body">
        <div class="row">
            {% for address in user.addresses %}
            <div class="col-md-6 mb-3">
                <div class="border rounded p-3">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="mb-0">{{ address.name }}</h6>
                        {% if address.is_default %}
                        <span class="badge bg-primary">默认</span>
                        {% endif %}
                    </div>
                    <p class="text-muted mb-1">{{ address.phone }}</p>
                    <p class="mb-0">{{ address.full_address }}</p>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- 最近订单 -->
{% if recent_orders %}
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">最近订单</h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>订单号</th>
                        <th>商品</th>
                        <th>金额</th>
                        <th>状态</th>
                        <th>下单时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for order in recent_orders %}
                    <tr>
                        <td>
                            <span class="font-monospace">{{ order.order_no }}</span>
                        </td>
                        <td>
                            {% set first_item = order.order_items.first() %}
                            {% if first_item %}
                                {{ first_item.product_name }}
                                {% if order.order_items.count() > 1 %}
                                <br><small class="text-muted">等{{ order.order_items.count() }}件商品</small>
                                {% endif %}
                            {% else %}
                                <span class="text-muted">无商品信息</span>
                            {% endif %}
                        </td>
                        <td>¥{{ "%.2f"|format(order.final_amount) }}</td>
                        <td>
                            <span class="badge bg-{% if order.status == 'pending' %}warning{% elif order.status == 'paid' %}info{% elif order.status == 'processing' %}primary{% elif order.status == 'shipped' %}secondary{% elif order.status == 'delivered' %}success{% else %}danger{% endif %}">
                                {{ order.get_status_display() }}
                            </span>
                        </td>
                        <td>
                            <span>{{ format_china_time(order.created_at, '%Y-%m-%d') }}</span>
                            <br>
                            <small class="text-muted">{{ format_china_time(order.created_at, '%H:%M') }}</small>
                        </td>
                        <td>
                            <a href="{{ url_for('admin.order_detail', id=order.id) }}" 
                               class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i> 详情
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endif %}

<!-- 操作区域 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">用户操作</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>账户状态管理</h6>
                <div class="d-flex gap-2 align-items-center mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="activeSwitch" 
                               {% if user.is_active %}checked{% endif %}>
                        <label class="form-check-label" for="activeSwitch">
                            账户{% if user.is_active %}活跃{% else %}禁用{% endif %}
                        </label>
                    </div>
                </div>
                <div class="d-flex gap-2 align-items-center">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="adminSwitch" 
                               {% if user.is_admin %}checked{% endif %}>
                        <label class="form-check-label" for="adminSwitch">
                            {% if user.is_admin %}管理员权限{% else %}普通用户{% endif %}
                        </label>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <h6>其他操作</h6>
                <div class="d-flex gap-2">
                    <a href="{{ url_for('admin.orders') }}?search={{ user.username }}" 
                       class="btn btn-outline-primary">
                        <i class="fas fa-shopping-cart me-1"></i>查看订单
                    </a>
                    <button type="button" class="btn btn-outline-info" onclick="resetPassword()">
                        <i class="fas fa-key me-1"></i>重置密码
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 账户状态开关
    $('#activeSwitch').on('change', function() {
        updateUserStatus('is_active', $(this).prop('checked'));
    });
    
    // 管理员权限开关
    $('#adminSwitch').on('change', function() {
        updateUserStatus('is_admin', $(this).prop('checked'));
    });
});

function updateUserStatus(field, value) {
    const userId = {{ user.id|tojson }};
    const data = {};
    data[field] = value;
    
    // 获取CSRF token
    const csrfToken = $('meta[name="csrf-token"]').attr('content') || 
                     $('input[name="csrf_token"]').val() || 
                     '{{ csrf_token() }}';
    
    $.ajax({
        url: `/admin/users/${userId}/update-status`,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(data),
        headers: {
            'X-CSRFToken': csrfToken
        },
        success: function(response) {
            if (response.success) {
                showAlert('success', response.message);
                // 更新标签文本
                if (field === 'is_active') {
                    $('#activeSwitch').next('label').text('账户' + (value ? '活跃' : '禁用'));
                } else if (field === 'is_admin') {
                    $('#adminSwitch').next('label').text(value ? '管理员权限' : '普通用户');
                }
            } else {
                showAlert('danger', response.message);
                // 恢复开关状态
                $('#' + field.replace('_', '') + 'Switch').prop('checked', !value);
            }
        },
        error: function() {
            showAlert('danger', '状态更新失败，请重试');
            // 恢复开关状态
            $('#' + field.replace('_', '') + 'Switch').prop('checked', !value);
        }
    });
}

function resetPassword() {
    // 实现密码重置功能
    showAlert('info', '密码重置功能正在开发中...');
}

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').prepend(alertHtml);
    
    // 3秒后自动关闭
    setTimeout(function() {
        $('.alert').alert('close');
    }, 3000);
}
</script>
{% endblock %} 