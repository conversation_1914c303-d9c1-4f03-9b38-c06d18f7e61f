"""
自定义属性功能测试用例
测试自定义数据的存储、显示和处理功能
"""

import unittest
import json
from app import create_app
from models import db
from models.user import User
from models.product import Product, Category, AttributeGroup, Attribute
from models.order import Order, OrderItem


class CustomAttributesTestCase(unittest.TestCase):
    """自定义属性测试用例"""
    
    def setUp(self):
        """测试前准备"""
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.client = self.app.test_client()
        
        # 创建数据库表
        db.create_all()
        
        # 创建测试数据
        self.create_test_data()
    
    def tearDown(self):
        """测试后清理"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def create_test_data(self):
        """创建测试数据"""
        # 创建测试用户
        self.user = User(
            username='testuser',
            email='<EMAIL>',
            real_name='测试用户'
        )
        self.user.set_password('password123')
        db.session.add(self.user)
        
        # 创建测试分类
        self.category = Category(
            name='测试分类',
            description='用于测试的分类'
        )
        db.session.add(self.category)
        
        # 创建支持自定义输入的属性组
        self.custom_group = AttributeGroup(
            category_id=1,  # 会在commit后更新
            name='尺寸',
            description='商品尺寸规格',
            allow_custom_input=True,
            custom_input_type='number',
            custom_input_label='自定义尺寸',
            custom_input_placeholder='请输入尺寸（毫米）',
            custom_validation_pattern=r'^[1-9]\d*$',
            custom_validation_message='请输入有效的尺寸',
            custom_price_formula='numeric_value * 0.05',
            custom_price_modifier_type='formula'
        )
        db.session.add(self.custom_group)
        
        # 创建标准属性组
        self.standard_group = AttributeGroup(
            category_id=1,
            name='材质',
            description='商品材质'
        )
        db.session.add(self.standard_group)
        
        # 创建测试商品
        self.product = Product(
            name='测试商品',
            description='用于测试的商品',
            category_id=1,
            base_price=100.0
        )
        db.session.add(self.product)
        
        # 提交以获取ID
        db.session.commit()
        
        # 更新属性组的分类ID
        self.custom_group.category_id = self.category.id
        self.standard_group.category_id = self.category.id
        self.product.category_id = self.category.id
        
        # 创建标准属性
        self.standard_attr = Attribute(
            group_id=self.standard_group.id,
            name='铜版纸',
            value='300g铜版纸',
            price_modifier=10.0,
            price_modifier_type='fixed'
        )
        db.session.add(self.standard_attr)
        
        db.session.commit()
    
    def login_user(self):
        """登录测试用户"""
        return self.client.post('/auth/login', data={
            'username': 'testuser',
            'password': 'password123'
        }, follow_redirects=True)
    
    def test_custom_attribute_group_creation(self):
        """测试自定义属性组创建"""
        self.assertTrue(self.custom_group.allow_custom_input)
        self.assertEqual(self.custom_group.custom_input_type, 'number')
        self.assertEqual(self.custom_group.custom_price_modifier_type, 'formula')
    
    def test_custom_price_calculation(self):
        """测试自定义价格计算"""
        # 测试公式计算
        custom_value = 50  # 50毫米
        base_price = 100.0
        
        calculated_price = self.custom_group.calculate_custom_price(custom_value, base_price)
        expected_price = 50 * 0.05  # numeric_value * 0.05
        
        self.assertEqual(calculated_price, expected_price)
    
    def test_custom_input_validation(self):
        """测试自定义输入验证"""
        # 测试有效输入
        is_valid, message = self.custom_group.validate_custom_input('50')
        self.assertTrue(is_valid)
        
        # 测试无效输入（非数字）
        is_valid, message = self.custom_group.validate_custom_input('abc')
        self.assertFalse(is_valid)
        
        # 测试无效输入（不符合正则）
        is_valid, message = self.custom_group.validate_custom_input('0')
        self.assertFalse(is_valid)
    
    def test_order_with_custom_attributes(self):
        """测试包含自定义属性的订单创建"""
        self.login_user()
        
        # 模拟创建包含自定义属性的订单
        order_data = {
            'product_id': self.product.id,
            'quantity': 1,
            'selected_attributes': ['custom_尺寸'],
            'attribute_quantities': {
                'custom_group_尺寸': 50
            },
            'recipient_name': '测试收货人',
            'recipient_phone': '13800138000',
            'shipping_province': '广东省',
            'shipping_city': '广州市',
            'shipping_district': '天河区',
            'shipping_address': '测试地址123号'
        }
        
        response = self.client.post('/create-order', 
                                  data=json.dumps(order_data),
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        
        # 检查订单是否创建成功
        result = json.loads(response.data)
        self.assertTrue(result['success'])
        
        # 检查订单项的自定义属性
        order = Order.query.get(result['order_id'])
        order_item = order.order_items.first()
        
        self.assertIsNotNone(order_item.product_attributes)
        self.assertTrue(order_item.has_custom_attributes())
        
        # 检查属性显示
        attr_display = order_item.get_attributes_display()
        self.assertIn('自定义', attr_display)
        self.assertIn('50', attr_display)
    
    def test_order_item_attribute_methods(self):
        """测试订单项属性方法"""
        # 创建包含自定义属性的订单项
        order = Order(
            order_no='TEST001',
            user_id=self.user.id,
            total_amount=150.0,
            final_amount=150.0,
            shipping_name='测试用户',
            shipping_phone='13800138000'
        )
        db.session.add(order)
        db.session.flush()
        
        # 创建包含自定义属性的订单项
        custom_attributes = {
            '尺寸': '50 (自定义)',
            '材质': '300g铜版纸'
        }
        
        order_item = OrderItem(
            order_id=order.id,
            product_id=self.product.id,
            product_name=self.product.name,
            product_attributes=custom_attributes,
            quantity=1,
            unit_price=150.0,
            total_price=150.0
        )
        db.session.add(order_item)
        db.session.commit()
        
        # 测试方法
        self.assertTrue(order_item.has_custom_attributes())
        
        attr_details = order_item.get_attributes_detail()
        self.assertEqual(len(attr_details), 2)
        
        # 检查自定义属性
        custom_attr = next((attr for attr in attr_details if attr['is_custom']), None)
        self.assertIsNotNone(custom_attr)
        self.assertEqual(custom_attr['name'], '尺寸')
        self.assertEqual(custom_attr['display_value'], '50')
        
        # 检查标准属性
        standard_attr = next((attr for attr in attr_details if not attr['is_custom']), None)
        self.assertIsNotNone(standard_attr)
        self.assertEqual(standard_attr['name'], '材质')
    
    def test_api_validate_custom_input(self):
        """测试自定义输入验证API"""
        validation_data = {
            'group_name': '尺寸',
            'custom_value': '50'
        }
        
        response = self.client.post('/api/validate-custom-input',
                                  data=json.dumps(validation_data),
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertTrue(result['success'])
    
    def test_api_debug_order_attributes(self):
        """测试订单属性调试API"""
        self.login_user()
        
        # 先创建一个包含自定义属性的订单
        order = Order(
            order_no='DEBUG001',
            user_id=self.user.id,
            total_amount=150.0,
            final_amount=150.0,
            shipping_name='测试用户',
            shipping_phone='13800138000'
        )
        db.session.add(order)
        db.session.flush()
        
        custom_attributes = {'尺寸': '50 (自定义)'}
        order_item = OrderItem(
            order_id=order.id,
            product_id=self.product.id,
            product_name=self.product.name,
            product_attributes=custom_attributes,
            quantity=1,
            unit_price=150.0,
            total_price=150.0
        )
        db.session.add(order_item)
        db.session.commit()
        
        # 测试调试API
        response = self.client.get(f'/api/debug/order-attributes/{order.id}')
        self.assertEqual(response.status_code, 200)
        
        result = json.loads(response.data)
        self.assertTrue(result['success'])
        self.assertEqual(result['data']['order_no'], 'DEBUG001')
        self.assertEqual(len(result['data']['items']), 1)


if __name__ == '__main__':
    unittest.main()
