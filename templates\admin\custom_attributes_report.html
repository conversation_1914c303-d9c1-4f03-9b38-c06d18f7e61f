{% extends "admin/base.html" %}

{% block title %}自定义属性使用报告 - 管理后台{% endblock %}

{% block extra_css %}
<style>
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.custom-attribute-item {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 0.25rem;
    padding: 0.5rem;
    margin: 0.25rem 0;
}

.attribute-badge {
    font-size: 0.75em;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    margin-right: 0.5rem;
}

.custom-badge {
    background-color: #dc3545;
    color: white;
}

.standard-badge {
    background-color: #6c757d;
    color: white;
}
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">自定义属性使用报告</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.orders') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>返回订单列表
            </a>
        </div>
    </div>
</div>

<!-- 统计概览 -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="stats-card">
            <div class="stats-number">{{ stats.total_orders_with_custom }}</div>
            <div>包含自定义属性的订单</div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="stats-card">
            <div class="stats-number">{{ stats.total_orders }}</div>
            <div>总订单数量</div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="stats-card">
            <div class="stats-number">{{ stats.custom_percentage }}%</div>
            <div>自定义属性使用率</div>
        </div>
    </div>
</div>

<!-- 自定义属性订单列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-edit me-2"></i>包含自定义属性的订单
            <span class="badge bg-primary ms-2">{{ custom_orders|length }}</span>
        </h5>
    </div>
    <div class="card-body p-0">
        {% if custom_orders %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>订单信息</th>
                        <th>商品名称</th>
                        <th>自定义属性详情</th>
                        <th>数量</th>
                        <th>金额</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in custom_orders %}
                    <tr>
                        <td>
                            <div>
                                <strong>{{ item.order.order_no }}</strong>
                            </div>
                            <small class="text-muted">
                                用户：{{ item.order.user.username }}
                            </small>
                        </td>
                        <td>
                            <div>{{ item.product_name }}</div>
                            {% if not item.product %}
                            <small class="text-danger">商品已删除</small>
                            {% endif %}
                        </td>
                        <td>
                            <div class="custom-attributes-detail">
                                {% set attr_details = item.get_attributes_detail() %}
                                {% if attr_details %}
                                    {% for attr in attr_details %}
                                    <div class="mb-1">
                                        {% if attr.is_custom %}
                                        <span class="attribute-badge custom-badge">自定义</span>
                                        <strong>{{ attr.name }}:</strong> {{ attr.display_value }}
                                        {% else %}
                                        <span class="attribute-badge standard-badge">标准</span>
                                        {{ attr.name }}: {{ attr.value }}
                                        {% endif %}
                                    </div>
                                    {% endfor %}
                                {% else %}
                                <div class="custom-attribute-item">
                                    {{ item.get_attributes_display() }}
                                </div>
                                {% endif %}
                            </div>
                        </td>
                        <td>{{ item.quantity }}</td>
                        <td>¥{{ "%.2f"|format(item.total_price) }}</td>
                        <td>
                            <small>{{ format_china_time(item.created_at, '%Y-%m-%d %H:%M') }}</small>
                        </td>
                        <td>
                            <a href="{{ url_for('admin.order_detail', id=item.order.id) }}" 
                               class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye me-1"></i>查看
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">暂无包含自定义属性的订单</h5>
            <p class="text-muted">当用户在下单时使用自定义属性时，相关订单会显示在这里。</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- 使用说明 -->
<div class="card mt-4">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fas fa-info-circle me-2"></i>说明
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>自定义属性标识</h6>
                <ul class="list-unstyled">
                    <li><span class="attribute-badge custom-badge">自定义</span> 用户输入的自定义值</li>
                    <li><span class="attribute-badge standard-badge">标准</span> 预设的标准属性值</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>注意事项</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>自定义属性需要特别注意生产要求</li>
                    <li><i class="fas fa-check-circle text-success me-2"></i>建议在生产前与客户确认自定义规格</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 可以添加一些交互功能，比如筛选、搜索等
document.addEventListener('DOMContentLoaded', function() {
    // 高亮显示自定义属性
    const customItems = document.querySelectorAll('.custom-attribute-item');
    customItems.forEach(item => {
        item.style.animation = 'fadeIn 0.5s ease-in';
    });
});

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
