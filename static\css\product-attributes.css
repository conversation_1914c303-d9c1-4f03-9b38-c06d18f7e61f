/**
 * 商品属性选择组件样式
 */

.attribute-group {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.attribute-group-header {
    margin-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 10px;
}

.attribute-group-header .form-label {
    font-size: 16px;
    color: #333;
    margin-bottom: 5px;
}

.attribute-group-header small {
    color: #666;
    font-size: 13px;
}

/* 标准属性选项样式 */
.attribute-options {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
}

.attribute-options .form-check {
    background-color: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 16px;
    margin: 0;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
    text-align: center;
}

.attribute-options .form-check:hover {
    border-color: #007bff;
    background-color: #e7f3ff;
}

.attribute-options .form-check-input:checked + .form-check-label {
    color: #007bff;
    font-weight: 600;
}

.attribute-options .form-check:has(.form-check-input:checked) {
    border-color: #007bff;
    background-color: #e7f3ff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.attribute-options .form-check-input {
    display: none; /* 隐藏原始单选框 */
}

.attribute-options .form-check-label {
    cursor: pointer;
    margin-bottom: 0;
    display: block;
    width: 100%;
}

.attribute-options .text-success {
    display: block;
    font-size: 12px;
    margin-top: 4px;
    font-weight: 500;
}

/* 下拉选择样式 */
.attribute-group select.form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 12px 16px;
    font-size: 15px;
    transition: border-color 0.3s ease;
}

.attribute-group select.form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 自定义输入样式 */
.custom-inputs {
    border-top: 1px solid #f0f0f0;
    padding-top: 15px;
}

.custom-input-item {
    background-color: #fafbfc;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.custom-input-item .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.custom-input-item .form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.3s ease;
}

.custom-input-item .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.custom-input-item .form-text {
    margin-top: 5px;
    font-size: 12px;
}

/* 数量选择器样式 */
.custom-input-item .input-group {
    border-radius: 6px;
    overflow: hidden;
}

.custom-input-item .input-group .form-control {
    border-radius: 0;
    border-right: none;
}

.custom-input-item .input-group-text {
    background-color: #e9ecef;
    border-left: none;
    font-weight: 500;
}

/* 文件上传样式 */
.custom-input-item input[type="file"] {
    padding: 8px 12px;
}

.custom-input-item input[type="file"]::-webkit-file-upload-button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    margin-right: 10px;
    cursor: pointer;
    font-size: 13px;
}

.custom-input-item input[type="file"]::-webkit-file-upload-button:hover {
    background-color: #0056b3;
}

/* 价格显示样式 */
.price-breakdown {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.price-breakdown .base-price,
.price-breakdown .attribute-price {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

.price-breakdown .total-price {
    color: #007bff;
    font-weight: 700;
    margin-bottom: 0;
    padding-top: 10px;
    border-top: 1px solid #dee2e6;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .attribute-options {
        flex-direction: column;
    }
    
    .attribute-options .form-check {
        min-width: auto;
        width: 100%;
    }
    
    .attribute-group {
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .custom-input-item {
        padding: 12px;
    }
}

/* 加载状态 */
.attribute-loading {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.attribute-loading .spinner-border {
    width: 2rem;
    height: 2rem;
    margin-bottom: 10px;
}

/* 错误状态 */
.attribute-error {
    text-align: center;
    padding: 30px 20px;
}

.attribute-error .alert {
    border-radius: 8px;
    font-size: 15px;
}

/* 验证状态 */
.custom-attribute.is-invalid {
    border-color: #dc3545;
}

.custom-attribute.is-valid {
    border-color: #28a745;
}

.invalid-feedback {
    display: block;
    font-size: 12px;
    color: #dc3545;
    margin-top: 5px;
}

.valid-feedback {
    display: block;
    font-size: 12px;
    color: #28a745;
    margin-top: 5px;
}

/* 动画效果 */
.attribute-group {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.attribute-options .form-check {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.custom-input-item {
    transition: all 0.3s ease;
}

.custom-input-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 特殊标记 */
.attribute-required::after {
    content: " *";
    color: #dc3545;
}

.attribute-optional {
    opacity: 0.8;
}

.attribute-price-impact {
    font-weight: 600;
    color: #28a745;
}

.attribute-price-impact.negative {
    color: #dc3545;
}

/* 工具提示样式 */
.attribute-tooltip {
    position: relative;
    cursor: help;
}

.attribute-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: white;
    padding: 5px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s;
    z-index: 1000;
}

.attribute-tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}
