-- 为attribute_groups表添加自定义输入支持字段
-- 执行时间: 2024-12-19

-- 添加自定义输入相关字段
ALTER TABLE attribute_groups 
ADD COLUMN allow_custom_input BOOLEAN DEFAULT FALSE COMMENT '是否允许自定义输入';

ALTER TABLE attribute_groups 
ADD COLUMN custom_input_type ENUM('text', 'number', 'textarea') DEFAULT 'text' COMMENT '自定义输入类型';

ALTER TABLE attribute_groups 
ADD COLUMN custom_input_label VARCHAR(100) DEFAULT '自定义' COMMENT '自定义选项的标签';

ALTER TABLE attribute_groups 
ADD COLUMN custom_input_placeholder VARCHAR(200) COMMENT '自定义输入框的占位符';

ALTER TABLE attribute_groups 
ADD COLUMN custom_validation_pattern VARCHAR(500) COMMENT '自定义输入的验证正则表达式';

ALTER TABLE attribute_groups 
ADD COLUMN custom_validation_message VARCHAR(200) COMMENT '自定义输入验证失败提示';

ALTER TABLE attribute_groups 
ADD COLUMN custom_price_formula TEXT COMMENT '自定义输入的价格计算公式';

ALTER TABLE attribute_groups 
ADD COLUMN custom_price_modifier DECIMAL(15,5) DEFAULT 0.00000 COMMENT '自定义输入的价格调整';

ALTER TABLE attribute_groups 
ADD COLUMN custom_price_modifier_type ENUM('fixed', 'percentage', 'formula') DEFAULT 'fixed' COMMENT '自定义输入价格调整类型';

-- 创建索引以提高查询性能
CREATE INDEX idx_attribute_groups_allow_custom ON attribute_groups(allow_custom_input);
CREATE INDEX idx_attribute_groups_custom_type ON attribute_groups(custom_input_type);

-- 更新现有数据示例（可选）
-- 为纸张属性组启用自定义输入
UPDATE attribute_groups 
SET 
    allow_custom_input = TRUE,
    custom_input_type = 'text',
    custom_input_label = '自定义',
    custom_input_placeholder = '请输入自定义纸张类型',
    custom_price_modifier = 5.00000,
    custom_price_modifier_type = 'fixed'
WHERE name = '纸张' AND allow_custom_input IS NOT NULL;

-- 为每本张数属性组启用自定义输入
UPDATE attribute_groups 
SET 
    allow_custom_input = TRUE,
    custom_input_type = 'number',
    custom_input_label = '自定义',
    custom_input_placeholder = '请输入自定义张数',
    custom_validation_pattern = '^[1-9][0-9]*$',
    custom_validation_message = '请输入大于0的整数',
    custom_price_formula = 'numeric_value * 0.1',
    custom_price_modifier_type = 'formula'
WHERE name = '每本张数' AND allow_custom_input IS NOT NULL;

-- 验证迁移结果
SELECT 
    name,
    allow_custom_input,
    custom_input_type,
    custom_input_label,
    custom_price_modifier_type
FROM attribute_groups 
WHERE allow_custom_input = TRUE;
