-- 订单详情表
CREATE TABLE order_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    product_name VARCHAR(200) NOT NULL,
    product_attributes JSON,
    quantity INT NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    <PERSON>OREIG<PERSON> KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    INDEX idx_order_id (order_id),
    INDEX idx_product_id (product_id)
);

-- 购物车表
CREATE TABLE cart_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL,
    selected_attributes JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_product (user_id, product_id),
    INDEX idx_user_id (user_id),
    INDEX idx_product_id (product_id)
);

-- 系统配置表
CREATE TABLE system_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT,
    description VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_config_key (config_key)
);

-- 操作日志表
CREATE TABLE admin_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(50),
    record_id INT,
    old_data JSON,
    new_data JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);

-- 插入默认管理员用户
INSERT INTO users (username, email, password_hash, real_name, is_admin, is_active) VALUES 
('admin', '<EMAIL>', 'pbkdf2:sha256:260000$salt$hash', '系统管理员', TRUE, TRUE);

-- 插入默认分类
INSERT INTO categories (name, description, sort_order) VALUES 
('纸箱包装', '各种规格的纸箱包装产品', 1),
('印刷品', '宣传册、海报等印刷产品', 2),
('包装材料', '气泡膜、胶带等包装辅材', 3);

-- 插入默认属性分组
INSERT INTO attribute_groups (name, description, sort_order) VALUES 
('尺寸规格', '产品的长宽高尺寸', 1),
('材质类型', '产品使用的材质', 2),
('印刷工艺', '印刷相关的工艺选项', 3),
('颜色选择', '产品颜色选项', 4);

-- 插入默认属性
INSERT INTO attributes (group_id, name, value, price_modifier, sort_order) VALUES 
(1, '小号', '20x15x10cm', 0.00, 1),
(1, '中号', '30x25x20cm', 5.00, 2),
(1, '大号', '40x35x30cm', 10.00, 3),
(2, '普通瓦楞纸', '3层瓦楞纸', 0.00, 1),
(2, '加厚瓦楞纸', '5层瓦楞纸', 3.00, 2),
(3, '单色印刷', '黑色或单一颜色', 0.00, 1),
(3, '彩色印刷', '全彩印刷', 8.00, 2),
(4, '本色', '纸箱原色', 0.00, 1),
(4, '白色', '白色纸箱', 2.00, 2);

-- 插入系统配置
INSERT INTO system_configs (config_key, config_value, description) VALUES 
('site_name', '印刷品商城', '网站名称'),
('site_description', '专业的印刷品在线销售平台', '网站描述'),
('contact_phone', '************', '联系电话'),
('contact_email', '<EMAIL>', '联系邮箱'),
('min_order_amount', '50.00', '最小订单金额'),
('free_shipping_amount', '200.00', '免运费金额');
