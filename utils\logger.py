import logging
import os
from datetime import datetime
from flask import request, current_app
from flask_login import current_user

class AdminLogger:
    """管理后台操作日志记录器"""
    
    def __init__(self):
        self.logger = None
        self.setup_logger()
    
    def setup_logger(self):
        """设置日志记录器"""
        self.logger = logging.getLogger('admin_operations')
        self.logger.setLevel(logging.DEBUG)
        
        # 避免重复添加handler
        if not self.logger.handlers:
            # 创建logs目录
            logs_dir = 'logs'
            if not os.path.exists(logs_dir):
                os.makedirs(logs_dir)
            
            # 文件处理器 - 详细日志
            file_handler = logging.FileHandler(
                os.path.join(logs_dir, 'admin_operations.log'),
                encoding='utf-8'
            )
            file_handler.setLevel(logging.DEBUG)
            
            # 错误文件处理器 - 仅错误日志
            error_handler = logging.FileHandler(
                os.path.join(logs_dir, 'admin_errors.log'),
                encoding='utf-8'
            )
            error_handler.setLevel(logging.ERROR)
            
            # 格式化器
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            file_handler.setFormatter(formatter)
            error_handler.setFormatter(formatter)
            
            # 添加处理器
            self.logger.addHandler(file_handler)
            self.logger.addHandler(error_handler)
    
    def get_request_context(self):
        """获取请求上下文信息"""
        context = {
            'timestamp': datetime.now().isoformat(),
            'method': request.method if request else 'Unknown',
            'url': request.url if request else 'Unknown',
            'remote_addr': request.remote_addr if request else 'Unknown',
            'user_agent': request.headers.get('User-Agent', 'Unknown') if request else 'Unknown',
        }
        
        # 添加用户信息
        if current_user and current_user.is_authenticated:
            context.update({
                'user_id': current_user.id,
                'username': current_user.username,
                'is_admin': current_user.is_admin
            })
        
        return context
    
    def log_admin_action(self, operation, details, additional_data=None):
        """记录管理员操作"""
        context = self.get_request_context()
        
        log_data = {
            'operation': operation,
            'details': details,
            'context': context
        }
        
        if additional_data:
            log_data['additional_data'] = additional_data
        
        self.logger.info(f"管理操作: {log_data}")
    
    def log_product_add_attempt(self, form_data, success=True, error=None):
        """记录添加商品的尝试"""
        context = self.get_request_context()
        
        log_data = {
            'operation': 'add_product',
            'success': success,
            'context': context,
            'form_data': {
                'name': form_data.get('name', ''),
                'category_id': form_data.get('category_id', ''),
                'base_price': form_data.get('base_price', ''),
                'selected_attributes_count': len(form_data.get('selected_attributes', [])),
                'has_image_file': bool(form_data.get('image_file')),
                'image_url': form_data.get('image_url', '')
            }
        }
        
        if success:
            self.logger.info(f"商品添加成功: {log_data}")
        else:
            log_data['error_details'] = {
                'error_type': type(error).__name__ if error else 'Unknown',
                'error_message': str(error) if error else 'Unknown error',
                'error_class': error.__class__.__module__ + '.' + error.__class__.__name__ if error else 'Unknown'
            }
            self.logger.error(f"商品添加失败: {log_data}")
    
    def log_validation_error(self, form_errors, operation='unknown'):
        """记录表单验证错误"""
        context = self.get_request_context()
        
        log_data = {
            'operation': operation,
            'type': 'validation_error',
            'context': context,
            'form_errors': form_errors
        }
        
        self.logger.warning(f"表单验证失败: {log_data}")
    
    def log_database_error(self, error, operation='unknown', additional_data=None):
        """记录数据库操作错误"""
        context = self.get_request_context()
        
        log_data = {
            'operation': operation,
            'type': 'database_error',
            'context': context,
            'error_details': {
                'error_type': type(error).__name__,
                'error_message': str(error),
                'error_class': error.__class__.__module__ + '.' + error.__class__.__name__
            }
        }
        
        if additional_data:
            log_data['additional_data'] = additional_data
        
        self.logger.error(f"数据库操作错误: {log_data}")
    
    def log_attribute_binding_issue(self, product_id, attribute_id, issue_type, details=None):
        """记录属性绑定相关问题"""
        context = self.get_request_context()
        
        log_data = {
            'operation': 'attribute_binding',
            'type': issue_type,
            'context': context,
            'binding_data': {
                'product_id': product_id,
                'attribute_id': attribute_id
            }
        }
        
        if details:
            log_data['details'] = details
        
        if issue_type in ['duplicate', 'not_found']:
            self.logger.warning(f"属性绑定问题: {log_data}")
        else:
            self.logger.error(f"属性绑定错误: {log_data}")

# 全局日志记录器实例
admin_logger = AdminLogger() 