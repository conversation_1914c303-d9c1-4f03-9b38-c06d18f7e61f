{% extends "admin/base.html" %}

{% block title %}用户管理 - 管理后台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">用户管理</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportUsers()">
                <i class="fas fa-download me-1"></i>导出用户
            </button>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card border-0 bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="card-title h6">总用户</div>
                        <div class="h4">{{ stats.total }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card border-0 bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="card-title h6">活跃用户</div>
                        <div class="h4">{{ stats.active }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-check fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card border-0 bg-secondary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="card-title h6">禁用用户</div>
                        <div class="h4">{{ stats.inactive }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-times fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card border-0 bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="card-title h6">管理员</div>
                        <div class="h4">{{ stats.admin }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-shield fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card border-0 bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="card-title h6">邮箱验证</div>
                        <div class="h4">{{ stats.verified_email }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-envelope-check fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card border-0 bg-dark text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="card-title h6">手机验证</div>
                        <div class="h4">{{ stats.verified_phone }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-mobile-alt fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 筛选工具栏 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ url_for('admin.users') }}">
            <div class="row align-items-end">
                <div class="col-md-3">
                    <label for="search" class="form-label">搜索用户</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ search }}" placeholder="用户名、真实姓名、邮箱、手机">
                </div>
                <div class="col-md-2">
                    <label for="is_active" class="form-label">活跃状态</label>
                    <select class="form-select" id="is_active" name="is_active">
                        <option value="">全部</option>
                        <option value="1" {% if is_active == '1' %}selected{% endif %}>活跃</option>
                        <option value="0" {% if is_active == '0' %}selected{% endif %}>禁用</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="is_admin" class="form-label">管理员</label>
                    <select class="form-select" id="is_admin" name="is_admin">
                        <option value="">全部</option>
                        <option value="1" {% if is_admin == '1' %}selected{% endif %}>是</option>
                        <option value="0" {% if is_admin == '0' %}selected{% endif %}>否</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>搜索
                    </button>
                    <a href="{{ url_for('admin.users') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>清除
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 用户列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">用户列表</h5>
    </div>
    <div class="card-body p-0">
        {% if users.items %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>用户信息</th>
                        <th>联系方式</th>
                        <th>验证状态</th>
                        <th>账户状态</th>
                        <th>注册时间</th>
                        <th>最近登录</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users.items %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center me-3" 
                                     style="width: 40px; height: 40px; font-size: 1rem; color: white;">
                                    {{ (user.real_name or user.username)[0].upper() }}
                                </div>
                                <div>
                                    <strong>{{ user.real_name or user.username }}</strong>
                                    {% if user.is_admin %}
                                    <span class="badge bg-warning text-dark ms-2">管理员</span>
                                    {% endif %}
                                    <br>
                                    <small class="text-muted">用户名: {{ user.username }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div>
                                {% if user.email %}
                                <div class="mb-1">
                                    <i class="fas fa-envelope me-1"></i>
                                    {{ user.email }}
                                </div>
                                {% endif %}
                                {% if user.phone %}
                                <div>
                                    <i class="fas fa-phone me-1"></i>
                                    {{ user.phone }}
                                </div>
                                {% endif %}
                                {% if not user.email and not user.phone %}
                                <span class="text-muted">未填写</span>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <div>
                                {% if user.email_verified %}
                                <span class="badge bg-success mb-1">邮箱已验证</span>
                                {% else %}
                                <span class="badge bg-secondary mb-1">邮箱未验证</span>
                                {% endif %}
                                <br>
                                {% if user.phone_verified %}
                                <span class="badge bg-success">手机已验证</span>
                                {% else %}
                                <span class="badge bg-secondary">手机未验证</span>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <div class="d-flex flex-column gap-1">
                                <!-- 活跃状态开关 -->
                                <div class="form-check form-switch">
                                    <input class="form-check-input status-switch" type="checkbox" 
                                           data-user-id="{{ user.id }}" 
                                           data-field="is_active"
                                           {% if user.is_active %}checked{% endif %}>
                                    <label class="form-check-label small">
                                        {% if user.is_active %}活跃{% else %}禁用{% endif %}
                                    </label>
                                </div>
                                <!-- 管理员开关 -->
                                <div class="form-check form-switch">
                                    <input class="form-check-input status-switch" type="checkbox" 
                                           data-user-id="{{ user.id }}" 
                                           data-field="is_admin"
                                           {% if user.is_admin %}checked{% endif %}>
                                    <label class="form-check-label small">
                                        {% if user.is_admin %}管理员{% else %}普通用户{% endif %}
                                    </label>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span>{{ format_china_time(user.created_at, '%Y-%m-%d') }}</span>
                            <br>
                            <small class="text-muted">{{ format_china_time(user.created_at, '%H:%M') }}</small>
                        </td>
                        <td>
                            <span>{{ format_china_time(user.last_login, '%Y-%m-%d') if user.last_login else '-' }}</span>
                            <br>
                            <small class="text-muted">{{ format_china_time(user.last_login, '%H:%M') if user.last_login else '' }}</small>
                        </td>
                        <td>
                            <a href="{{ url_for('admin.user_detail', id=user.id) }}" 
                               class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i> 详情
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        {% if users.pages > 1 %}
        <div class="card-footer">
            <nav aria-label="用户分页">
                {{ render_pagination(users, 'admin.users', search=search, is_active=is_active, is_admin=is_admin) }}
            </nav>
        </div>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">暂无用户数据</h5>
            <p class="text-muted">系统中还没有任何用户记录</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 用户状态开关
    $('.status-switch').on('change', function() {
        const userId = $(this).data('user-id');
        const field = $(this).data('field');
        const isChecked = $(this).prop('checked');
        const switchElement = $(this);
        const label = switchElement.next('label');
        
        // 禁用开关
        switchElement.prop('disabled', true);
        
        const updateData = {};
        updateData[field] = isChecked;
        
        $.ajax({
            url: `/admin/users/${userId}/update-status`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(updateData),
            success: function(response) {
                if (response.success) {
                    // 更新标签文本
                    if (field === 'is_active') {
                        label.text(isChecked ? '活跃' : '禁用');
                    } else if (field === 'is_admin') {
                        label.text(isChecked ? '管理员' : '普通用户');
                    }
                    showAlert('success', response.message);
                } else {
                    // 恢复开关状态
                    switchElement.prop('checked', !isChecked);
                    showAlert('danger', response.message);
                }
            },
            error: function() {
                // 恢复开关状态
                switchElement.prop('checked', !isChecked);
                showAlert('danger', '状态更新失败，请重试');
            },
            complete: function() {
                switchElement.prop('disabled', false);
            }
        });
    });
});

function exportUsers() {
    // 实现用户导出功能
    showAlert('info', '导出功能正在开发中...');
}

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').prepend(alertHtml);
    
    // 3秒后自动关闭
    setTimeout(function() {
        $('.alert').alert('close');
    }, 3000);
}
</script>
{% endblock %}
