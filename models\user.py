from datetime import datetime
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from . import db

class CartItem(db.Model):
    __tablename__ = 'cart_items'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=False, index=True)
    product_id = db.Column(db.Integer, db.Foreign<PERSON>ey('products.id'), nullable=False, index=True)
    quantity = db.Column(db.Integer, nullable=False)
    selected_attributes = db.Column(db.JSON)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    __table_args__ = (db.UniqueConstraint('user_id', 'product_id', name='unique_user_product'),)

    def __repr__(self):
        return f'<CartItem {self.user_id}-{self.product_id}>'

class User(UserMixin, db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    phone = db.Column(db.String(20), index=True)
    real_name = db.Column(db.String(50))
    avatar_url = db.Column(db.String(500))
    is_active = db.Column(db.Boolean, default=True, index=True)
    is_admin = db.Column(db.Boolean, default=False)
    email_verified = db.Column(db.Boolean, default=False)
    phone_verified = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    
    # 关系
    orders = db.relationship('Order', backref='user', lazy='dynamic')
    addresses = db.relationship('UserAddress', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    cart_items = db.relationship('CartItem', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def get_id(self):
        """Flask-Login需要的方法"""
        return str(self.id)
    
    def is_authenticated(self):
        """用户是否已认证"""
        return True
    
    def is_anonymous(self):
        """是否匿名用户"""
        return False
    
    def get_default_address(self):
        """获取默认地址"""
        return self.addresses.filter_by(is_default=True).first()

    def get_cart_count(self):
        """获取购物车商品数量"""
        return self.cart_items.count()

    def get_cart_total_quantity(self):
        """获取购物车商品总数量"""
        total = db.session.query(db.func.sum(CartItem.quantity)).filter_by(user_id=self.id).scalar()
        return total or 0

    def __repr__(self):
        return f'<User {self.username}>'

class UserAddress(db.Model):
    __tablename__ = 'user_addresses'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    recipient_name = db.Column(db.String(50), nullable=False)
    phone = db.Column(db.String(20), nullable=False)
    province = db.Column(db.String(50), nullable=False)
    city = db.Column(db.String(50), nullable=False)
    district = db.Column(db.String(50), nullable=False)
    address_detail = db.Column(db.String(200), nullable=False)
    postal_code = db.Column(db.String(10))
    is_default = db.Column(db.Boolean, default=False, index=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def get_full_address(self):
        """获取完整地址"""
        return f"{self.province} {self.city} {self.district} {self.address_detail}"
    
    def set_as_default(self):
        """设置为默认地址"""
        # 先取消其他默认地址
        UserAddress.query.filter_by(user_id=self.user_id, is_default=True).update({'is_default': False})
        # 设置当前地址为默认
        self.is_default = True
        db.session.commit()
    
    def __repr__(self):
        return f'<UserAddress {self.recipient_name}: {self.get_full_address()}>'
