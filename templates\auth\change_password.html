{% extends "base_modern.html" %}

{% block title %}修改密码 - {{ site_config.site_name }}{% endblock %}

{% block extra_css %}
<style>
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --glass-bg: rgba(255, 255, 255, 0.1);
        --glass-border: rgba(255, 255, 255, 0.2);
        --shadow-light: rgba(0, 0, 0, 0.1);
    }

    .profile-container {
        background: var(--primary-gradient);
        min-height: 100vh;
        padding: 2rem 0;
        position: relative;
        overflow: hidden;
    }

    .profile-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: 
            radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 118, 117, 0.3) 0%, transparent 50%);
        pointer-events: none;
    }

    .profile-sidebar {
        background: var(--glass-bg);
        backdrop-filter: blur(20px);
        border: 1px solid var(--glass-border);
        border-radius: 20px;
        padding: 0;
        overflow: hidden;
        box-shadow: 0 8px 32px var(--shadow-light);
    }

    .profile-menu-header {
        background: rgba(255, 255, 255, 0.1);
        padding: 1.5rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .profile-menu-title {
        color: white;
        font-weight: 600;
        margin: 0;
        font-size: 1.125rem;
    }

    .profile-menu-item {
        background: transparent;
        border: none;
        color: rgba(255, 255, 255, 0.8);
        padding: 1rem 1.5rem;
        display: flex;
        align-items: center;
        text-decoration: none;
        transition: all 0.3s ease;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    }

    .profile-menu-item:hover {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        transform: translateX(5px);
    }

    .profile-menu-item.active {
        background: var(--success-gradient);
        color: white;
        box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
    }

    .profile-menu-item i {
        margin-right: 0.75rem;
        font-size: 1rem;
    }

    .profile-content {
        background: var(--glass-bg);
        backdrop-filter: blur(20px);
        border: 1px solid var(--glass-border);
        border-radius: 20px;
        box-shadow: 0 8px 32px var(--shadow-light);
        overflow: hidden;
    }

    .profile-content-header {
        background: rgba(255, 255, 255, 0.1);
        padding: 1.5rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .profile-content-title {
        color: white;
        font-weight: 600;
        margin: 0;
        font-size: 1.25rem;
        display: flex;
        align-items: center;
    }

    .profile-content-title i {
        margin-right: 0.75rem;
        color: #4facfe;
    }

    .profile-content-body {
        padding: 2rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        color: white;
        font-weight: 500;
        margin-bottom: 0.5rem;
        display: block;
    }

    .form-control {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        color: white;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        background: rgba(255, 255, 255, 0.15);
        border-color: #4facfe;
        box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.2);
        color: white;
    }

    .form-control::placeholder {
        color: rgba(255, 255, 255, 0.5);
    }

    .form-control.is-invalid {
        border-color: #ef4444;
    }

    .invalid-feedback {
        color: #fecaca;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .form-text {
        color: rgba(255, 255, 255, 0.6);
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .btn-primary {
        background: var(--success-gradient);
        border: none;
        border-radius: 12px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        color: white;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
    }

    .btn-secondary {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        color: white;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }

    .btn-secondary:hover {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        transform: translateY(-2px);
    }

    .password-strength {
        margin-top: 0.5rem;
    }

    .password-strength-bar {
        height: 4px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 2px;
        overflow: hidden;
        margin-bottom: 0.5rem;
    }

    .password-strength-fill {
        height: 100%;
        transition: all 0.3s ease;
        border-radius: 2px;
    }

    .password-strength-text {
        font-size: 0.75rem;
        color: rgba(255, 255, 255, 0.7);
    }

    @media (max-width: 768px) {
        .profile-container {
            padding: 1rem 0;
        }
        
        .profile-content-body {
            padding: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="profile-container">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 col-md-4 mb-4">
                <!-- 用户菜单 -->
                <div class="profile-sidebar">
                    <div class="profile-menu-header">
                        <h6 class="profile-menu-title">
                            <i class="fas fa-user-circle me-2"></i>个人中心
                        </h6>
                    </div>
                    <div class="profile-menu">
                        <a href="{{ url_for('auth.profile') }}" class="profile-menu-item">
                            <i class="fas fa-user"></i>个人资料
                        </a>
                        <a href="{{ url_for('auth.change_password') }}" class="profile-menu-item active">
                            <i class="fas fa-lock"></i>修改密码
                        </a>
                        <a href="{{ url_for('main.orders') }}" class="profile-menu-item">
                            <i class="fas fa-shopping-bag"></i>我的订单
                        </a>
                        <a href="{{ url_for('main.products') }}" class="profile-menu-item">
                            <i class="fas fa-shopping-products"></i>商品
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-9 col-md-8">
                <div class="profile-content">
                    <div class="profile-content-header">
                        <h5 class="profile-content-title">
                            <i class="fas fa-key"></i>修改密码
                        </h5>
                    </div>
                    <div class="profile-content-body">
                        <form method="POST" id="changePasswordForm">
                            {{ form.hidden_tag() }}
                            
                            <div class="form-group">
                                <label class="form-label">{{ form.old_password.label.text }}</label>
                                {{ form.old_password(class="form-control" + (" is-invalid" if form.old_password.errors else ""), placeholder="请输入当前密码") }}
                                {% if form.old_password.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.old_password.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">{{ form.password.label.text }}</label>
                                {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else ""), placeholder="请输入新密码", id="newPassword") }}
                                {% if form.password.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.password.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="password-strength" id="passwordStrength">
                                    <div class="password-strength-bar">
                                        <div class="password-strength-fill" id="strengthBar"></div>
                                    </div>
                                    <div class="password-strength-text" id="strengthText">密码强度</div>
                                </div>
                                <div class="form-text">密码长度至少6个字符，建议包含大小写字母、数字和特殊字符</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">{{ form.password2.label.text }}</label>
                                {{ form.password2(class="form-control" + (" is-invalid" if form.password2.errors else ""), placeholder="请再次输入新密码") }}
                                {% if form.password2.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.password2.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="d-flex gap-3">
                                {{ form.submit(class="btn btn-primary") }}
                                <a href="{{ url_for('auth.profile') }}" class="btn btn-secondary">返回</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const passwordInput = document.getElementById('newPassword');
    const strengthBar = document.getElementById('strengthBar');
    const strengthText = document.getElementById('strengthText');
    
    if (passwordInput) {
        passwordInput.addEventListener('input', function() {
            const password = this.value;
            const strength = calculatePasswordStrength(password);
            updatePasswordStrength(strength);
        });
    }
    
    function calculatePasswordStrength(password) {
        let score = 0;
        const checks = {
            length: password.length >= 8,
            lowercase: /[a-z]/.test(password),
            uppercase: /[A-Z]/.test(password),
            numbers: /\d/.test(password),
            symbols: /[^A-Za-z0-9]/.test(password)
        };
        
        score = Object.values(checks).filter(Boolean).length;
        
        if (password.length >= 12) score += 1;
        if (password.length >= 16) score += 1;
        
        return Math.min(score, 5);
    }
    
    function updatePasswordStrength(strength) {
        const colors = ['#ef4444', '#f97316', '#eab308', '#22c55e', '#16a34a'];
        const texts = ['很弱', '弱', '一般', '强', '很强'];
        const widths = [20, 40, 60, 80, 100];
        
        if (strength === 0) {
            strengthBar.style.width = '0%';
            strengthBar.style.background = 'transparent';
            strengthText.textContent = '密码强度';
            return;
        }
        
        const index = Math.max(0, strength - 1);
        strengthBar.style.width = widths[index] + '%';
        strengthBar.style.background = colors[index];
        strengthText.textContent = `密码强度: ${texts[index]}`;
    }
});
</script>
{% endblock %}
