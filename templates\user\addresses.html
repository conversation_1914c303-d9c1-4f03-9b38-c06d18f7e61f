{% extends "base_modern.html" %}

{% block title %}收货地址管理 - {{ site_config.site_name }}{% endblock %}
{% block description %}管理您的收货地址，方便快速下单{% endblock %}

{% block extra_css %}
<style>
    /* ========== Address Management Styles ========== */
    .address-page {
        background: var(--gradient-light);
        min-height: 100vh;
        padding: 2rem 0;
    }
    
    .address-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
    }
    
    .page-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: var(--border-radius-xl);
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-lg);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }
    
    .page-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--gray-900);
        margin-bottom: 0.5rem;
    }
    
    .page-subtitle {
        color: var(--gray-600);
        font-size: 1rem;
    }
    
    .address-actions {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 2rem;
    }
    
    .btn-add-address {
        background: var(--primary-gradient);
        color: white;
        border: none;
        padding: 0.875rem 1.5rem;
        border-radius: var(--border-radius-lg);
        font-weight: 600;
        transition: var(--transition-base);
        box-shadow: var(--shadow-md);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .btn-add-address:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
    }
    
    .addresses-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .address-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: var(--border-radius-xl);
        padding: 1.5rem;
        box-shadow: var(--shadow-md);
        border: 2px solid transparent;
        transition: var(--transition-base);
        position: relative;
    }
    
    .address-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
        border-color: rgba(102, 126, 234, 0.2);
    }
    
    .address-card.default {
        border-color: var(--primary-500);
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
    }
    
    .default-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: var(--primary-gradient);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: var(--border-radius-full);
        font-size: 0.75rem;
        font-weight: 600;
    }
    
    .address-info {
        margin-bottom: 1rem;
    }
    
    .recipient-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 0.75rem;
    }
    
    .recipient-name {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--gray-900);
    }
    
    .recipient-phone {
        color: var(--gray-600);
        font-size: 0.9rem;
    }
    
    .address-detail {
        color: var(--gray-700);
        line-height: 1.5;
        margin-bottom: 0.5rem;
    }
    
    .postal-code {
        color: var(--gray-500);
        font-size: 0.875rem;
    }
    
    .address-actions-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 1rem;
        border-top: 1px solid var(--gray-200);
    }
    
    .address-buttons {
        display: flex;
        gap: 0.5rem;
    }
    
    .btn-action {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: var(--border-radius-md);
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: var(--transition-base);
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }
    
    .btn-edit {
        background: rgba(59, 130, 246, 0.1);
        color: var(--primary-600);
    }
    
    .btn-edit:hover {
        background: rgba(59, 130, 246, 0.2);
    }
    
    .btn-delete {
        background: rgba(239, 68, 68, 0.1);
        color: var(--danger-600);
    }
    
    .btn-delete:hover {
        background: rgba(239, 68, 68, 0.2);
    }
    
    .btn-set-default {
        background: rgba(16, 185, 129, 0.1);
        color: var(--success-600);
        font-size: 0.8rem;
    }
    
    .btn-set-default:hover {
        background: rgba(16, 185, 129, 0.2);
    }
    
    /* Empty State */
    .empty-addresses {
        text-align: center;
        padding: 4rem 2rem;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: var(--border-radius-xl);
        box-shadow: var(--shadow-md);
    }
    
    .empty-icon {
        font-size: 4rem;
        color: var(--gray-400);
        margin-bottom: 1.5rem;
    }
    
    .empty-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--gray-700);
        margin-bottom: 0.5rem;
    }
    
    .empty-description {
        color: var(--gray-500);
        margin-bottom: 2rem;
    }
    
    /* Modal Styles */
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(4px);
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        visibility: hidden;
        transition: var(--transition-base);
    }
    
    .modal-overlay.show {
        opacity: 1;
        visibility: visible;
    }
    
    .modal-content {
        background: white;
        border-radius: var(--border-radius-xl);
        padding: 2rem;
        width: 90%;
        max-width: 500px;
        max-height: 90vh;
        overflow-y: auto;
        transform: scale(0.9);
        transition: var(--transition-base);
    }
    
    .modal-overlay.show .modal-content {
        transform: scale(1);
    }
    
    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--gray-200);
    }
    
    .modal-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--gray-900);
    }
    
    .modal-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        color: var(--gray-400);
        cursor: pointer;
        padding: 0.25rem;
        border-radius: var(--border-radius-md);
        transition: var(--transition-base);
    }
    
    .modal-close:hover {
        color: var(--gray-600);
        background: var(--gray-100);
    }
    
    /* Form Styles */
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1rem;
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    .form-label {
        display: block;
        font-weight: 600;
        color: var(--gray-700);
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }
    
    .form-control {
        width: 100%;
        padding: 0.875rem 1rem;
        border: 2px solid var(--gray-200);
        border-radius: var(--border-radius-lg);
        font-size: 1rem;
        transition: var(--transition-base);
    }
    
    .form-control:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .form-check {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1.5rem;
    }
    
    .form-check-input {
        width: 1.25rem;
        height: 1.25rem;
    }
    
    .modal-actions {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        padding-top: 1.5rem;
        border-top: 1px solid var(--gray-200);
    }
    
    .btn-cancel {
        background: var(--gray-100);
        color: var(--gray-700);
        border: none;
        padding: 0.875rem 1.5rem;
        border-radius: var(--border-radius-lg);
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition-base);
    }
    
    .btn-cancel:hover {
        background: var(--gray-200);
    }
    
    .btn-save {
        background: var(--primary-gradient);
        color: white;
        border: none;
        padding: 0.875rem 1.5rem;
        border-radius: var(--border-radius-lg);
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition-base);
    }
    
    .btn-save:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-lg);
    }
    
    /* 地址选择器样式 */
    .address-selector-section {
        background: rgba(248, 250, 252, 0.8);
        border-radius: var(--border-radius-lg);
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border: 1px solid rgba(226, 232, 240, 0.8);
    }

    .region-select {
        background-color: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 0.75rem 1rem;
        font-size: 0.95rem;
        color: #2d3748;
        transition: all 0.2s ease;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23718096' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 0.75rem center;
        background-size: 16px;
        padding-right: 2.5rem;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }

    .region-select:focus {
        outline: none;
        border-color: #4299e1;
        box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
    }

    .region-select:disabled {
        background-color: #f7fafc;
        color: #a0aec0;
        cursor: not-allowed;
        border-color: #e2e8f0;
    }

    .required-mark {
        color: #e53e3e;
        font-weight: 600;
    }

    #district-container {
        position: relative;
    }

    #district-input {
        background-color: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 0.75rem 1rem;
        font-size: 0.95rem;
        color: #2d3748;
        transition: all 0.2s ease;
    }

    #district-input:focus {
        outline: none;
        border-color: #4299e1;
        box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .addresses-grid {
            grid-template-columns: 1fr;
        }

        .form-row {
            grid-template-columns: 1fr;
        }

        .address-actions-row {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }

        .address-buttons {
            justify-content: center;
        }

        .address-selector-section {
            padding: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="address-page">
    <div class="address-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">收货地址管理</h1>
            <p class="page-subtitle">管理您的收货地址，让购物更便捷</p>
        </div>
        
        <!-- 添加地址按钮 -->
        <div class="address-actions">
            <button class="btn-add-address" onclick="openAddressModal()">
                <i class="fas fa-plus"></i>
                添加新地址
            </button>
        </div>
        
        <!-- 地址列表 -->
        {% if addresses %}
            <div class="addresses-grid">
                {% for address in addresses %}
                <div class="address-card {{ 'default' if address.is_default }}">
                    {% if address.is_default %}
                        <div class="default-badge">
                            <i class="fas fa-star"></i>
                            默认地址
                        </div>
                    {% endif %}
                    
                    <div class="address-info">
                        <div class="recipient-info">
                            <span class="recipient-name">{{ address.recipient_name }}</span>
                            <span class="recipient-phone">{{ address.phone }}</span>
                        </div>
                        
                        <div class="address-detail">
                            {{ address.get_full_address() }}
                        </div>
                        
                        {% if address.postal_code %}
                            <div class="postal-code">
                                邮编: {{ address.postal_code }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="address-actions-row">
                        {% if not address.is_default %}
                            <button class="btn-action btn-set-default" onclick="setDefaultAddress({{ address.id }})">
                                <i class="fas fa-star"></i>
                                设为默认
                            </button>
                        {% else %}
                            <div></div>
                        {% endif %}
                        
                        <div class="address-buttons">
                            <button class="btn-action btn-edit" onclick="editAddress({{ address.id }})">
                                <i class="fas fa-edit"></i>
                                编辑
                            </button>
                            {% if not address.is_default %}
                                <button class="btn-action btn-delete" onclick="deleteAddress({{ address.id }})">
                                    <i class="fas fa-trash"></i>
                                    删除
                                </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-addresses">
                <div class="empty-icon">
                    <i class="fas fa-map-marker-alt"></i>
                </div>
                <h3 class="empty-title">暂无收货地址</h3>
                <p class="empty-description">添加您的第一个收货地址，让购物更便捷</p>
                <button class="btn-add-address" onclick="openAddressModal()">
                    <i class="fas fa-plus"></i>
                    添加地址
                </button>
            </div>
        {% endif %}
    </div>
</div>

<!-- 地址编辑模态框 -->
<div class="modal-overlay" id="addressModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title" id="modalTitle">添加地址</h3>
            <button class="modal-close" onclick="closeAddressModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <form id="addressForm">
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label" for="recipientName">收货人姓名</label>
                    <input type="text" class="form-control" id="recipientName" name="recipient_name" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="phone">联系电话</label>
                    <input type="tel" class="form-control" id="phone" name="phone" required>
                </div>
            </div>
            
            <!-- 地址选择区域 -->
            <div class="address-selector-section">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="province">省份 <span class="required-mark">*</span></label>
                        <select class="form-control region-select" id="province" name="province" required>
                            <option value="">请选择省份</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="city">城市 <span class="required-mark">*</span></label>
                        <select class="form-control region-select" id="city" name="city" required>
                            <option value="">请选择城市</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="district">区县</label>
                        <div id="district-container">
                            <select class="form-control region-select" id="district" name="district" style="display: none;">
                                <option value="">请选择区县</option>
                            </select>
                            <input type="text" class="form-control" id="district-input" name="district_input"
                                   placeholder="请输入区县名称" style="display: none;">
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="postalCode">邮政编码</label>
                        <input type="text" class="form-control" id="postalCode" name="postal_code"
                               placeholder="请输入邮政编码">
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="addressDetail">详细地址</label>
                <input type="text" class="form-control" id="addressDetail" name="address_detail" 
                       placeholder="街道门牌号等详细信息" required>
            </div>
            
            <div class="form-check">
                <input type="checkbox" class="form-check-input" id="isDefault" name="is_default">
                <label class="form-check-label" for="isDefault">设为默认地址</label>
            </div>
            
            <div class="modal-actions">
                <button type="button" class="btn-cancel" onclick="closeAddressModal()">取消</button>
                <button type="submit" class="btn-save">保存</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/simple-region-selector.js') }}"></script>
<script>
let currentAddressId = null;
let regionSelector = null;

// 初始化地址选择器
function initRegionSelector() {
    if (regionSelector) {
        return; // 已经初始化过了
    }

    try {
        regionSelector = new SimpleRegionSelector({
            provinceSelector: '#province',
            citySelector: '#city',
            districtSelector: '#district',
            provincePlaceholder: '请选择省份',
            cityPlaceholder: '请选择城市',
            districtPlaceholder: '请选择区县',
            onChange: function(region) {
                console.log('地址选择变化:', region);
                // 可以在这里添加额外的处理逻辑
            }
        });
        console.log('地址选择器初始化成功');
    } catch (error) {
        console.error('地址选择器初始化失败:', error);
    }
}

// 设置地址数据到选择器
async function setAddressData(addressData) {
    if (!regionSelector || !addressData) {
        return;
    }

    try {
        console.log('设置地址数据:', addressData);

        // 首先选择省份
        const provinceSelect = document.getElementById('province');
        for (let option of provinceSelect.options) {
            if (option.text === addressData.province) {
                provinceSelect.value = option.value;
                // 触发省份变化事件
                await regionSelector.onProvinceChange();
                break;
            }
        }

        // 等待城市数据加载完成后选择城市
        setTimeout(async () => {
            const citySelect = document.getElementById('city');
            for (let option of citySelect.options) {
                if (option.text === addressData.city) {
                    citySelect.value = option.value;
                    // 触发城市变化事件
                    await regionSelector.onCityChange();
                    break;
                }
            }

            // 等待区县数据加载完成后设置区县
            setTimeout(() => {
                const districtInput = document.getElementById('district-input');
                const districtSelect = document.getElementById('district');

                if (districtInput && districtInput.style.display !== 'none') {
                    // 使用输入框模式
                    districtInput.value = addressData.district;
                } else if (districtSelect && districtSelect.style.display !== 'none') {
                    // 使用选择框模式
                    for (let option of districtSelect.options) {
                        if (option.text === addressData.district) {
                            districtSelect.value = option.value;
                            break;
                        }
                    }
                }
            }, 500);
        }, 500);

    } catch (error) {
        console.error('设置地址数据失败:', error);
    }
}

// 打开地址模态框
function openAddressModal(addressData = null) {
    const modal = document.getElementById('addressModal');
    const title = document.getElementById('modalTitle');
    const form = document.getElementById('addressForm');

    if (addressData) {
        // 编辑模式
        title.textContent = '编辑地址';
        currentAddressId = addressData.id;

        // 填充表单数据
        document.getElementById('recipientName').value = addressData.recipient_name;
        document.getElementById('phone').value = addressData.phone;
        document.getElementById('addressDetail').value = addressData.address_detail;
        document.getElementById('postalCode').value = addressData.postal_code || '';
        document.getElementById('isDefault').checked = addressData.is_default;

        // 延迟设置地址信息，等待选择器初始化完成
        setTimeout(() => {
            if (regionSelector) {
                setAddressData(addressData);
            }
        }, 1000);
    } else {
        // 添加模式
        title.textContent = '添加地址';
        currentAddressId = null;
        form.reset();

        // 重置地址选择器
        if (regionSelector) {
            regionSelector.reset();
        }
    }

    modal.classList.add('show');

    // 初始化地址选择器（如果还没有初始化）
    initRegionSelector();
}

// 关闭地址模态框
function closeAddressModal() {
    const modal = document.getElementById('addressModal');
    modal.classList.remove('show');
    currentAddressId = null;
}

// 编辑地址
function editAddress(addressId) {
    fetch(`/api/addresses/${addressId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            openAddressModal(data.address);
        } else {
            window.ModernNotification.error(data.message);
        }
    })
    .catch(error => {
        console.error('获取地址失败:', error);
        window.ModernNotification.error('获取地址信息失败');
    });
}

// 删除地址
function deleteAddress(addressId) {
    if (!confirm('确定要删除这个地址吗？')) {
        return;
    }
    
    fetch(`/api/addresses/${addressId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.ModernNotification.success(data.message);
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            window.ModernNotification.error(data.message);
        }
    })
    .catch(error => {
        console.error('删除地址失败:', error);
        window.ModernNotification.error('删除地址失败');
    });
}

// 设置默认地址
function setDefaultAddress(addressId) {
    fetch(`/api/addresses/${addressId}/set-default`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.ModernNotification.success(data.message);
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            window.ModernNotification.error(data.message);
        }
    })
    .catch(error => {
        console.error('设置默认地址失败:', error);
        window.ModernNotification.error('设置默认地址失败');
    });
}

// 表单提交
document.getElementById('addressForm').addEventListener('submit', function(e) {
    e.preventDefault();

    // 获取表单数据
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    data.is_default = document.getElementById('isDefault').checked;

    // 获取地址选择器的值
    if (regionSelector) {
        const selectedRegion = regionSelector.getSelectedRegion();
        data.province = selectedRegion.province.name || '';
        data.city = selectedRegion.city.name || '';
        data.district = selectedRegion.district.name || '';
    } else {
        // 如果选择器未初始化，从表单元素直接获取
        const provinceSelect = document.getElementById('province');
        const citySelect = document.getElementById('city');
        const districtSelect = document.getElementById('district');
        const districtInput = document.getElementById('district-input');

        data.province = provinceSelect.options[provinceSelect.selectedIndex]?.text || '';
        data.city = citySelect.options[citySelect.selectedIndex]?.text || '';

        if (districtInput.style.display !== 'none') {
            data.district = districtInput.value || '';
        } else {
            data.district = districtSelect.options[districtSelect.selectedIndex]?.text || '';
        }
    }

    // 验证必填字段
    if (!data.province || !data.city) {
        window.ModernNotification.error('请选择省份和城市');
        return;
    }

    const url = currentAddressId ? `/api/addresses/${currentAddressId}` : '/api/addresses';
    const method = currentAddressId ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.ModernNotification.success(data.message);
            closeAddressModal();
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            window.ModernNotification.error(data.message);
        }
    })
    .catch(error => {
        console.error('保存地址失败:', error);
        window.ModernNotification.error('保存地址失败');
    });
});

// 点击模态框外部关闭
document.getElementById('addressModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeAddressModal();
    }
});

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('地址管理页面加载完成');
    // 地址选择器将在打开模态框时初始化，以避免不必要的资源加载
});
</script>
{% endblock %}