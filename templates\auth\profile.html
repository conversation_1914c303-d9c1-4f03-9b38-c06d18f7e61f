{% extends "base_modern.html" %}

{% block title %}个人资料 - {{ site_config.site_name }}{% endblock %}

{% block extra_css %}
<style>
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --glass-bg: rgba(255, 255, 255, 0.1);
        --glass-border: rgba(255, 255, 255, 0.2);
        --shadow-light: rgba(0, 0, 0, 0.1);
    }

    .profile-container {
        background: var(--primary-gradient);
        min-height: 100vh;
        padding: 2rem 0;
        position: relative;
        overflow: hidden;
    }

    .profile-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: 
            radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 118, 117, 0.3) 0%, transparent 50%);
        pointer-events: none;
    }

    .profile-sidebar {
        background: var(--glass-bg);
        backdrop-filter: blur(20px);
        border: 1px solid var(--glass-border);
        border-radius: 20px;
        padding: 0;
        overflow: hidden;
        box-shadow: 0 8px 32px var(--shadow-light);
    }

    .profile-menu-header {
        background: rgba(255, 255, 255, 0.1);
        padding: 1.5rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .profile-menu-title {
        color: white;
        font-weight: 600;
        margin: 0;
        font-size: 1.125rem;
    }

    .profile-menu-item {
        background: transparent;
        border: none;
        color: rgba(255, 255, 255, 0.8);
        padding: 1rem 1.5rem;
        display: flex;
        align-items: center;
        text-decoration: none;
        transition: all 0.3s ease;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    }

    .profile-menu-item:hover {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        transform: translateX(5px);
    }

    .profile-menu-item.active {
        background: var(--success-gradient);
        color: white;
        box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
    }

    .profile-menu-item i {
        margin-right: 0.75rem;
        font-size: 1rem;
    }

    .profile-content {
        background: var(--glass-bg);
        backdrop-filter: blur(20px);
        border: 1px solid var(--glass-border);
        border-radius: 20px;
        box-shadow: 0 8px 32px var(--shadow-light);
        overflow: hidden;
    }

    .profile-content-header {
        background: rgba(255, 255, 255, 0.1);
        padding: 1.5rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .profile-content-title {
        color: white;
        font-weight: 600;
        margin: 0;
        font-size: 1.25rem;
        display: flex;
        align-items: center;
    }

    .profile-content-title i {
        margin-right: 0.75rem;
        color: #4facfe;
    }

    .profile-content-body {
        padding: 2rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        color: white;
        font-weight: 500;
        margin-bottom: 0.5rem;
        display: block;
    }

    .form-control {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        color: white;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        background: rgba(255, 255, 255, 0.15);
        border-color: #4facfe;
        box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.2);
        color: white;
    }

    .form-control::placeholder {
        color: rgba(255, 255, 255, 0.5);
    }

    .form-control.is-invalid {
        border-color: #ef4444;
    }

    .form-control.is-valid {
        border-color: #22c55e;
    }

    .form-control:read-only {
        background: rgba(255, 255, 255, 0.05);
        color: rgba(255, 255, 255, 0.7);
    }

    .invalid-feedback {
        color: #fecaca;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .form-text {
        color: rgba(255, 255, 255, 0.6);
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .btn-primary {
        background: var(--success-gradient);
        border: none;
        border-radius: 12px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        color: white;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
    }

    .btn-secondary {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        color: white;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }

    .btn-secondary:hover {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        transform: translateY(-2px);
    }

    @media (max-width: 768px) {
        .profile-container {
            padding: 1rem 0;
        }
        
        .profile-content-body {
            padding: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="profile-container">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 col-md-4 mb-4">
                <!-- 用户菜单 -->
                <div class="profile-sidebar">
                    <div class="profile-menu-header">
                        <h6 class="profile-menu-title">
                            <i class="fas fa-user-circle me-2"></i>个人中心
                        </h6>
                    </div>
                    <div class="profile-menu">
                        <a href="{{ url_for('auth.profile') }}" class="profile-menu-item active">
                            <i class="fas fa-user"></i>个人资料
                        </a>
                        <a href="{{ url_for('auth.change_password') }}" class="profile-menu-item">
                            <i class="fas fa-lock"></i>修改密码
                        </a>
                        <a href="{{ url_for('main.orders') }}" class="profile-menu-item">
                            <i class="fas fa-shopping-bag"></i>我的订单
                        </a>
                        <a href="{{ url_for('main.products') }}" class="profile-menu-item">
                            <i class="fas fa-box"></i>商品
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-9 col-md-8">
                <div class="profile-content">
                    <div class="profile-content-header">
                        <h5 class="profile-content-title">
                            <i class="fas fa-user-edit"></i>个人资料
                        </h5>
                    </div>
                    <div class="profile-content-body">
                        <form method="POST">
                            {{ form.hidden_tag() }}
                            
                            <div class="form-group">
                                <label class="form-label">用户名</label>
                                <input type="text" class="form-control" value="{{ current_user.username }}" readonly>
                                <div class="form-text">用户名不可修改</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">邮箱</label>
                                <input type="email" class="form-control" value="{{ current_user.email }}" readonly>
                                <div class="form-text">邮箱不可修改</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">{{ form.real_name.label.text }}</label>
                                {{ form.real_name(class="form-control" + (" is-invalid" if form.real_name.errors else ""), placeholder="请输入真实姓名") }}
                                {% if form.real_name.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.real_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">{{ form.phone.label.text }}</label>
                                {{ form.phone(class="form-control" + (" is-invalid" if form.phone.errors else ""), pattern="^1[3-9]\\d{9}$", placeholder="请输入11位手机号码", id="phoneInput") }}
                                {% if form.phone.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.phone.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">请输入有效的中国大陆手机号码（11位数字，以1开头）</div>
                                <div class="invalid-feedback" id="phoneError" style="display: none;">
                                    请输入正确的手机号格式（例如：13812345678）
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">注册时间</label>
                                <input type="text" class="form-control" value="{{ format_china_time(current_user.created_at) }}" readonly>
                            </div>
                            
                            <div class="d-flex gap-3">
                                {{ form.submit(class="btn btn-primary") }}
                                <a href="{{ url_for('main.index') }}" class="btn btn-secondary">返回首页</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const phoneInput = document.getElementById('phoneInput');
    const phoneError = document.getElementById('phoneError');
    
    if (phoneInput) {
        // 实时验证手机号
        phoneInput.addEventListener('input', function() {
            validatePhone();
        });
        
        phoneInput.addEventListener('blur', function() {
            validatePhone();
        });
        
        // 表单提交时验证
        const form = phoneInput.closest('form');
        if (form) {
            form.addEventListener('submit', function(e) {
                if (!validatePhone()) {
                    e.preventDefault();
                    phoneInput.focus();
                }
            });
        }
    }
    
    function validatePhone() {
        const phone = phoneInput.value.trim();
        const phoneRegex = /^1[3-9]\d{9}$/;
        
        // 清除之前的验证状态
        phoneInput.classList.remove('is-valid', 'is-invalid');
        phoneError.style.display = 'none';
        
        if (phone === '') {
            // 空值不显示错误（可选字段）
            return true;
        }
        
        if (!phoneRegex.test(phone)) {
            phoneInput.classList.add('is-invalid');
            phoneError.style.display = 'block';
            return false;
        } else {
            phoneInput.classList.add('is-valid');
            return true;
        }
    }
    
    // 只允许输入数字
    if (phoneInput) {
        phoneInput.addEventListener('keypress', function(e) {
            // 允许数字、退格键、删除键、方向键等
            if (!/[0-9]/.test(e.key) && !['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(e.key)) {
                e.preventDefault();
            }
        });
        
        // 限制最大长度为11
        phoneInput.addEventListener('input', function(e) {
            if (this.value.length > 11) {
                this.value = this.value.slice(0, 11);
            }
        });
    }
});
</script>
{% endblock %}
