/**
 * 自定义属性显示组件
 * 用于增强自定义属性在前端的显示效果
 */

class CustomAttributesDisplay {
    constructor(options = {}) {
        this.options = {
            highlightCustom: true,
            showBadges: true,
            animateOnLoad: true,
            ...options
        };
        
        this.init();
    }
    
    init() {
        this.enhanceAttributeDisplay();
        this.addEventListeners();
        
        if (this.options.animateOnLoad) {
            this.animateCustomAttributes();
        }
    }
    
    /**
     * 增强属性显示效果
     */
    enhanceAttributeDisplay() {
        // 查找所有包含自定义属性的元素
        const attributeElements = document.querySelectorAll('[data-has-custom="true"], .has-custom-attributes');
        
        attributeElements.forEach(element => {
            this.processAttributeElement(element);
        });
        
        // 处理属性详情元素
        const detailElements = document.querySelectorAll('.attribute-details, .attribute-details-admin');
        detailElements.forEach(element => {
            this.processDetailElement(element);
        });
    }
    
    /**
     * 处理属性元素
     */
    processAttributeElement(element) {
        if (this.options.highlightCustom) {
            element.classList.add('custom-attribute-highlight');
        }
        
        // 添加自定义属性图标
        if (this.options.showBadges && !element.querySelector('.custom-indicator')) {
            const indicator = document.createElement('span');
            indicator.className = 'custom-indicator badge bg-warning text-dark ms-2';
            indicator.innerHTML = '<i class="fas fa-edit me-1"></i>自定义';
            element.appendChild(indicator);
        }
    }
    
    /**
     * 处理详情元素
     */
    processDetailElement(element) {
        const customBadges = element.querySelectorAll('.badge.bg-warning, .badge.bg-danger');
        
        customBadges.forEach(badge => {
            if (badge.textContent.includes('自定义') || badge.textContent.includes('custom')) {
                badge.style.animation = 'pulse 2s infinite';
            }
        });
    }
    
    /**
     * 添加事件监听器
     */
    addEventListeners() {
        // 点击自定义属性时显示详细信息
        document.addEventListener('click', (e) => {
            if (e.target.closest('.custom-attribute-highlight, .custom-indicator')) {
                this.showCustomAttributeDetails(e.target);
            }
        });
        
        // 鼠标悬停效果
        document.addEventListener('mouseover', (e) => {
            if (e.target.closest('.custom-attribute-highlight')) {
                e.target.closest('.custom-attribute-highlight').classList.add('custom-hover');
            }
        });
        
        document.addEventListener('mouseout', (e) => {
            if (e.target.closest('.custom-attribute-highlight')) {
                e.target.closest('.custom-attribute-highlight').classList.remove('custom-hover');
            }
        });
    }
    
    /**
     * 显示自定义属性详细信息
     */
    showCustomAttributeDetails(element) {
        const orderId = this.extractOrderId(element);
        if (!orderId) return;
        
        // 创建模态框显示详细信息
        this.createDetailModal(orderId);
    }
    
    /**
     * 提取订单ID
     */
    extractOrderId(element) {
        // 从各种可能的位置提取订单ID
        const orderElement = element.closest('[data-order-id]');
        if (orderElement) {
            return orderElement.dataset.orderId;
        }
        
        // 从URL中提取
        const urlMatch = window.location.pathname.match(/\/order\/(\d+)/);
        if (urlMatch) {
            return urlMatch[1];
        }
        
        return null;
    }
    
    /**
     * 创建详情模态框
     */
    async createDetailModal(orderId) {
        try {
            const response = await fetch(`/api/order-custom-attributes/${orderId}`);
            const result = await response.json();
            
            if (result.success && result.data.has_custom_attributes) {
                this.showModal(result.data);
            } else {
                this.showToast('此订单不包含自定义属性', 'info');
            }
        } catch (error) {
            console.error('获取自定义属性详情失败:', error);
            this.showToast('获取详情失败，请重试', 'error');
        }
    }
    
    /**
     * 显示模态框
     */
    showModal(data) {
        const modalHtml = `
            <div class="modal fade" id="customAttributesModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-edit me-2"></i>自定义属性详情
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <strong>订单号：</strong>${data.order_no}
                            </div>
                            ${this.renderCustomItems(data.custom_items)}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 移除已存在的模态框
        const existingModal = document.getElementById('customAttributesModal');
        if (existingModal) {
            existingModal.remove();
        }
        
        // 添加新模态框
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('customAttributesModal'));
        modal.show();
    }
    
    /**
     * 渲染自定义项目
     */
    renderCustomItems(items) {
        return items.map(item => `
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">${item.product_name} (数量: ${item.quantity})</h6>
                </div>
                <div class="card-body">
                    ${item.custom_attributes.map(attr => `
                        <div class="row mb-2">
                            <div class="col-4">
                                <span class="badge bg-danger text-white">自定义</span>
                                <strong>${attr.name}:</strong>
                            </div>
                            <div class="col-8">
                                <span class="text-primary">${attr.value}</span>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `).join('');
    }
    
    /**
     * 动画效果
     */
    animateCustomAttributes() {
        const customElements = document.querySelectorAll('.custom-attribute-highlight, .badge.bg-warning');
        
        customElements.forEach((element, index) => {
            setTimeout(() => {
                element.style.animation = 'fadeInUp 0.5s ease-out';
            }, index * 100);
        });
    }
    
    /**
     * 显示提示消息
     */
    showToast(message, type = 'info') {
        const toastHtml = `
            <div class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;
        
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }
        
        toastContainer.insertAdjacentHTML('beforeend', toastHtml);
        
        const toastElement = toastContainer.lastElementChild;
        const toast = new bootstrap.Toast(toastElement);
        toast.show();
        
        // 自动移除
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    }
}

// 添加CSS样式
const customAttributesCSS = `
    .custom-attribute-highlight {
        background-color: #fff3cd !important;
        border: 1px solid #ffeaa7 !important;
        border-radius: 0.25rem !important;
        transition: all 0.3s ease !important;
    }
    
    .custom-attribute-highlight.custom-hover {
        background-color: #ffeaa7 !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3) !important;
    }
    
    .custom-indicator {
        cursor: pointer !important;
        transition: all 0.3s ease !important;
    }
    
    .custom-indicator:hover {
        transform: scale(1.05) !important;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;

// 注入CSS样式
if (!document.getElementById('custom-attributes-styles')) {
    const styleElement = document.createElement('style');
    styleElement.id = 'custom-attributes-styles';
    styleElement.textContent = customAttributesCSS;
    document.head.appendChild(styleElement);
}

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
    window.customAttributesDisplay = new CustomAttributesDisplay();
});

// 导出类供其他脚本使用
window.CustomAttributesDisplay = CustomAttributesDisplay;
