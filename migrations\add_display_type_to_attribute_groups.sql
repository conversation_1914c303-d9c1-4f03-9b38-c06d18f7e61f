-- 为属性组表添加显示类型字段的迁移脚本
-- 执行前请备份数据库

-- 1. 检查字段是否已存在，如果不存在则添加
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'attribute_groups'
    AND COLUMN_NAME = 'display_type'
);

-- 2. 如果字段不存在，则添加字段
SET @sql = IF(@column_exists = 0,
    "ALTER TABLE attribute_groups ADD COLUMN display_type ENUM('radio', 'select') NOT NULL DEFAULT 'radio' AFTER description",
    'SELECT "display_type column already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 更新现有数据（可选：根据属性组名称设置合适的显示类型）
-- 将包含"规格"、"尺寸"、"型号"等词的属性组设置为下拉框显示
UPDATE attribute_groups 
SET display_type = 'select' 
WHERE (name LIKE '%规格%' OR name LIKE '%尺寸%' OR name LIKE '%型号%' OR name LIKE '%联数%') 
AND display_type = 'radio';

-- 显示迁移结果
SELECT
    'Migration completed successfully' as status,
    COUNT(*) as total_groups,
    SUM(CASE WHEN display_type = 'radio' THEN 1 ELSE 0 END) as radio_groups,
    SUM(CASE WHEN display_type = 'select' THEN 1 ELSE 0 END) as select_groups
FROM attribute_groups; 