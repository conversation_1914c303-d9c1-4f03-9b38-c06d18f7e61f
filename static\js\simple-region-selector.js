/**
 * 简化版省市县三级联动选择器
 * 重新设计，更加可靠和简单
 */
class SimpleRegionSelector {
    constructor(options) {
        this.options = {
            provinceSelector: '#province',
            citySelector: '#city', 
            districtSelector: '#district',
            provincePlaceholder: '请选择省份',
            cityPlaceholder: '请选择城市',
            districtPlaceholder: '请选择区县',
            onChange: null,
            ...options
        };
        
        this.provinceSelect = document.querySelector(this.options.provinceSelector);
        this.citySelect = document.querySelector(this.options.citySelector);
        this.districtSelect = document.querySelector(this.options.districtSelector);
        this.districtInput = document.querySelector('#district-input');
        this.districtContainer = document.querySelector('#district-container');

        if (!this.provinceSelect || !this.citySelect) {
            console.error('找不到选择器元素');
            return;
        }

        // 初始化区县组件状态
        this.districtMode = 'select'; // 'select' 或 'input'
        
        this.init();
    }
    
    init() {
        console.log('初始化简化版区域选择器...');
        
        // 绑定事件
        this.provinceSelect.addEventListener('change', () => this.onProvinceChange());
        this.citySelect.addEventListener('change', () => this.onCityChange());

        if (this.districtSelect) {
            this.districtSelect.addEventListener('change', () => this.onDistrictChange());
        }
        if (this.districtInput) {
            this.districtInput.addEventListener('input', () => this.onDistrictInputChange());
            this.districtInput.addEventListener('blur', () => this.onDistrictChange());
        }

        // 初始化选择框
        this.resetSelect(this.provinceSelect, this.options.provincePlaceholder);
        this.resetSelect(this.citySelect, this.options.cityPlaceholder);
        this.initDistrictComponent();

        // 禁用城市和区县组件
        this.citySelect.disabled = true;
        this.setDistrictDisabled(true);
        
        // 加载省份数据
        this.loadProvinces();
    }
    
    resetSelect(select, placeholder) {
        select.innerHTML = `<option value="">${placeholder}</option>`;
    }

    // 重置整个选择器到初始状态
    reset() {
        this.resetSelect(this.provinceSelect, this.options.provincePlaceholder);
        this.resetSelect(this.citySelect, this.options.cityPlaceholder);
        this.initDistrictComponent();
        this.citySelect.disabled = true;
        this.setDistrictDisabled(true);
        this.loadProvinces();
    }

    initDistrictComponent() {
        // 初始化时隐藏所有区县组件
        if (this.districtSelect) {
            this.districtSelect.style.display = 'none';
            this.resetSelect(this.districtSelect, this.options.districtPlaceholder);
        }
        if (this.districtInput) {
            this.districtInput.style.display = 'none';
            this.districtInput.placeholder = '请先选择城市';
            this.districtInput.value = '';
        }
    }

    switchDistrictMode(mode, hasDistricts = false) {
        this.districtMode = mode;

        if (mode === 'select' && hasDistricts) {
            // 显示选择框，隐藏输入框
            if (this.districtSelect) {
                this.districtSelect.style.display = 'block';
                this.districtSelect.disabled = false;
            }
            if (this.districtInput) {
                this.districtInput.style.display = 'none';
            }
        } else {
            // 显示输入框，隐藏选择框
            if (this.districtSelect) {
                this.districtSelect.style.display = 'none';
            }
            if (this.districtInput) {
                this.districtInput.style.display = 'block';
                this.districtInput.disabled = false;
                this.districtInput.placeholder = hasDistricts ? '请选择区县' : '请输入区县名称';
                // 重置样式（当从无区县模式切换回来时）
                this.districtInput.readOnly = false;
                this.districtInput.style.backgroundColor = '';
                this.districtInput.style.cursor = '';
            }
        }
    }

    setDistrictDisabled(disabled) {
        if (this.districtSelect) {
            this.districtSelect.disabled = disabled;
        }
        if (this.districtInput) {
            this.districtInput.disabled = disabled;
            if (disabled) {
                this.districtInput.placeholder = '请先选择城市';
            }
        }
    }
    
    async loadProvinces() {
        console.log('开始加载省份数据...');
        try {
            const response = await fetch('/api/regions/provinces');
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const provinces = await response.json();
            console.log('省份数据加载成功:', provinces.length, '个省份');
            
            this.renderProvinces(provinces);
        } catch (error) {
            console.error('加载省份数据失败:', error);
            this.showError('加载省份数据失败');
        }
    }
    
    renderProvinces(provinces) {
        this.resetSelect(this.provinceSelect, this.options.provincePlaceholder);
        
        provinces.forEach(province => {
            const option = document.createElement('option');
            option.value = province.id;
            option.textContent = province.name;
            this.provinceSelect.appendChild(option);
        });
        
        console.log('省份选项渲染完成');
    }
    
    async onProvinceChange() {
        const provinceId = this.provinceSelect.value;
        console.log('省份选择变化:', provinceId);

        // 重置城市和区县
        this.resetSelect(this.citySelect, this.options.cityPlaceholder);
        this.initDistrictComponent();
        this.citySelect.disabled = true;
        this.setDistrictDisabled(true);

        if (!provinceId) {
            this.triggerChange();
            return;
        }

        await this.loadCities(provinceId);
    }
    
    async loadCities(provinceId) {
        console.log('开始加载城市数据，省份ID:', provinceId);
        try {
            const response = await fetch(`/api/regions/cities/${provinceId}`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const cities = await response.json();
            console.log('城市数据加载成功:', cities.length, '个城市');
            
            this.renderCities(cities);
        } catch (error) {
            console.error('加载城市数据失败:', error);
            this.showError('加载城市数据失败');
        }
    }
    
    renderCities(cities) {
        this.resetSelect(this.citySelect, this.options.cityPlaceholder);
        
        cities.forEach(city => {
            const option = document.createElement('option');
            option.value = city.id;
            option.textContent = city.name;
            this.citySelect.appendChild(option);
        });
        
        this.citySelect.disabled = cities.length === 0;
        console.log('城市选项渲染完成');
        this.triggerChange();
    }
    
    async onCityChange() {
        const cityId = this.citySelect.value;
        console.log('城市选择变化:', cityId);

        // 重置区县组件
        this.initDistrictComponent();
        this.setDistrictDisabled(true);

        if (!cityId) {
            this.triggerChange();
            return;
        }

        await this.loadDistricts(cityId);
    }
    
    async loadDistricts(cityId) {
        console.log('开始加载区县数据，城市ID:', cityId);
        try {
            const response = await fetch(`/api/regions/districts/${cityId}`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const districts = await response.json();
            console.log('区县数据加载成功:', districts.length, '个区县');
            
            this.renderDistricts(districts);
        } catch (error) {
            console.error('加载区县数据失败:', error);
            this.showError('加载区县数据失败');
        }
    }
    
    renderDistricts(districts) {
        if (districts.length > 0) {
            // 有区县数据，使用选择框
            console.log('发现区县数据，使用选择框模式');
            this.resetSelect(this.districtSelect, this.options.districtPlaceholder);

            districts.forEach(district => {
                const option = document.createElement('option');
                option.value = district.id;
                option.textContent = district.name;
                this.districtSelect.appendChild(option);
            });

            this.switchDistrictMode('select', true);
        } else {
            // 没有区县数据，使用输入框模式，并设置默认值为空格
            console.log('未发现区县数据，使用输入框模式');
            this.switchDistrictMode('input', false);

            // 设置默认值为空格
            if (this.districtInput) {
                this.districtInput.value = ' ';
                this.districtInput.placeholder = '该城市无区县数据';
                this.districtInput.readOnly = true;  // 设置为只读，防止用户修改
                this.districtInput.style.backgroundColor = '#f8f9fa';  // 视觉提示
                this.districtInput.style.cursor = 'not-allowed';
            }
        }

        console.log('区县组件渲染完成，模式:', this.districtMode);
        this.triggerChange();
    }
    
    onDistrictChange() {
        if (this.districtMode === 'select') {
            console.log('区县选择变化:', this.districtSelect.value);
        } else {
            console.log('区县输入变化:', this.districtInput.value);
        }
        this.triggerChange();
    }

    onDistrictInputChange() {
        // 实时输入变化，可以在这里添加输入验证等逻辑
        console.log('区县输入实时变化:', this.districtInput.value);
    }
    
    triggerChange() {
        if (typeof this.options.onChange === 'function') {
            const province = this.provinceSelect.options[this.provinceSelect.selectedIndex];
            const city = this.citySelect.options[this.citySelect.selectedIndex];

            let districtData = { id: '', name: '' };

            if (this.districtMode === 'select' && this.districtSelect) {
                const district = this.districtSelect.options[this.districtSelect.selectedIndex];
                districtData = {
                    id: this.districtSelect.value,
                    name: district && district.value ? district.textContent : ''
                };
            } else if (this.districtMode === 'input' && this.districtInput) {
                districtData = {
                    id: '', // 输入模式没有ID
                    name: this.districtInput.value.trim()
                };
            }

            const result = {
                province: {
                    id: this.provinceSelect.value,
                    name: province && province.value ? province.textContent : ''
                },
                city: {
                    id: this.citySelect.value,
                    name: city && city.value ? city.textContent : ''
                },
                district: districtData,
                districtMode: this.districtMode // 添加模式信息
            };

            console.log('触发变化事件:', result);
            this.options.onChange(result);
        }
    }
    
    showError(message) {
        console.error(message);
        alert(message); // 简单的错误提示
    }
    
    getSelectedRegion() {
        const province = this.provinceSelect.options[this.provinceSelect.selectedIndex];
        const city = this.citySelect.options[this.citySelect.selectedIndex];

        let districtData = { id: '', name: '' };

        if (this.districtMode === 'select' && this.districtSelect) {
            const district = this.districtSelect.options[this.districtSelect.selectedIndex];
            districtData = {
                id: this.districtSelect.value,
                name: district && district.value ? district.textContent : ''
            };
        } else if (this.districtMode === 'input' && this.districtInput) {
            const inputValue = this.districtInput.value.trim();
            districtData = {
                id: '', // 输入模式没有ID
                name: inputValue === '' || inputValue === ' ' ? ' ' : inputValue
            };
        }

        return {
            province: {
                id: this.provinceSelect.value,
                name: province && province.value ? province.textContent : ''
            },
            city: {
                id: this.citySelect.value,
                name: city && city.value ? city.textContent : ''
            },
            district: districtData,
            districtMode: this.districtMode
        };
    }
}

// 挂载到全局
if (typeof window !== 'undefined') {
    window.SimpleRegionSelector = SimpleRegionSelector;
}
