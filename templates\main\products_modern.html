{% extends "base_modern.html" %}

{% block title %}商品中心 - {{ site_config.site_name }}{% endblock %}
{% block description %}浏览我们的专业印刷产品，包括纸箱包装、宣传册印刷等定制化服务{% endblock %}

{% block extra_css %}
<style>
    /* ========== Page Layout ========== */
    .products-page {
        background: var(--gradient-light);
        min-height: 100vh;
        padding: 2rem 0;
    }
    
    .products-container {
        display: flex;
        gap: 2rem;
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 1rem;
    }
    
    /* ========== Left Sidebar ========== */
    .sidebar {
        width: 280px;
        flex-shrink: 0;
    }
    
    .sidebar-section {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-radius: var(--border-radius-xl);
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: var(--shadow-md);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }
    
    .sidebar-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--gray-800);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .sidebar-title::before {
        content: '';
        width: 4px;
        height: 1.25rem;
        background: var(--primary-gradient);
        border-radius: 2px;
    }
    
    /* ========== Category Menu ========== */
    .category-menu {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .category-item {
        margin-bottom: 0.5rem;
    }
    
    .category-link {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        border-radius: var(--border-radius-lg);
        text-decoration: none;
        color: var(--gray-700);
        transition: var(--transition-base);
        position: relative;
        overflow: hidden;
    }
    
    .category-link::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 0;
        height: 100%;
        background: var(--primary-gradient);
        transition: var(--transition-base);
        z-index: -1;
    }
    
    .category-link:hover,
    .category-link.active {
        color: white;
        transform: translateX(5px);
    }
    
    .category-link:hover::before,
    .category-link.active::before {
        width: 100%;
    }
    
    .category-icon {
        margin-right: 0.75rem;
        font-size: 1rem;
        width: 1.25rem;
        text-align: center;
    }
    
    /* ========== Search Box ========== */
    .search-box {
        position: relative;
        margin-bottom: 1rem;
    }
    
    .search-input {
        width: 100%;
        padding: 0.75rem 1rem 0.75rem 2.5rem;
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: var(--border-radius-lg);
        background: rgba(255, 255, 255, 0.9);
        font-size: 0.9rem;
        transition: var(--transition-base);
    }
    
    .search-input:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .search-icon {
        position: absolute;
        left: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--gray-400);
        font-size: 0.9rem;
    }
    
    /* ========== Sort Options ========== */
    .sort-select {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: var(--border-radius-lg);
        background: rgba(255, 255, 255, 0.9);
        font-size: 0.9rem;
        transition: var(--transition-base);
    }
    
    .sort-select:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    /* ========== Main Content ========== */
    .main-content {
        flex: 1;
        min-width: 0;
    }
    
    .content-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding: 0 0.5rem;
    }
    
    .page-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--gray-900);
        margin: 0;
    }
    
    .view-toggle {
        display: flex;
        background: rgba(255, 255, 255, 0.9);
        border-radius: var(--border-radius-lg);
        padding: 0.25rem;
        box-shadow: var(--shadow-sm);
    }
    
    .view-btn {
        background: none;
        border: none;
        padding: 0.5rem 0.75rem;
        border-radius: var(--border-radius-md);
        color: var(--gray-600);
        transition: var(--transition-base);
        cursor: pointer;
        font-size: 0.9rem;
    }
    
    .view-btn.active {
        background: var(--primary-gradient);
        color: white;
        box-shadow: var(--shadow-sm);
    }
    
    /* ========== Product Cards ========== */
    .products-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
        max-width: 100%;
    }

    /* 确保每行最多4个商品 */
    @media (min-width: 1400px) {
        .products-grid {
            grid-template-columns: repeat(4, 1fr);
        }
    }

    @media (min-width: 1000px) and (max-width: 1399px) {
        .products-grid {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    @media (min-width: 768px) and (max-width: 999px) {
        .products-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }
    
    .product-card {
        background: white;
        border-radius: var(--border-radius-xl);
        overflow: hidden;
        box-shadow: var(--shadow-md);
        transition: var(--transition-base);
        position: relative;
    }
    
    .product-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-xl);
    }
    
    .product-image-container {
        position: relative;
        height: 200px;
        overflow: hidden;
        background: var(--gradient-light);
    }
    
    .product-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: var(--transition-slow);
    }
    
    .product-card:hover .product-image {
        transform: scale(1.05);
    }
    
    .product-actions {
        position: absolute;
        top: 0.75rem;
        right: 0.75rem;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        opacity: 0;
        transform: translateX(15px);
        transition: var(--transition-base);
    }
    
    .product-card:hover .product-actions {
        opacity: 1;
        transform: translateX(0);
    }
    
    .action-btn {
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.9);
        border: none;
        color: var(--gray-600);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: var(--transition-base);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        font-size: 0.875rem;
    }
    
    .action-btn:hover {
        background: var(--primary-gradient);
        color: white;
        transform: scale(1.1);
    }
    
    .product-content {
        padding: 1.25rem;
    }
    
    .product-category {
        color: var(--primary-500);
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 0.5rem;
    }
    
    .product-title {
        font-size: 1.125rem;
        font-weight: 600;
        margin-bottom: 0.75rem;
        color: var(--gray-900);
        line-height: 1.3;
    }
    
    .product-description {
        color: var(--gray-600);
        line-height: 1.5;
        margin-bottom: 1rem;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        font-size: 0.9rem;
    }
    
    .product-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .product-price {
        display: flex;
        flex-direction: column;
    }
    
    .price-main {
        font-size: 1.25rem;
        font-weight: 700;
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .price-unit {
        font-size: 0.75rem;
        color: var(--gray-500);
    }
    
    .product-btn {
        background: var(--primary-gradient);
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: var(--border-radius-lg);
        font-weight: 500;
        text-decoration: none;
        transition: var(--transition-base);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
    }
    
    .product-btn:hover {
        color: white;
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }
    
    /* ========== List View ========== */
    .products-list {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }
    
    .products-list .product-card {
        display: flex;
        flex-direction: row;
        align-items: center;
    }
    
    .products-list .product-image-container {
        width: 160px;
        height: 120px;
        flex-shrink: 0;
    }
    
    .products-list .product-content {
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .products-list .product-info {
        flex: 1;
    }
    
    .products-list .product-footer {
        flex-direction: column;
        align-items: flex-end;
        gap: 0.75rem;
        margin: 0;
    }
    
    /* ========== Empty State ========== */
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: rgba(255, 255, 255, 0.9);
        border-radius: var(--border-radius-xl);
        margin: 2rem 0;
    }
    
    .empty-icon {
        font-size: 4rem;
        color: var(--gray-300);
        margin-bottom: 1.5rem;
    }
    
    .empty-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--gray-700);
        margin-bottom: 1rem;
    }
    
    .empty-text {
        color: var(--gray-500);
        margin-bottom: 2rem;
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
    }
    
    /* ========== Pagination ========== */
    .pagination-modern {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
        margin-top: 3rem;
    }
    
    .page-btn {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid rgba(102, 126, 234, 0.2);
        color: var(--primary-600);
        padding: 0.5rem 0.75rem;
        border-radius: var(--border-radius-md);
        text-decoration: none;
        font-weight: 500;
        transition: var(--transition-base);
        min-width: 2.5rem;
        text-align: center;
        font-size: 0.9rem;
    }
    
    .page-btn:hover,
    .page-btn.active {
        background: var(--primary-gradient);
        color: white;
        border-color: var(--primary-500);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }
    
    .page-btn.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        pointer-events: none;
    }
    
    /* ========== Responsive Design ========== */
    @media (max-width: 768px) {
        .products-container {
            flex-direction: column;
            gap: 1rem;
        }
        
        .sidebar {
            width: 100%;
        }
        
        .sidebar-section {
            padding: 1rem;
        }
        
        .products-grid {
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
            gap: 1rem;
        }
        
        .content-header {
            flex-direction: column;
            gap: 1rem;
            align-items: flex-start;
        }
        
        .products-list .product-card {
            flex-direction: column;
        }
        
        .products-list .product-image-container {
            width: 100%;
            height: 160px;
        }
        
        .products-list .product-content {
            flex-direction: column;
            align-items: stretch;
        }
        
        .products-list .product-footer {
            align-items: center;
            flex-direction: row;
        }
    }
    
    /* ========== Animation Enhancement ========== */
    .animate-on-scroll {
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.6s ease-out;
    }
    
    .animate-on-scroll.animated {
        opacity: 1;
        transform: translateY(0);
    }
    
    .animate-on-scroll.delay-1 { transition-delay: 0.1s; }
    .animate-on-scroll.delay-2 { transition-delay: 0.2s; }
    .animate-on-scroll.delay-3 { transition-delay: 0.3s; }
    .animate-on-scroll.delay-4 { transition-delay: 0.4s; }
</style>
{% endblock %}

{% block content %}
<section class="products-page">
    <div class="products-container">
        <!-- Left Sidebar -->
        <aside class="sidebar">
            <!-- Search -->
            <div class="sidebar-section">
                <div class="sidebar-title">
                    <i class="fas fa-search"></i>搜索商品
                </div>
                <form method="GET" class="search-box">
                    {% if current_category %}
                    <input type="hidden" name="category" value="{{ current_category }}">
                    {% endif %}
                    <input type="hidden" name="sort" value="{{ sort }}">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" name="search" class="search-input" 
                           placeholder="输入商品名称..." value="{{ search }}">
                </form>
            </div>
            
            <!-- Categories -->
            <div class="sidebar-section">
                <div class="sidebar-title">
                    <i class="fas fa-tags"></i>商品分类
                </div>
                <ul class="category-menu">
                    <li class="category-item">
                        <a href="{{ url_for('main.products') }}" 
                           class="category-link {{ 'active' if not current_category else '' }}">
                            <i class="fas fa-th-large category-icon"></i>
                            全部商品
                        </a>
                    </li>
                    {% for category in categories %}
                    <li class="category-item">
                        <a href="{{ url_for('main.products', category=category.id) }}" 
                           class="category-link {{ 'active' if current_category == category.id else '' }}">
                            <i class="fas fa-tag category-icon"></i>
                            {{ category.name }}
                        </a>
                    </li>
                    {% endfor %}
                </ul>
            </div>
            
            <!-- Sort -->
            <div class="sidebar-section">
                <div class="sidebar-title">
                    <i class="fas fa-sort"></i>排序方式
                </div>
                <form method="GET">
                    {% if current_category %}
                    <input type="hidden" name="category" value="{{ current_category }}">
                    {% endif %}
                    {% if search %}
                    <input type="hidden" name="search" value="{{ search }}">
                    {% endif %}
                    <select name="sort" onchange="this.form.submit()" class="sort-select">
                        <option value="default" {{ 'selected' if sort == 'default' else '' }}>默认排序</option>
                        <option value="price_asc" {{ 'selected' if sort == 'price_asc' else '' }}>价格 ↑</option>
                        <option value="price_desc" {{ 'selected' if sort == 'price_desc' else '' }}>价格 ↓</option>
                        <option value="name" {{ 'selected' if sort == 'name' else '' }}>名称排序</option>
                        <option value="newest" {{ 'selected' if sort == 'newest' else '' }}>最新商品</option>
                    </select>
                </form>
            </div>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <div class="content-header">
                <h1 class="page-title">商品列表</h1>
                <div class="view-toggle">
                    <button class="view-btn active" data-view="grid" title="网格视图">
                        <i class="fas fa-th"></i>
                    </button>
                    <button class="view-btn" data-view="list" title="列表视图">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>
            
            <!-- Products -->
            {% if products.items %}
            <div class="products-grid" id="products-container">
                {% for product in products.items %}
                <div class="product-card animate-on-scroll delay-{{ loop.index0 % 4 + 1 }}">
                    <div class="product-image-container">
                        <img src="{{ product.get_main_image() }}" 
                             alt="{{ product.name }}" 
                             class="product-image"
                             onerror="this.src='/static/images/placeholder.jpg'">
                        
                        <div class="product-actions">
                            <button class="action-btn" title="快速预览" onclick="quickView({{ product.id }})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="action-btn" title="添加到收藏" onclick="toggleWishlist({{ product.id }})">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="product-content">
                        <div class="product-info">
                            <div class="product-category">{{ product.category.name if product.category else '其他' }}</div>
                            <h3 class="product-title">{{ product.name }}</h3>
                            <p class="product-description">{{ product.description or '暂无描述' }}</p>
                        </div>
                        
                        <div class="product-footer">
                            <div class="product-price">
                                <span class="price-main">¥{{ "%.2f"|format(product.base_price) }}</span>
                                <span class="price-unit">起/{{ product.unit }}</span>
                            </div>
                            <a href="{{ url_for('main.product_detail', id=product.id) }}" 
                               class="product-btn">
                                <i class="fas fa-eye"></i>
                                查看详情
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            
            <!-- Pagination -->
            {% if products.pages > 1 %}
            <div class="pagination-modern">
                {% if products.has_prev %}
                <a href="{{ url_for('main.products', page=products.prev_num, 
                   category=current_category, search=search, sort=sort) }}" 
                   class="page-btn">
                    <i class="fas fa-chevron-left"></i>
                </a>
                {% endif %}
                
                {% for page_num in products.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != products.page %}
                        <a href="{{ url_for('main.products', page=page_num, 
                           category=current_category, search=search, sort=sort) }}" 
                           class="page-btn">{{ page_num }}</a>
                        {% else %}
                        <span class="page-btn active">{{ page_num }}</span>
                        {% endif %}
                    {% else %}
                    <span class="page-btn disabled">…</span>
                    {% endif %}
                {% endfor %}
                
                {% if products.has_next %}
                <a href="{{ url_for('main.products', page=products.next_num, 
                   category=current_category, search=search, sort=sort) }}" 
                   class="page-btn">
                    <i class="fas fa-chevron-right"></i>
                </a>
                {% endif %}
            </div>
            {% endif %}
            
            {% else %}
            <!-- Empty State -->
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h2 class="empty-title">没有找到相关商品</h2>
                <p class="empty-text">
                    {% if search %}
                    没有找到包含"{{ search }}"的商品，请尝试其他关键词
                    {% else %}
                    该分类下暂无商品，请浏览其他分类
                    {% endif %}
                </p>
                <a href="{{ url_for('main.products') }}" class="btn btn-primary">
                    <i class="fas fa-refresh me-2"></i>重新浏览
                </a>
            </div>
            {% endif %}
        </main>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    // View toggle functionality
    document.addEventListener('DOMContentLoaded', function() {
        const viewButtons = document.querySelectorAll('.view-btn');
        const productsContainer = document.getElementById('products-container');
        
        viewButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                // Update active state
                viewButtons.forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                // Toggle view
                const view = this.dataset.view;
                if (view === 'list') {
                    productsContainer.className = 'products-list';
                } else {
                    productsContainer.className = 'products-grid';
                }
                
                // Save preference
                localStorage.setItem('products-view', view);
            });
        });
        
        // Restore saved view preference
        const savedView = localStorage.getItem('products-view');
        if (savedView === 'list') {
            document.querySelector('[data-view="list"]').click();
        }
    });
    
    // Quick view modal
    function quickView(productId) {
        if (typeof ModernUI === 'undefined') {
            window.location.href = `/product/${productId}`;
            return;
        }
        
        const quickViewModal = ModernUI.modal.create({
            title: '商品快速预览',
            content: `
                <div class="text-center p-4">
                    <div class="loading-spinner-large mb-3"></div>
                    <p>正在加载商品信息...</p>
                </div>
            `,
            closable: true,
            showFooter: false,
            size: 'lg'
        });
        
        ModernUI.modal.open(quickViewModal);
        
        // Redirect to detail page after short delay
        setTimeout(() => {
            ModernUI.modal.close(quickViewModal);
            window.location.href = `/product/${productId}`;
        }, 800);
    }
    
    // Wishlist toggle
    function toggleWishlist(productId) {
        const heartIcon = event.target.closest('.action-btn').querySelector('i');
        
        // Toggle icon
        if (heartIcon.classList.contains('far')) {
            heartIcon.classList.remove('far');
            heartIcon.classList.add('fas');
            heartIcon.style.color = '#ef4444';
            
            // Show notification if ModernUI is available
            if (typeof ModernUI !== 'undefined') {
                ModernUI.notification.success('已添加到收藏夹');
            }
        } else {
            heartIcon.classList.remove('fas');
            heartIcon.classList.add('far');
            heartIcon.style.color = '';
            
            // Show notification if ModernUI is available
            if (typeof ModernUI !== 'undefined') {
                ModernUI.notification.info('已从收藏夹移除');
            }
        }
        
        console.log('Toggle wishlist for product:', productId);
    }
    
    // Product card entrance animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -30px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animated');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // Observe all animate-on-scroll elements
    document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
    });
</script>
{% endblock %} 