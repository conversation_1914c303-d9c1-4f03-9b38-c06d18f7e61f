{% extends "base_modern.html" %}

{% block title %}用户登录 - {{ site_config.site_name }}{% endblock %}
{% block description %}登录您的账户，享受专业的印刷服务{% endblock %}

{% block extra_css %}
<style>
    /* ========== Login Page Styles ========== */
    .auth-page {
        background: var(--gradient-light);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem 0;
        position: relative;
        overflow: hidden;
    }
    
    /* Background Shapes */
    .auth-background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 1;
    }
    
    .auth-shape {
        position: absolute;
        border-radius: 50%;
        background: var(--primary-gradient);
        opacity: 0.05;
        animation: float 6s ease-in-out infinite;
    }
    
    .auth-shape-1 {
        width: 200px;
        height: 200px;
        top: 10%;
        left: 10%;
        animation-delay: 0s;
    }
    
    .auth-shape-2 {
        width: 150px;
        height: 150px;
        top: 60%;
        right: 15%;
        animation-delay: 2s;
    }
    
    .auth-shape-3 {
        width: 100px;
        height: 100px;
        bottom: 20%;
        left: 20%;
        animation-delay: 4s;
    }
    
    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }
    
    /* Auth Container */
    .auth-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-radius: var(--border-radius-2xl);
        padding: 3rem;
        box-shadow: var(--shadow-xl);
        border: 1px solid rgba(255, 255, 255, 0.3);
        width: 100%;
        max-width: 450px;
        position: relative;
        z-index: 10;
    }
    
    .auth-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: var(--primary-gradient);
        border-radius: var(--border-radius-2xl) var(--border-radius-2xl) 0 0;
    }
    
    /* Header */
    .auth-header {
        text-align: center;
        margin-bottom: 2.5rem;
    }
    
    .auth-logo {
        background: var(--primary-gradient);
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        color: white;
        font-size: 2rem;
        box-shadow: var(--shadow-lg);
    }
    
    .auth-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--gray-900);
        margin-bottom: 0.5rem;
    }
    
    .auth-subtitle {
        color: var(--gray-600);
        font-size: 1rem;
    }
    
    /* Form Styles */
    .auth-form {
        margin-bottom: 2rem;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        display: block;
        font-weight: 600;
        color: var(--gray-800);
        margin-bottom: 0.5rem;
        font-size: 1rem;
    }
    
    .form-control {
        width: 100%;
        padding: 1rem 1.25rem;
        border: 2px solid rgba(0, 0, 0, 0.15);
        border-radius: var(--border-radius-lg);
        background: rgba(255, 255, 255, 1);
        font-size: 1rem;
        transition: var(--transition-base);
        position: relative;
        color: #333333 !important;
        font-weight: 500;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }
    
    .form-control:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
        background: white;
    }
    
    .form-control::placeholder {
        color: #666666 !important;
        opacity: 1;
    }
    
    /* Input with Icon */
    .input-group {
        position: relative;
    }
    
    .input-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--gray-500);
        font-size: 1rem;
        z-index: 5;
    }
    
    .input-group .form-control {
        padding-left: 3rem;
    }
    
    /* Checkbox */
    .auth-form .form-check {
        display: flex !important;
        align-items: center !important;
        gap: 0.75rem !important;
        margin-bottom: 1.5rem !important;
        background: rgba(255, 255, 255, 0.8) !important;
        padding: 0.75rem 1rem !important;
        border-radius: var(--border-radius-md) !important;
        border: 1px solid rgba(0, 0, 0, 0.1) !important;
        width: 100% !important;
        box-sizing: border-box !important;
        transform: none !important;
        box-shadow: none !important;
    }
    
    .auth-form .form-check-input {
        width: 1.25rem !important;
        height: 1.25rem !important;
        border: 2px solid var(--primary-500) !important;
        border-radius: var(--border-radius-sm) !important;
        background: white !important;
        cursor: pointer !important;
        transition: var(--transition-base) !important;
        margin: 0 !important;
        margin-top: 0 !important;
        flex-shrink: 0 !important;
    }
    
    .auth-form .form-check-input:checked {
        background: var(--primary-gradient) !important;
        border-color: var(--primary-600) !important;
    }

    .auth-form .form-check-label {
        color: var(--gray-800) !important;
        font-size: 0.95rem !important;
        font-weight: 500 !important;
        cursor: pointer !important;
        user-select: none !important;
        flex: 1 !important;
        margin-bottom: 0 !important;
    }

    .auth-form .form-check:hover {
        background: rgba(255, 255, 255, 0.9) !important;
        transform: none !important;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
    }
    
    /* Submit Button */
    .btn-submit {
        width: 100%;
        background: var(--primary-gradient);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: var(--border-radius-lg);
        font-size: 1.125rem;
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition-base);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
        position: relative;
        overflow: hidden;
    }
    
    .btn-submit::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        transition: var(--transition-base);
    }
    
    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
    }
    
    .btn-submit:hover::before {
        left: 100%;
    }
    
    .btn-submit:active {
        transform: translateY(0);
    }
    
    /* Loading State */
    .btn-submit.loading {
        pointer-events: none;
        opacity: 0.8;
    }
    
    .btn-submit .loading-spinner {
        display: none;
    }
    
    .btn-submit.loading .loading-spinner {
        display: inline-block;
        margin-right: 0.5rem;
    }
    
    /* Links */
    .auth-links {
        text-align: center;
    }
    
    .auth-link {
        color: var(--primary-600);
        text-decoration: none;
        font-weight: 500;
        transition: var(--transition-base);
    }
    
    .auth-link:hover {
        color: var(--primary-700);
        text-decoration: underline;
    }
    
    .auth-divider {
        margin: 1.5rem 0;
        text-align: center;
        position: relative;
        color: var(--gray-500);
        font-size: 0.9rem;
    }
    
    .auth-divider::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background: var(--gray-200);
        z-index: 1;
    }
    
    .auth-divider span {
        background: rgba(255, 255, 255, 0.95);
        padding: 0 1rem;
        position: relative;
        z-index: 2;
    }
    
    /* Error Messages */
    .alert {
        padding: 1rem;
        border-radius: var(--border-radius-lg);
        margin-bottom: 1.5rem;
        border: 1px solid transparent;
    }
    
    .alert-danger {
        background: rgba(239, 68, 68, 0.1);
        border-color: rgba(239, 68, 68, 0.2);
        color: var(--danger-700);
    }
    
    .alert-success {
        background: rgba(16, 185, 129, 0.1);
        border-color: rgba(16, 185, 129, 0.2);
        color: var(--success-700);
    }
    
    /* Responsive Design */
    @media (max-width: 768px) {
        .auth-container {
            padding: 2rem 1.5rem;
            margin: 1rem;
        }
        
        .auth-title {
            font-size: 1.75rem;
        }
        
        .auth-shape {
            display: none;
        }
    }
    
    /* Animation */
    .slide-in {
        animation: slideInUp 0.6s ease-out;
    }
    
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="auth-page">
    <div class="auth-background">
        <div class="auth-shape auth-shape-1"></div>
        <div class="auth-shape auth-shape-2"></div>
        <div class="auth-shape auth-shape-3"></div>
    </div>
    
    <div class="auth-container slide-in">
        <div class="auth-header">
            <div class="auth-logo">
                <i class="fas fa-print"></i>
            </div>
            <h1 class="auth-title">欢迎回来</h1>
            <p class="auth-subtitle">登录您的账户，继续您的印刷服务</p>
        </div>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                <div class="alert alert-{{ 'danger' if category == 'error' else category }}">
                    <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }} me-2"></i>
                    {{ message }}
                </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form method="POST" class="auth-form" id="loginForm">
            {{ form.hidden_tag() }}
            
            <div class="form-group">
                <label for="username" class="form-label">用户名或邮箱</label>
                <div class="input-group">
                    <i class="fas fa-user input-icon"></i>
                    {{ form.username(class="form-control", placeholder="请输入用户名或邮箱", id="username") }}
                </div>
                {% if form.username.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.username.errors %}
                            <small>{{ error }}</small>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="password" class="form-label">密码</label>
                <div class="input-group">
                    <i class="fas fa-lock input-icon"></i>
                    {{ form.password(class="form-control", placeholder="请输入密码", id="password") }}
                </div>
                {% if form.password.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.password.errors %}
                            <small>{{ error }}</small>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <div class="form-check">
                {{ form.remember_me(class="form-check-input", id="remember_me") }}
                <label for="remember_me" class="form-check-label">记住我</label>
            </div>
            
            <button type="submit" class="btn-submit" id="submitBtn">
                <i class="fas fa-spinner fa-spin loading-spinner"></i>
                立即登录
            </button>
        </form>
        
        <div class="auth-links">
            <div class="auth-divider">
                <span>还没有账户？</span>
            </div>
            <p>
                <a href="{{ url_for('auth.register') }}" class="auth-link">
                    <i class="fas fa-user-plus me-1"></i>立即注册
                </a>
            </p>
            <p>
                <a href="{{ url_for('main.index') }}" class="auth-link">
                    <i class="fas fa-home me-1"></i>返回首页
                </a>
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('loginForm');
    const submitBtn = document.getElementById('submitBtn');
    
    form.addEventListener('submit', function(e) {
        // Add loading state
        submitBtn.classList.add('loading');
        submitBtn.disabled = true;
        
        // Let the form submit normally
        setTimeout(() => {
            if (submitBtn.classList.contains('loading')) {
                submitBtn.classList.remove('loading');
                submitBtn.disabled = false;
            }
        }, 3000); // Reset after 3 seconds if no redirect
    });
    
    // Auto-focus first input
    document.getElementById('username').focus();
    
    // Enter key handling
    document.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !submitBtn.disabled) {
            form.submit();
        }
    });
});
</script>
{% endblock %}
