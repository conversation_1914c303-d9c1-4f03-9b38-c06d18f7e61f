-- 添加自定义输入属性支持
-- 执行时间: 2024-12-15

-- 为attributes表添加新字段
ALTER TABLE attributes 
ADD COLUMN input_type ENUM('select', 'radio', 'input', 'textarea', 'checkbox') DEFAULT 'select' AFTER price_modifier_type,
ADD COLUMN is_custom_input BOOLEAN DEFAULT FALSE AFTER max_quantity,
ADD COLUMN input_placeholder VARCHAR(200) AFTER is_custom_input,
ADD COLUMN default_value VARCHAR(200) AFTER input_placeholder,
ADD COLUMN validation_pattern VARCHAR(500) AFTER default_value,
ADD COLUMN validation_message VARCHAR(200) AFTER validation_pattern;

-- 更新现有数据，将所有现有属性设置为select类型
UPDATE attributes SET input_type = 'select' WHERE input_type IS NULL;

-- 添加索引
ALTER TABLE attributes ADD INDEX idx_input_type (input_type);
ALTER TABLE attributes ADD INDEX idx_is_custom_input (is_custom_input);

-- 插入示例自定义输入属性数据
-- 注意：这些是示例数据，实际使用时请根据需要调整

-- 首先确保有一个测试分类
INSERT IGNORE INTO categories (name, description, sort_order, is_active) 
VALUES ('印刷品', '各类印刷产品', 1, TRUE);

-- 获取分类ID（假设是1，实际使用时请查询获取）
SET @category_id = (SELECT id FROM categories WHERE name = '印刷品' LIMIT 1);

-- 创建属性组：尺寸（固定选项）
INSERT IGNORE INTO attribute_groups (category_id, name, description, display_type, sort_order, is_active)
VALUES (@category_id, '尺寸', '产品尺寸规格', 'select', 1, TRUE);

SET @size_group_id = LAST_INSERT_ID();

-- 创建属性组：纸张（固定选项）
INSERT IGNORE INTO attribute_groups (category_id, name, description, display_type, sort_order, is_active)
VALUES (@category_id, '纸张', '纸张类型和颜色', 'radio', 2, TRUE);

SET @paper_group_id = LAST_INSERT_ID();

-- 创建属性组：每本张数（自定义输入）
INSERT IGNORE INTO attribute_groups (category_id, name, description, display_type, sort_order, is_active)
VALUES (@category_id, '每本张数', '自定义每本张数', 'radio', 3, TRUE);

SET @pages_group_id = LAST_INSERT_ID();

-- 添加尺寸属性（固定选项）
INSERT INTO attributes (group_id, name, value, price_modifier, price_modifier_type, input_type, is_custom_input, sort_order, is_active)
VALUES 
(@size_group_id, '大16开', '210*285', 0.00, 'fixed', 'select', FALSE, 1, TRUE),
(@size_group_id, '大32开', '148*210', -2.00, 'fixed', 'select', FALSE, 2, TRUE),
(@size_group_id, 'A4', '210*297', 1.50, 'fixed', 'select', FALSE, 3, TRUE),
(@size_group_id, 'A5', '148*210', -1.00, 'fixed', 'select', FALSE, 4, TRUE);

-- 添加纸张属性（固定选项）
INSERT INTO attributes (group_id, name, value, price_modifier, price_modifier_type, input_type, is_custom_input, sort_order, is_active)
VALUES 
(@paper_group_id, '白红', 'white_red', 0.00, 'fixed', 'radio', FALSE, 1, TRUE),
(@paper_group_id, '牛皮纸', 'kraft_paper', 2.50, 'fixed', 'radio', FALSE, 2, TRUE),
(@paper_group_id, '瓦楞纸', 'corrugated_paper', 3.00, 'fixed', 'radio', FALSE, 3, TRUE);

-- 添加每本张数属性（自定义输入）
INSERT INTO attributes (group_id, name, value, price_modifier, price_modifier_type, input_type, is_custom_input, 
                       input_placeholder, default_value, validation_pattern, validation_message, 
                       price_formula, is_quantity_based, quantity_unit, min_quantity, max_quantity, sort_order, is_active)
VALUES 
(@pages_group_id, '自定义张数', 'custom_pages', 0.00, 'fixed', 'input', TRUE, 
 '请输入每本张数', '100', '^[1-9][0-9]*$', '请输入有效的张数（正整数）',
 'quantity * 0.24', TRUE, '张', 1, 9999, 1, TRUE);

-- 验证数据插入
SELECT 
    ag.name as group_name,
    a.name as attr_name,
    a.input_type,
    a.is_custom_input,
    a.input_placeholder,
    a.price_formula
FROM attribute_groups ag
JOIN attributes a ON ag.id = a.group_id
WHERE ag.category_id = @category_id
ORDER BY ag.sort_order, a.sort_order;
