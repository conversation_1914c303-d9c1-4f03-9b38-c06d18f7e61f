/**
 * 商品属性选择组件
 * 支持标准属性和自定义属性，实时价格计算
 */

class ProductAttributeSelector {
    constructor(options) {
        this.options = {
            productId: null,
            container: '#attribute-container',
            priceContainer: '#price-display',
            onPriceChange: null,
            onAttributeChange: null,
            ...options
        };
        
        this.productId = this.options.productId;
        this.container = document.querySelector(this.options.container);
        this.priceContainer = document.querySelector(this.options.priceContainer);
        
        this.attributeGroups = [];
        this.selectedAttributes = [];
        this.customAttributes = {};
        this.attributeQuantities = {};
        this.currentPrice = 0;
        
        this.init();
    }
    
    async init() {
        if (!this.productId || !this.container) {
            console.error('ProductAttributeSelector: 缺少必要参数');
            return;
        }
        
        try {
            await this.loadAttributes();
            this.renderAttributes();
            this.calculatePrice();
        } catch (error) {
            console.error('属性选择器初始化失败:', error);
            this.showError('属性加载失败，请刷新页面重试');
        }
    }
    
    async loadAttributes() {
        const response = await fetch(`/api/attributes/product/${this.productId}`);
        const result = await response.json();
        
        if (!result.success) {
            throw new Error(result.message);
        }
        
        this.attributeGroups = result.data;
    }
    
    renderAttributes() {
        this.container.innerHTML = '';
        
        this.attributeGroups.forEach(group => {
            const groupElement = this.createAttributeGroup(group);
            this.container.appendChild(groupElement);
        });
    }
    
    createAttributeGroup(group) {
        const groupDiv = document.createElement('div');
        groupDiv.className = 'attribute-group mb-4';
        groupDiv.innerHTML = `
            <div class="attribute-group-header">
                <label class="form-label fw-bold">
                    ${group.name}
                    <span class="text-danger">*</span>
                </label>
                ${group.description ? `<small class="text-muted d-block">${group.description}</small>` : ''}
            </div>
            <div class="attribute-group-content" data-group-id="${group.id}">
                ${this.renderAttributeOptions(group)}
            </div>
        `;
        
        return groupDiv;
    }
    
    renderAttributeOptions(group) {
        let html = '';
        
        // 标准属性选项
        const standardAttrs = group.attributes.filter(attr => 
            !attr.is_custom_input && attr.attribute_category === 'standard'
        );
        
        if (standardAttrs.length > 0) {
            if (group.display_type === 'radio') {
                html += this.renderRadioOptions(group, standardAttrs);
            } else {
                html += this.renderSelectOptions(group, standardAttrs);
            }
        }
        
        // 自定义输入选项
        const customAttrs = group.attributes.filter(attr => 
            attr.is_custom_input || attr.attribute_category === 'custom'
        );
        
        if (customAttrs.length > 0) {
            html += this.renderCustomInputs(group, customAttrs);
        }
        
        return html;
    }
    
    renderRadioOptions(group, attributes) {
        let html = '<div class="attribute-options">';
        
        attributes.forEach(attr => {
            const priceText = this.getPriceText(attr);
            html += `
                <div class="form-check form-check-inline me-3">
                    <input class="form-check-input" type="radio" 
                           name="attr_group_${group.id}" 
                           id="attr_${attr.id}" 
                           value="${attr.id}"
                           data-group-id="${group.id}"
                           data-attr-id="${attr.id}">
                    <label class="form-check-label" for="attr_${attr.id}">
                        ${attr.value}
                        ${priceText ? `<span class="text-success">${priceText}</span>` : ''}
                    </label>
                </div>
            `;
        });
        
        html += '</div>';
        return html;
    }
    
    renderSelectOptions(group, attributes) {
        let html = `
            <select class="form-select" 
                    name="attr_group_${group.id}" 
                    data-group-id="${group.id}">
                <option value="">请选择${group.name}</option>
        `;
        
        attributes.forEach(attr => {
            const priceText = this.getPriceText(attr);
            html += `
                <option value="${attr.id}" data-attr-id="${attr.id}">
                    ${attr.value}
                    ${priceText ? ` (${priceText})` : ''}
                </option>
            `;
        });
        
        html += '</select>';
        return html;
    }
    
    renderCustomInputs(group, attributes) {
        let html = '<div class="custom-inputs mt-3">';
        
        attributes.forEach(attr => {
            html += '<div class="custom-input-item mb-3">';
            
            if (attr.input_type === 'text_input') {
                html += this.renderTextInput(attr);
            } else if (attr.input_type === 'number_input') {
                html += this.renderNumberInput(attr);
            } else if (attr.input_type === 'textarea') {
                html += this.renderTextarea(attr);
            } else if (attr.input_type === 'file_upload') {
                html += this.renderFileUpload(attr);
            } else if (attr.input_type === 'quantity_select') {
                html += this.renderQuantitySelect(attr);
            }
            
            html += '</div>';
        });
        
        html += '</div>';
        return html;
    }
    
    renderTextInput(attr) {
        return `
            <label class="form-label">${attr.name}</label>
            <input type="text" 
                   class="form-control custom-attribute" 
                   data-attr-id="${attr.id}"
                   placeholder="${attr.input_placeholder || '请输入' + attr.name}"
                   value="${attr.default_value || ''}"
                   ${attr.validation_rules?.required ? 'required' : ''}>
            <small class="form-text text-muted">
                ${this.getPriceText(attr) || ''}
            </small>
        `;
    }
    
    renderNumberInput(attr) {
        const min = attr.number_min !== undefined ? attr.number_min : (attr.validation_rules?.min || '');
        const max = attr.number_max !== undefined ? attr.number_max : (attr.validation_rules?.max || '');
        const step = attr.number_step || 1;
        
        return `
            <label class="form-label">${attr.name}</label>
            <input type="number" 
                   class="form-control custom-attribute" 
                   data-attr-id="${attr.id}"
                   placeholder="${attr.input_placeholder || '请输入' + attr.name}"
                   value="${attr.default_value || ''}"
                   min="${min}" 
                   max="${max}" 
                   step="${step}"
                   ${attr.validation_rules?.required ? 'required' : ''}>
            <small class="form-text text-muted">
                ${this.getPriceText(attr) || ''}
            </small>
        `;
    }
    
    renderTextarea(attr) {
        return `
            <label class="form-label">${attr.name}</label>
            <textarea class="form-control custom-attribute" 
                      data-attr-id="${attr.id}"
                      placeholder="${attr.input_placeholder || '请输入' + attr.name}"
                      rows="3"
                      ${attr.validation_rules?.required ? 'required' : ''}>${attr.default_value || ''}</textarea>
            <small class="form-text text-muted">
                ${this.getPriceText(attr) || ''}
            </small>
        `;
    }
    
    renderFileUpload(attr) {
        const accept = attr.file_accept_types || '';
        return `
            <label class="form-label">${attr.name}</label>
            <input type="file" 
                   class="form-control custom-attribute" 
                   data-attr-id="${attr.id}"
                   accept="${accept}"
                   ${attr.validation_rules?.required ? 'required' : ''}>
            <small class="form-text text-muted">
                ${accept ? `支持格式: ${accept}` : ''}
                ${this.getPriceText(attr) || ''}
            </small>
        `;
    }
    
    renderQuantitySelect(attr) {
        return `
            <label class="form-label">${attr.name}</label>
            <div class="input-group">
                <input type="number" 
                       class="form-control custom-attribute" 
                       data-attr-id="${attr.id}"
                       value="${attr.min_quantity || 1}"
                       min="${attr.min_quantity || 1}" 
                       max="${attr.max_quantity || 9999}">
                <span class="input-group-text">${attr.quantity_unit || '个'}</span>
            </div>
            <small class="form-text text-muted">
                ${this.getPriceText(attr) || ''}
            </small>
        `;
    }
    
    getPriceText(attr) {
        if (attr.price_modifier === 0) return '';
        
        if (attr.is_quantity_based && attr.price_formula) {
            return `公式计价: ${attr.price_formula}`;
        } else if (attr.price_modifier_type === 'percentage') {
            return `+${attr.price_modifier}%`;
        } else {
            return `+¥${attr.price_modifier}`;
        }
    }
    
    bindEvents() {
        // 标准属性选择事件
        this.container.addEventListener('change', (e) => {
            if (e.target.type === 'radio' || e.target.tagName === 'SELECT') {
                this.handleStandardAttributeChange(e);
            } else if (e.target.classList.contains('custom-attribute')) {
                this.handleCustomAttributeChange(e);
            }
        });
        
        // 自定义属性输入事件
        this.container.addEventListener('input', (e) => {
            if (e.target.classList.contains('custom-attribute')) {
                this.handleCustomAttributeChange(e);
            }
        });
    }
    
    handleStandardAttributeChange(e) {
        const groupId = e.target.dataset.groupId;
        const attrId = e.target.value;
        
        // 更新选中的属性
        this.selectedAttributes = this.selectedAttributes.filter(id => {
            const attr = this.findAttributeById(id);
            return attr && attr.group_id !== parseInt(groupId);
        });
        
        if (attrId) {
            this.selectedAttributes.push(parseInt(attrId));
        }
        
        this.calculatePrice();
        this.triggerAttributeChange();
    }
    
    handleCustomAttributeChange(e) {
        const attrId = parseInt(e.target.dataset.attrId);
        const value = e.target.value;
        
        if (value) {
            this.customAttributes[attrId] = value;
        } else {
            delete this.customAttributes[attrId];
        }
        
        this.calculatePrice();
        this.triggerAttributeChange();
    }
    
    async calculatePrice() {
        try {
            const response = await fetch('/api/attributes/calculate-price', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    product_id: this.productId,
                    quantity: this.getQuantity(),
                    selected_attributes: this.selectedAttributes,
                    custom_attributes: this.customAttributes,
                    attribute_quantities: this.attributeQuantities
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.currentPrice = result.data.total_price;
                this.updatePriceDisplay(result.data);
                this.triggerPriceChange(result.data);
            } else {
                console.error('价格计算失败:', result.message);
            }
        } catch (error) {
            console.error('价格计算请求失败:', error);
        }
    }
    
    updatePriceDisplay(priceData) {
        if (!this.priceContainer) return;
        
        this.priceContainer.innerHTML = `
            <div class="price-breakdown">
                <div class="base-price">
                    基础价格: ¥${priceData.base_price.toFixed(2)}
                </div>
                ${priceData.total_attribute_price > 0 ? `
                    <div class="attribute-price">
                        属性加价: ¥${priceData.total_attribute_price.toFixed(2)}
                    </div>
                ` : ''}
                <div class="total-price h5">
                    总价: ¥${priceData.total_price.toFixed(2)}
                </div>
            </div>
        `;
    }
    
    getQuantity() {
        const quantityInput = document.querySelector('#quantity');
        return quantityInput ? parseInt(quantityInput.value) || 1 : 1;
    }
    
    findAttributeById(attrId) {
        for (const group of this.attributeGroups) {
            const attr = group.attributes.find(a => a.id === attrId);
            if (attr) return attr;
        }
        return null;
    }
    
    triggerPriceChange(priceData) {
        if (this.options.onPriceChange) {
            this.options.onPriceChange(priceData);
        }
    }
    
    triggerAttributeChange() {
        if (this.options.onAttributeChange) {
            this.options.onAttributeChange({
                selectedAttributes: this.selectedAttributes,
                customAttributes: this.customAttributes,
                attributeQuantities: this.attributeQuantities
            });
        }
    }
    
    showError(message) {
        this.container.innerHTML = `
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
            </div>
        `;
    }
    
    // 公共方法
    getSelectedData() {
        return {
            selectedAttributes: this.selectedAttributes,
            customAttributes: this.customAttributes,
            attributeQuantities: this.attributeQuantities,
            totalPrice: this.currentPrice
        };
    }
    
    reset() {
        this.selectedAttributes = [];
        this.customAttributes = {};
        this.attributeQuantities = {};
        this.renderAttributes();
        this.calculatePrice();
    }
}

// 导出到全局
window.ProductAttributeSelector = ProductAttributeSelector;
