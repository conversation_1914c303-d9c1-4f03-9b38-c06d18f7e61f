<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}{{ site_config.site_name }}{% endblock %}</title>
    <meta name="description" content="{% block description %}{{ site_config.site_description }}{% endblock %}">
    
    <!-- Bootstrap CSS -->
    <link href="{{ url_for("static", filename="vendor/bootstrap/bootstrap.min.css") }}" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="{{ url_for("static", filename="vendor/fontawesome/css/all.min.css") }}" rel="stylesheet">
    <!-- 自定义CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="{{ url_for('main.index') }}">
                <i class="fas fa-print me-2"></i>{{ site_config.site_name }}
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.index') }}">首页</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            商品分类
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('main.products') }}">全部商品</a></li>
                            <li><hr class="dropdown-divider"></li>
                            {% for category in main_categories %}
                            <li><a class="dropdown-item" href="{{ url_for('main.products', category=category.id) }}">{{ category.name }}</a></li>
                            {% endfor %}
                        </ul>
                    </li>
                </ul>
                
                <!-- 搜索框 -->
                <form class="d-flex me-3" method="GET" action="{{ url_for('main.products') }}">
                    <input class="form-control me-2" type="search" name="search" placeholder="搜索商品..." value="{{ request.args.get('search', '') }}">
                    <button class="btn btn-outline-light" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
                
                <ul class="navbar-nav">
                    {% if current_user.is_authenticated %}
                    <!-- 用户菜单 -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>{{ current_user.real_name or current_user.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">个人资料</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('main.orders') }}">我的订单</a></li>
                            {% if current_user.is_admin %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('admin.index') }}">管理后台</a></li>
                            {% endif %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">退出登录</a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.login') }}">登录</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.register') }}">注册</a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- 消息提示 -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
        <div class="container mt-3">
            {% for category, message in messages %}
            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}
    {% endwith %}
    
    <!-- 主要内容 -->
    <main>
        {% block content %}{% endblock %}
    </main>
    
    <!-- 页脚 -->
    <footer class="bg-dark text-light py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>{{ site_config.site_name }}</h5>
                    <p>{{ site_config.site_description }}</p>
                </div>
                <div class="col-md-4">
                    <h5>联系我们</h5>
                    <p><i class="fas fa-phone me-2"></i>{{ site_config.contact_phone }}</p>
                    <p><i class="fas fa-envelope me-2"></i>{{ site_config.contact_email }}</p>
                </div>
                <div class="col-md-4">
                    <h5>快速链接</h5>
                    <ul class="list-unstyled">
                        <li><a href="{{ url_for('main.index') }}" class="text-light text-decoration-none">首页</a></li>
                        <li><a href="{{ url_for('main.products') }}" class="text-light text-decoration-none">商品中心</a></li>
                        {% if current_user.is_authenticated %}
                        <li><a href="{{ url_for('main.orders') }}" class="text-light text-decoration-none">我的订单</a></li>
                        {% endif %}
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p>&copy; 2024 {{ site_config.site_name }}. All rights reserved.</p>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="{{ url_for("static", filename="vendor/bootstrap/bootstrap.bundle.min.js") }}"></script>
    <!-- jQuery -->
    <script src="{{ url_for("static", filename="vendor/jquery/jquery.min.js") }}"></script>
    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
