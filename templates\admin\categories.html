{% extends "admin/base.html" %}

{% block title %}分类管理 - 管理后台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">分类管理</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('admin.add_category') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>添加分类
        </a>
    </div>
</div>

<div class="card">
    <div class="card-body">
        {% if categories.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>分类名称</th>
                        <th>上级分类</th>
                        <th>排序</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for category in categories.items %}
                    <tr>
                        <td>{{ category.id }}</td>
                        <td>{{ category.name }}</td>
                        <td>{{ category.parent.name if category.parent else '-' }}</td>
                        <td>{{ category.sort_order }}</td>
                        <td>
                            <span class="badge bg-{{ 'success' if category.is_active else 'secondary' }}">
                                {{ '启用' if category.is_active else '禁用' }}
                            </span>
                        </td>
                        <td>{{ category.created_at.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('admin.edit_category', id=category.id) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-edit me-1"></i>编辑
                                </a>
                                <a href="{{ url_for('admin.attribute_groups') }}?category_id={{ category.id }}" class="btn btn-sm btn-outline-info">
                                    <i class="fas fa-tags me-1"></i>大属性
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-danger delete-btn"
                                        data-url="{{ url_for('admin.delete_category', id=category.id) }}"
                                        data-name="{{ category.name }}">
                                    <i class="fas fa-trash me-1"></i>删除
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        {% if categories.pages > 1 %}
        <nav aria-label="分类分页">
            <ul class="pagination justify-content-center">
                {% if categories.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.categories', page=categories.prev_num) }}">上一页</a>
                </li>
                {% endif %}
                
                {% for page_num in categories.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != categories.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.categories', page=page_num) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if categories.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.categories', page=categories.next_num) }}">下一页</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
            <h5>暂无分类</h5>
            <p class="text-muted">还没有创建任何分类</p>
            <a href="{{ url_for('admin.add_category') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>添加第一个分类
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除分类 "<span id="deleteName"></span>" 吗？</p>
                <p class="text-danger small">删除后无法恢复，请谨慎操作。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 初始化分类页面删除功能');
    
    // 初始化删除功能
    initDeleteFunction();
});

function initDeleteFunction() {
    const deleteModal = document.getElementById('deleteModal');
    const deleteNameSpan = document.getElementById('deleteName');
    const confirmDeleteBtn = document.getElementById('confirmDelete');
    let currentDeleteUrl = '';
    let currentDeleteName = '';
    
    if (!deleteModal) {
        console.error('删除模态框未找到');
        return;
    }
    
    const modal = new bootstrap.Modal(deleteModal);
    
    // 移除所有现有的事件监听器
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(btn => {
        // 克隆节点以移除所有事件监听器
        const newBtn = btn.cloneNode(true);
        btn.parentNode.replaceChild(newBtn, btn);
    });
    
    // 重新绑定删除按钮事件
    document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            currentDeleteUrl = this.dataset.url;
            currentDeleteName = this.dataset.name;
            
            console.log('点击删除按钮:', currentDeleteName, currentDeleteUrl);
            
            if (deleteNameSpan) {
                deleteNameSpan.textContent = currentDeleteName;
            }
            
            modal.show();
        });
    });
    
    // 确认删除事件
    if (confirmDeleteBtn) {
        // 移除旧的事件监听器
        const newConfirmBtn = confirmDeleteBtn.cloneNode(true);
        confirmDeleteBtn.parentNode.replaceChild(newConfirmBtn, confirmDeleteBtn);
        
        // 绑定新的事件监听器
        document.getElementById('confirmDelete').addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            if (!currentDeleteUrl) {
                showAlert('danger', '删除URL无效');
                return;
            }
            
            // 禁用按钮，显示加载状态
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>删除中...';
            
            // 获取CSRF token
            const csrfToken = getCSRFToken();
            
            console.log('发送删除请求:', currentDeleteUrl);
            console.log('CSRF Token:', csrfToken);
            
            // 发送删除请求
            fetch(currentDeleteUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken,
                    'Accept': 'application/json'
                },
                credentials: 'same-origin'
            })
            .then(response => {
                console.log('响应状态:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return response.json();
            })
            .then(data => {
                console.log('删除响应:', data);
                
                if (data.success) {
                    showAlert('success', data.message || '删除成功');
                    // 延迟刷新页面
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showAlert('danger', data.message || '删除失败');
                    this.disabled = false;
                    this.innerHTML = '确认删除';
                }
            })
            .catch(error => {
                console.error('删除请求错误:', error);
                showAlert('danger', `删除失败: ${error.message}`);
                this.disabled = false;
                this.innerHTML = '确认删除';
            });
            
            // 隐藏模态框
            modal.hide();
        });
    }
}

function getCSRFToken() {
    // 尝试多种方式获取CSRF token
    const metaToken = document.querySelector('meta[name="csrf-token"]');
    if (metaToken) {
        return metaToken.getAttribute('content');
    }
    
    const inputToken = document.querySelector('input[name="csrf_token"]');
    if (inputToken) {
        return inputToken.value;
    }
    
    // 最后尝试模板变量
    return '{{ csrf_token() }}';
}

function showAlert(type, message) {
    // 移除现有的alert
    const existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(alert => alert.remove());
    
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert" style="margin-bottom: 1rem;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
    
    const container = document.querySelector('.container-fluid');
    if (container) {
        container.insertAdjacentHTML('afterbegin', alertHtml);
        
        // 滚动到顶部显示消息
        window.scrollTo({ top: 0, behavior: 'smooth' });
        
        // 自动关闭
        setTimeout(() => {
            const alert = document.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
}
</script>
{% endblock %}
