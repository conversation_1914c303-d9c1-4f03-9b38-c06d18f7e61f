-- 安全的数据库迁移脚本
-- 为属性组表添加分类ID字段

-- 1. 检查字段是否已存在，如果不存在则添加
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'attribute_groups'
    AND COLUMN_NAME = 'category_id'
);

-- 2. 如果字段不存在，则添加字段
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE attribute_groups ADD COLUMN category_id INT NULL AFTER id',
    'SELECT "category_id column already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 添加索引（如果不存在）
SET @index_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'attribute_groups'
    AND INDEX_NAME = 'idx_category_id'
);

SET @sql = IF(@index_exists = 0,
    'ALTER TABLE attribute_groups ADD INDEX idx_category_id (category_id)',
    'SELECT "idx_category_id index already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 添加外键约束（如果不存在）
SET @fk_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'attribute_groups'
    AND CONSTRAINT_NAME = 'fk_attribute_groups_category'
);

SET @sql = IF(@fk_exists = 0,
    'ALTER TABLE attribute_groups ADD CONSTRAINT fk_attribute_groups_category FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE',
    'SELECT "fk_attribute_groups_category foreign key already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 为现有的属性组分配默认分类（可选）
-- 获取第一个分类的ID作为默认分类
SET @default_category_id = (SELECT id FROM categories WHERE is_active = 1 ORDER BY id LIMIT 1);

-- 如果有默认分类，则为没有分类的属性组分配默认分类
UPDATE attribute_groups
SET category_id = @default_category_id
WHERE category_id IS NULL AND @default_category_id IS NOT NULL;

-- 显示迁移结果
SELECT
    'Migration completed successfully' as status,
    COUNT(*) as total_attribute_groups,
    SUM(CASE WHEN category_id IS NOT NULL THEN 1 ELSE 0 END) as groups_with_category,
    SUM(CASE WHEN category_id IS NULL THEN 1 ELSE 0 END) as groups_without_category
FROM attribute_groups;
