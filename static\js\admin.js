// 管理后台JavaScript功能

$(document).ready(function() {
    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // 自动隐藏消息提示
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
    
    // 确认删除对话框
    $('.delete-btn').on('click', function(e) {
        e.preventDefault();
        const url = $(this).attr('href') || $(this).data('url');
        const itemName = $(this).data('name') || '该项目';
        
        if (confirm(`确定要删除 "${itemName}" 吗？此操作不可恢复。`)) {
            if ($(this).data('method') === 'POST') {
                deleteItem(url);
            } else {
                window.location.href = url;
            }
        }
    });
    
    // 批量操作
    $('#select-all').on('change', function() {
        $('.item-checkbox').prop('checked', this.checked);
        updateBatchActions();
    });
    
    $('.item-checkbox').on('change', function() {
        updateBatchActions();
    });
    
    // 图片预览
    $('.image-input').on('change', function() {
        previewImage(this);
    });
    
    // 表单验证增强
    $('form').on('submit', function() {
        const $form = $(this);
        const $submitBtn = $form.find('button[type="submit"]');
        
        // 防止重复提交
        $submitBtn.prop('disabled', true);
        $submitBtn.html('<span class="spinner-border spinner-border-sm me-2"></span>提交中...');
        
        // 5秒后重新启用按钮
        setTimeout(function() {
            $submitBtn.prop('disabled', false);
            $submitBtn.html($submitBtn.data('original-text') || '提交');
        }, 5000);
    });
    
    // 保存按钮原始文本
    $('button[type="submit"]').each(function() {
        $(this).data('original-text', $(this).html());
    });
});

// 删除项目
function deleteItem(url) {
    $.ajax({
        url: url,
        method: 'POST',
        headers: {
            'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
        },
        success: function(response) {
            showMessage('success', response.message || '删除成功');
            setTimeout(function() {
                location.reload();
            }, 1000);
        },
        error: function(xhr) {
            const error = xhr.responseJSON?.error || '删除失败，请重试';
            showMessage('error', error);
        }
    });
}

// 显示消息
function showMessage(type, message) {
    const alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // 在页面顶部显示消息
    $('main').prepend(alertHtml);
    
    // 自动隐藏
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 3000);
}

// 更新批量操作按钮状态
function updateBatchActions() {
    const checkedCount = $('.item-checkbox:checked').length;
    const $batchActions = $('.batch-actions');
    
    if (checkedCount > 0) {
        $batchActions.removeClass('d-none');
        $batchActions.find('.selected-count').text(checkedCount);
    } else {
        $batchActions.addClass('d-none');
    }
}

// 批量删除
function batchDelete() {
    const selectedIds = [];
    $('.item-checkbox:checked').each(function() {
        selectedIds.push($(this).val());
    });
    
    if (selectedIds.length === 0) {
        alert('请选择要删除的项目');
        return;
    }
    
    if (!confirm(`确定要删除选中的 ${selectedIds.length} 个项目吗？`)) {
        return;
    }
    
    $.ajax({
        url: window.location.pathname + '/batch-delete',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            ids: selectedIds
        }),
        headers: {
            'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
        },
        success: function(response) {
            showMessage('success', response.message || '批量删除成功');
            setTimeout(function() {
                location.reload();
            }, 1000);
        },
        error: function(xhr) {
            const error = xhr.responseJSON?.error || '批量删除失败';
            showMessage('error', error);
        }
    });
}

// 图片预览
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            const $preview = $(input).siblings('.image-preview');
            if ($preview.length === 0) {
                $(input).after('<div class="image-preview mt-2"></div>');
            }
            
            $('.image-preview').html(`<img src="${e.target.result}" alt="预览图片">`);
        };
        
        reader.readAsDataURL(input.files[0]);
    }
}

// 状态切换
function toggleStatus(id, type, currentStatus) {
    const newStatus = !currentStatus;
    const statusText = newStatus ? '启用' : '禁用';
    
    $.ajax({
        url: `/admin/${type}/${id}/toggle-status`,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            status: newStatus
        }),
        headers: {
            'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
        },
        success: function(response) {
            showMessage('success', `${statusText}成功`);
            
            // 更新页面状态显示
            const $statusBadge = $(`.status-badge[data-id="${id}"]`);
            if (newStatus) {
                $statusBadge.removeClass('bg-secondary').addClass('bg-success').text('启用');
            } else {
                $statusBadge.removeClass('bg-success').addClass('bg-secondary').text('禁用');
            }
            
            // 更新切换按钮
            const $toggleBtn = $(`.toggle-status[data-id="${id}"]`);
            $toggleBtn.data('status', newStatus);
            $toggleBtn.text(newStatus ? '禁用' : '启用');
        },
        error: function(xhr) {
            const error = xhr.responseJSON?.error || `${statusText}失败`;
            showMessage('error', error);
        }
    });
}

// 排序功能
function initSortable(selector) {
    if (typeof Sortable !== 'undefined') {
        const el = document.querySelector(selector);
        if (el) {
            Sortable.create(el, {
                animation: 150,
                ghostClass: 'sortable-ghost',
                onEnd: function(evt) {
                    const itemIds = [];
                    $(selector + ' [data-id]').each(function() {
                        itemIds.push($(this).data('id'));
                    });
                    
                    updateSortOrder(itemIds);
                }
            });
        }
    }
}

// 更新排序
function updateSortOrder(itemIds) {
    $.ajax({
        url: window.location.pathname + '/update-sort',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            item_ids: itemIds
        }),
        headers: {
            'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
        },
        success: function(response) {
            showMessage('success', '排序更新成功');
        },
        error: function(xhr) {
            const error = xhr.responseJSON?.error || '排序更新失败';
            showMessage('error', error);
            location.reload(); // 重新加载页面恢复原始排序
        }
    });
}

// 数据导出
function exportData(type, format = 'excel') {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);
    
    window.location.href = `${window.location.pathname}?${params.toString()}`;
}

// 数据统计图表
function initCharts() {
    // 这里可以集成Chart.js或其他图表库
    console.log('图表功能待实现');
}

// 实时搜索
function initLiveSearch(inputSelector, targetSelector) {
    let searchTimeout;
    
    $(inputSelector).on('input', function() {
        const query = $(this).val();
        
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            if (query.length >= 2) {
                performLiveSearch(query, targetSelector);
            } else {
                $(targetSelector).show();
            }
        }, 300);
    });
}

function performLiveSearch(query, targetSelector) {
    $.ajax({
        url: window.location.pathname,
        method: 'GET',
        data: {
            search: query,
            ajax: 1
        },
        success: function(response) {
            $(targetSelector).html(response);
        },
        error: function() {
            showMessage('error', '搜索失败');
        }
    });
}

// 表单自动保存
function initAutoSave(formSelector, interval = 30000) {
    let autoSaveTimeout;
    
    $(formSelector + ' input, ' + formSelector + ' textarea, ' + formSelector + ' select').on('change input', function() {
        clearTimeout(autoSaveTimeout);
        autoSaveTimeout = setTimeout(function() {
            autoSaveForm(formSelector);
        }, interval);
    });
}

function autoSaveForm(formSelector) {
    const formData = new FormData($(formSelector)[0]);
    formData.append('auto_save', '1');
    
    $.ajax({
        url: $(formSelector).attr('action') || window.location.pathname,
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.auto_saved) {
                showMessage('info', '表单已自动保存');
            }
        },
        error: function() {
            // 静默失败，不显示错误消息
        }
    });
}
