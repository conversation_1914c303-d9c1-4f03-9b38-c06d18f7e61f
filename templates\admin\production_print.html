<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生产单 - {{ order.order_no }}</title>
    <style>
        /* 打印专用样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: white;
        }

        .print-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 15mm;
            background: white;
        }

        /* 公司标头 */
        .company-header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .company-info {
            font-size: 11px;
            color: #666;
        }

        /* 生产单标题 */
        .production-title {
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            margin: 30px 0;
            color: #e74c3c;
            background-color: #f8f9fa;
            padding: 10px;
            border: 2px solid #e74c3c;
        }

        /* 订单信息区域 */
        .order-info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }

        .info-block {
            width: 48%;
        }

        .info-block h3 {
            font-size: 14px;
            font-weight: bold;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .info-row {
            display: flex;
            margin-bottom: 5px;
        }

        .info-label {
            font-weight: bold;
            width: 80px;
            color: #555;
        }

        .info-value {
            flex: 1;
        }

        /* 优先级标识 */
        .priority-urgent {
            background-color: #e74c3c;
            color: white;
            padding: 3px 8px;
            border-radius: 3px;
            font-weight: bold;
        }

        .priority-normal {
            background-color: #3498db;
            color: white;
            padding: 3px 8px;
            border-radius: 3px;
        }

        /* 生产明细表格 */
        .production-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            font-size: 11px;
        }

        .production-table th,
        .production-table td {
            border: 2px solid #333;
            padding: 10px;
            text-align: left;
        }

        .production-table th {
            background-color: #34495e;
            color: white;
            font-weight: bold;
        }

        .production-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .production-specs {
            background-color: #fff3cd;
            padding: 8px;
            border-left: 4px solid #ffc107;
            margin: 5px 0;
        }

        .production-notes {
            font-weight: bold;
            color: #d63384;
        }

        /* 生产要求区域 */
        .production-requirements {
            margin-top: 30px;
            border: 2px solid #28a745;
            padding: 20px;
            background-color: #f8fff9;
        }

        .requirements-title {
            font-size: 16px;
            font-weight: bold;
            color: #28a745;
            margin-bottom: 15px;
            text-align: center;
        }

        .requirement-item {
            margin-bottom: 10px;
            padding: 8px;
            background-color: white;
            border-left: 4px solid #28a745;
        }

        /* 质量检查清单 */
        .quality-check {
            margin-top: 30px;
            border: 2px solid #17a2b8;
            padding: 15px;
            background-color: #f0fcff;
        }

        .check-title {
            font-weight: bold;
            color: #17a2b8;
            margin-bottom: 10px;
            text-align: center;
        }

        .check-item {
            display: flex;
            margin-bottom: 8px;
            align-items: center;
        }

        .check-box {
            width: 15px;
            height: 15px;
            border: 2px solid #17a2b8;
            margin-right: 10px;
            display: inline-block;
        }

        /* 页脚 */
        .print-footer {
            margin-top: 50px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }

        .signature-area {
            display: flex;
            justify-content: space-around;
            margin-top: 40px;
            padding: 20px 0;
            border-top: 1px solid #ddd;
        }

        .signature-box {
            text-align: center;
            width: 30%;
        }

        .signature-line {
            border-bottom: 1px solid #333;
            margin: 20px 0 5px 0;
            height: 30px;
        }

        /* 打印样式 */
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            .print-container {
                margin: 0;
                padding: 0;
                max-width: none;
            }
            
            .no-print {
                display: none !important;
            }
            
            @page {
                margin: 15mm;
                size: A4;
            }
        }

        /* 屏幕预览样式 */
        @media screen {
            body {
                background-color: #f5f5f5;
                padding: 20px;
            }
            
            .print-container {
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
            }
            
            .print-actions {
                text-align: center;
                margin-bottom: 20px;
            }
            
            .btn {
                display: inline-block;
                padding: 10px 20px;
                margin: 0 10px;
                background-color: #007bff;
                color: white;
                text-decoration: none;
                border-radius: 5px;
                border: none;
                cursor: pointer;
                font-size: 14px;
            }
            
            .btn:hover {
                background-color: #0056b3;
            }
            
            .btn-secondary {
                background-color: #6c757d;
            }
            
            .btn-secondary:hover {
                background-color: #545b62;
            }
        }
    </style>
</head>
<body>
    <!-- 打印操作按钮（仅屏幕显示） -->
    <div class="print-actions no-print">
        <button class="btn" onclick="window.print()">打印生产单</button>
        <a href="{{ url_for('admin.order_detail', id=order.id) }}" class="btn btn-secondary">返回详情</a>
    </div>

    <div class="print-container">
        <!-- 公司标头 -->
        <div class="company-header">
            <div class="company-name">{{ site_config.company_name or '三联纸印刷有限公司' }}</div>
            <div class="company-info">
                电话：{{ site_config.company_phone or '************' }} | 
                地址：{{ site_config.company_address or '广东省深圳市南山区' }}
            </div>
        </div>

        <!-- 生产单标题 -->
        <div class="production-title">生产作业单</div>

        <!-- 订单信息 -->
        <div class="order-info-section">
            <div class="info-block">
                <h3>基本信息</h3>
                <div class="info-row">
                    <span class="info-label">订单号：</span>
                    <span class="info-value">{{ order.order_no }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">下单时间：</span>
                    <span class="info-value">{{ order.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">客户姓名：</span>
                    <span class="info-value">{{ order.user.real_name or order.user.username }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">联系电话：</span>
                    <span class="info-value">{{ order.user.phone or '未提供' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">生产优先级：</span>
                    <span class="info-value">
                        {% if order.status == 'processing' %}
                        <span class="priority-urgent">紧急</span>
                        {% else %}
                        <span class="priority-normal">正常</span>
                        {% endif %}
                    </span>
                </div>
            </div>

            <div class="info-block">
                <h3>交付信息</h3>
                <div class="info-row">
                    <span class="info-label">预计交期：</span>
                    <span class="info-value">{{ expected_delivery_date.strftime('%Y-%m-%d') if expected_delivery_date else '待确定' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">订单状态：</span>
                    <span class="info-value">{{ order.get_status_display() }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">总数量：</span>
                    <span class="info-value">{{ order.order_items.all() | sum(attribute='quantity') }} 件</span>
                </div>
                <div class="info-row">
                    <span class="info-label">总金额：</span>
                    <span class="info-value">¥{{ "%.2f"|format(order.final_amount) }}</span>
                </div>
                {% if order.shipping_address %}
                <div class="info-row">
                    <span class="info-label">收货地址：</span>
                    <span class="info-value">{{ order.shipping_address }}</span>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- 生产明细表格 -->
        <table class="production-table">
            <thead>
                <tr>
                    <th style="width: 40px;">序号</th>
                    <th style="width: 150px;">产品名称</th>
                    <th style="width: 200px;">生产规格要求</th>
                    <th style="width: 60px;">数量</th>
                    <th style="width: 100px;">工艺备注</th>
                    <th style="width: 80px;">完成签名</th>
                </tr>
            </thead>
            <tbody>
                {% for item in order.order_items %}
                <tr>
                    <td style="text-align: center; font-weight: bold;">{{ loop.index }}</td>
                    <td>
                        <div style="font-weight: bold; margin-bottom: 5px;">{{ item.product_name }}</div>
                        {% if item.product %}
                        <div style="font-size: 10px; color: #666;">
                            编号：{{ item.product.id }}
                        </div>
                        {% endif %}
                    </td>
                    <td>
                        <div class="production-specs">
                            {% if item.get_attributes_display() %}
                            <strong>规格：</strong>{{ item.get_attributes_display() }}
                            {% if item.has_custom_attributes() %}
                            <div style="margin-top: 3px; color: #d63384; font-weight: bold;">
                                ⚠️ 注意：包含客户自定义规格，请仔细核对！
                            </div>
                            {% endif %}
                            {% else %}
                            <strong>标准规格</strong>
                            {% endif %}
                        </div>
                        <div style="margin-top: 5px; font-size: 10px;">
                            单价：¥{{ "%.2f"|format(item.unit_price) }}
                        </div>
                    </td>
                    <td style="text-align: center; font-size: 16px; font-weight: bold; color: #e74c3c;">
                        {{ item.quantity }}
                    </td>
                    <td>
                        <div class="production-notes">
                            {% if item.product and item.product.description %}
                            {{ item.product.description[:50] }}...
                            {% else %}
                            按标准工艺制作
                            {% endif %}
                        </div>
                    </td>
                    <td style="text-align: center;">
                        <div style="height: 30px; border-bottom: 1px solid #ccc; margin: 5px 0;"></div>
                        <div style="font-size: 9px;">完成人签名</div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- 生产要求 -->
        <div class="production-requirements">
            <div class="requirements-title">生产技术要求</div>
            <div class="requirement-item">
                <strong>1. 质量标准：</strong>严格按照公司质量标准执行，确保产品质量合格率达到99%以上。
            </div>
            <div class="requirement-item">
                <strong>2. 工艺要求：</strong>按照客户规格要求进行生产，如有疑问及时与技术部门沟通。
            </div>
            <div class="requirement-item">
                <strong>3. 交期要求：</strong>严格控制生产进度，确保按时完成生产任务。
            </div>
            <div class="requirement-item">
                <strong>4. 安全要求：</strong>生产过程中严格遵守安全操作规程，确保人员和设备安全。
            </div>
            {% if order.notes %}
            <div class="requirement-item">
                <strong>5. 特殊要求：</strong>{{ order.notes }}
            </div>
            {% endif %}
        </div>

        <!-- 质量检查清单 -->
        <div class="quality-check">
            <div class="check-title">质量检查清单</div>
            <div class="check-item">
                <span class="check-box"></span>
                <span>原材料检查合格</span>
            </div>
            <div class="check-item">
                <span class="check-box"></span>
                <span>生产工艺执行正确</span>
            </div>
            <div class="check-item">
                <span class="check-box"></span>
                <span>产品尺寸规格符合要求</span>
            </div>
            <div class="check-item">
                <span class="check-box"></span>
                <span>产品外观质量合格</span>
            </div>
            <div class="check-item">
                <span class="check-box"></span>
                <span>数量清点准确</span>
            </div>
            <div class="check-item">
                <span class="check-box"></span>
                <span>包装标识正确</span>
            </div>
        </div>

        <!-- 签名区域 -->
        <div class="signature-area">
            <div class="signature-box">
                <div><strong>生产负责人</strong></div>
                <div class="signature-line"></div>
                <div>签名</div>
            </div>
            <div class="signature-box">
                <div><strong>质检员</strong></div>
                <div class="signature-line"></div>
                <div>签名</div>
            </div>
            <div class="signature-box">
                <div><strong>车间主任</strong></div>
                <div class="signature-line"></div>
                <div>签名</div>
            </div>
        </div>

        <!-- 页脚 -->
        <div class="print-footer">
            <div>生产单打印时间：<span id="print-time"></span></div>
            <div style="margin-top: 10px;">{{ site_config.company_name or '三联纸印刷有限公司' }} - 生产部</div>
        </div>
    </div>

    <script>
        // 设置打印时间
        document.getElementById('print-time').textContent = new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        
        // 自动打印功能（可选）
        function autoPrint() {
            window.print();
        }
        
        // 如果URL包含auto参数，自动打印
        if (window.location.search.includes('auto=1')) {
            setTimeout(autoPrint, 1000);
        }
    </script>
</body>
</html> 