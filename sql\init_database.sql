-- 印刷品商城数据库初始化脚本
-- 创建时间：2024年
-- 描述：包含用户、商品、订单、购物车等完整功能的数据库结构

-- 设置字符集
SET NAMES utf8mb4;
SET character_set_client = utf8mb4;

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS print_shop DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE print_shop;

-- 用户表
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(80) UNIQUE NOT NULL,
    email VARCHAR(120) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    real_name VARCHAR(50),
    avatar_url VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    is_admin BOOLEAN DEFAULT FALSE,
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login DATETIME,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at)
);

-- 用户地址表
CREATE TABLE user_addresses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    recipient_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    province VARCHAR(50) NOT NULL,
    city VARCHAR(50) NOT NULL,
    district VARCHAR(50) NOT NULL,
    address_detail VARCHAR(200) NOT NULL,
    postal_code VARCHAR(10),
    is_default BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_is_default (is_default)
);

-- 商品分类表
CREATE TABLE categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id INT,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL,
    INDEX idx_parent_id (parent_id),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_active (is_active)
);

-- 属性组表
CREATE TABLE attribute_groups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
    INDEX idx_category_id (category_id),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_active (is_active)
);

-- 属性表
CREATE TABLE attributes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    group_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    value VARCHAR(200) NOT NULL,
    price_modifier DECIMAL(10,2) DEFAULT 0.00,
    price_modifier_type ENUM('fixed', 'percentage') DEFAULT 'fixed',
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (group_id) REFERENCES attribute_groups(id) ON DELETE CASCADE,
    INDEX idx_group_id (group_id),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_active (is_active)
);

-- 商品表
CREATE TABLE products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    category_id INT,
    base_price DECIMAL(10,2) NOT NULL,
    min_quantity INT DEFAULT 1,
    max_quantity INT DEFAULT 999999,
    unit VARCHAR(20) DEFAULT '个',
    image_url VARCHAR(500),
    images JSON,
    is_active BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
    INDEX idx_category_id (category_id),
    INDEX idx_is_active (is_active),
    INDEX idx_is_featured (is_featured),
    INDEX idx_sort_order (sort_order),
    INDEX idx_created_at (created_at),
    INDEX idx_base_price (base_price)
);

-- 商品属性关联表
CREATE TABLE product_attributes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    attribute_id INT NOT NULL,
    is_required BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (attribute_id) REFERENCES attributes(id) ON DELETE CASCADE,
    UNIQUE KEY unique_product_attribute (product_id, attribute_id),
    INDEX idx_product_id (product_id),
    INDEX idx_attribute_id (attribute_id)
);

-- 数量折扣表
CREATE TABLE quantity_discounts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT,
    category_id INT,
    min_quantity INT NOT NULL,
    max_quantity INT,
    discount_type ENUM('fixed', 'percentage') NOT NULL,
    discount_value DECIMAL(10,2) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
    INDEX idx_product_id (product_id),
    INDEX idx_category_id (category_id),
    INDEX idx_min_quantity (min_quantity),
    INDEX idx_is_active (is_active)
);

-- 购物车表
CREATE TABLE cart_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL,
    selected_attributes JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_product (user_id, product_id),
    INDEX idx_user_id (user_id),
    INDEX idx_product_id (product_id)
);

-- 优惠券表
CREATE TABLE coupons (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    discount_type ENUM('fixed', 'percentage') NOT NULL,
    discount_value DECIMAL(10,2) NOT NULL,
    min_order_amount DECIMAL(10,2) DEFAULT 0.00,
    max_discount_amount DECIMAL(10,2),
    usage_limit INT,
    used_count INT DEFAULT 0,
    user_limit INT DEFAULT 1,
    valid_from DATETIME NOT NULL,
    valid_until DATETIME NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_code (code),
    INDEX idx_valid_from (valid_from),
    INDEX idx_valid_until (valid_until),
    INDEX idx_is_active (is_active)
);

-- 用户优惠券使用记录表
CREATE TABLE user_coupons (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    coupon_id INT NOT NULL,
    order_id INT,
    used_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (coupon_id) REFERENCES coupons(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_coupon_id (coupon_id),
    INDEX idx_order_id (order_id)
);

-- 订单表
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_no VARCHAR(50) UNIQUE NOT NULL,
    user_id INT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    shipping_fee DECIMAL(10,2) DEFAULT 0.00,
    final_amount DECIMAL(10,2) NOT NULL,
    status ENUM('pending', 'paid', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
    payment_method VARCHAR(50),
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
    coupon_id INT,
    -- 收货信息
    recipient_name VARCHAR(50) NOT NULL,
    recipient_phone VARCHAR(20) NOT NULL,
    shipping_province VARCHAR(50) NOT NULL,
    shipping_city VARCHAR(50) NOT NULL,
    shipping_district VARCHAR(50) NOT NULL,
    shipping_address VARCHAR(200) NOT NULL,
    shipping_postal_code VARCHAR(10),
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (coupon_id) REFERENCES coupons(id) ON DELETE SET NULL,
    INDEX idx_order_no (order_no),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_payment_status (payment_status),
    INDEX idx_created_at (created_at)
);

-- 订单项表
CREATE TABLE order_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    product_name VARCHAR(200) NOT NULL,
    product_attributes JSON,
    quantity INT NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT,
    INDEX idx_order_id (order_id),
    INDEX idx_product_id (product_id)
);

-- 系统配置表
CREATE TABLE system_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT,
    description VARCHAR(500),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_config_key (config_key)
);

-- 操作日志表
CREATE TABLE admin_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(50),
    record_id INT,
    old_data JSON,
    new_data JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);

-- 插入默认数据

-- 插入默认管理员用户
INSERT INTO users (username, email, password_hash, real_name, is_admin, is_active, email_verified) VALUES
('admin', '<EMAIL>', 'pbkdf2:sha256:600000$defaultsalt$defaulthash', '系统管理员', TRUE, TRUE, TRUE);

-- 插入默认分类
INSERT INTO categories (name, description, sort_order) VALUES
('纸箱包装', '各种规格的纸箱包装产品', 1),
('宣传册', '企业宣传册、产品手册等印刷品', 2),
('名片印刷', '商务名片、个人名片等', 3),
('标签贴纸', '各种材质的标签和贴纸', 4),
('包装材料', '包装用的各种辅助材料', 5);

-- 为第一个分类（纸箱包装）添加属性组
INSERT INTO attribute_groups (category_id, name, description, sort_order) VALUES
(1, '材质', '纸箱材质选择', 1),
(1, '尺寸规格', '纸箱尺寸选择', 2),
(1, '联数', '纸箱联数选择', 3),
(1, '封皮', '纸箱封皮选择', 4),
(1, '纸厚', '纸箱厚度选择', 5);

-- 为属性组添加具体属性
-- 材质属性
INSERT INTO attributes (group_id, name, value, price_modifier, sort_order) VALUES
(1, '材质', '木浆纸', 0.10, 1),
(1, '材质', '再生纸', 0.00, 2),
(1, '材质', '高强瓦楞纸', 0.20, 3);

-- 尺寸规格属性
INSERT INTO attributes (group_id, name, value, price_modifier, sort_order) VALUES
(2, '尺寸', '20×15×10cm', 0.00, 1),
(2, '尺寸', '30×25×20cm', 0.20, 2),
(2, '尺寸', '40×35×30cm', 0.50, 3);

-- 联数属性
INSERT INTO attributes (group_id, name, value, price_modifier, sort_order) VALUES
(3, '联数', '三联', 2.50, 1),
(3, '联数', '二联', 0.10, 2),
(3, '联数', '单联', 0.00, 3);

-- 封皮属性
INSERT INTO attributes (group_id, name, value, price_modifier, sort_order) VALUES
(4, '封皮', '80g空白封皮', 1.00, 1),
(4, '封皮', '纸箱原色', 0.00, 2),
(4, '封皮', '白色纸箱', 2.00, 3);

-- 纸厚属性
INSERT INTO attributes (group_id, name, value, price_modifier, sort_order) VALUES
(5, '纸厚', '3mm', 0.00, 1),
(5, '纸厚', '5mm', 0.50, 2),
(5, '纸厚', '7mm', 1.00, 3);

-- 插入示例产品
INSERT INTO products (name, description, category_id, base_price, min_quantity, max_quantity, unit, is_active, is_featured) VALUES
('定制纸箱', '高质量瓦楞纸箱，多种规格可选，适用于各种商品包装', 1, 2.50, 50, 10000, '个', TRUE, TRUE),
('企业宣传册', '精美企业宣传册，高品质印刷，多种装订方式', 2, 8.80, 100, 5000, '本', TRUE, TRUE),
('商务名片', '专业商务名片印刷，多种纸质和工艺可选', 3, 0.35, 100, 10000, '张', TRUE, FALSE),
('不干胶标签', '高品质不干胶标签，多种尺寸和材质', 4, 0.25, 1000, 50000, '张', TRUE, FALSE);

-- 为产品关联属性
INSERT INTO product_attributes (product_id, attribute_id, is_required) VALUES
-- 定制纸箱的属性
(1, 1, TRUE),  -- 材质
(1, 4, TRUE),  -- 尺寸
(1, 7, TRUE),  -- 联数
(1, 10, FALSE), -- 封皮
(1, 13, FALSE); -- 纸厚

-- 插入数量折扣
INSERT INTO quantity_discounts (product_id, min_quantity, max_quantity, discount_type, discount_value, is_active) VALUES
-- 纸箱折扣
(1, 100, 499, 'percentage', 5.00, TRUE),
(1, 500, 999, 'percentage', 10.00, TRUE),
(1, 1000, NULL, 'percentage', 15.00, TRUE),
-- 宣传册折扣
(2, 200, 499, 'percentage', 8.00, TRUE),
(2, 500, NULL, 'percentage', 12.00, TRUE);

-- 插入系统配置
INSERT INTO system_configs (config_key, config_value, description) VALUES
('site_name', '印刷品商城', '网站名称'),
('company_name', '三联纸印刷有限公司', '公司名称'),
('company_phone', '************', '客服电话'),
('company_email', '<EMAIL>', '客服邮箱'),
('free_shipping_amount', '200', '免运费金额'),
('default_shipping_fee', '15', '默认运费'),
('max_cart_items', '50', '购物车最大商品数量'),
('order_auto_confirm_days', '7', '订单自动确认天数');

-- 插入示例优惠券
INSERT INTO coupons (code, name, description, discount_type, discount_value, min_order_amount, usage_limit, valid_from, valid_until) VALUES
('WELCOME10', '新用户优惠券', '新用户专享10元优惠券', 'fixed', 10.00, 50.00, 1000, '2024-01-01 00:00:00', '2025-12-31 23:59:59'),
('SAVE5', '满100减5', '满100元可用的5元优惠券', 'fixed', 5.00, 100.00, 500, '2024-01-01 00:00:00', '2025-12-31 23:59:59'),
('DISCOUNT15', '全场85折', '全场商品85折优惠', 'percentage', 15.00, 200.00, 200, '2024-01-01 00:00:00', '2025-06-30 23:59:59');

COMMIT; 