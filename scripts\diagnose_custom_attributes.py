#!/usr/bin/env python3
"""
自定义属性功能诊断脚本
检查和诊断自定义属性功能的各个环节
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_database_schema():
    """检查数据库模式"""
    print("\n🔍 检查数据库模式...")
    
    try:
        from app import create_app
        from models import db
        from models.product import AttributeGroup
        from models.order import OrderItem
        
        app = create_app()
        with app.app_context():
            # 检查AttributeGroup表的自定义字段
            inspector = db.inspect(db.engine)
            columns = inspector.get_columns('attribute_groups')
            column_names = [col['name'] for col in columns]
            
            required_columns = [
                'allow_custom_input',
                'custom_input_type',
                'custom_input_label',
                'custom_input_placeholder',
                'custom_validation_pattern',
                'custom_validation_message',
                'custom_price_formula',
                'custom_price_modifier',
                'custom_price_modifier_type'
            ]
            
            missing_columns = [col for col in required_columns if col not in column_names]
            
            if missing_columns:
                print(f"❌ AttributeGroup表缺少字段: {missing_columns}")
                return False
            else:
                print("✅ AttributeGroup表结构正确")
            
            # 检查OrderItem表的product_attributes字段
            order_columns = inspector.get_columns('order_items')
            order_column_names = [col['name'] for col in order_columns]
            
            if 'product_attributes' not in order_column_names:
                print("❌ OrderItem表缺少product_attributes字段")
                return False
            else:
                print("✅ OrderItem表结构正确")
                
        return True
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {str(e)}")
        return False

def check_custom_attribute_groups():
    """检查自定义属性组配置"""
    print("\n🔍 检查自定义属性组配置...")
    
    try:
        from app import create_app
        from models.product import AttributeGroup
        
        app = create_app()
        with app.app_context():
            # 查找支持自定义输入的属性组
            custom_groups = AttributeGroup.query.filter_by(allow_custom_input=True).all()
            
            if not custom_groups:
                print("⚠️  没有找到支持自定义输入的属性组")
                print("   建议：在后台管理中配置一些属性组支持自定义输入")
                return False
            
            print(f"✅ 找到 {len(custom_groups)} 个支持自定义输入的属性组:")
            
            for group in custom_groups:
                print(f"   • {group.name} ({group.custom_input_type})")
                
                # 检查配置完整性
                config_issues = []
                if not group.custom_input_label:
                    config_issues.append("缺少自定义输入标签")
                if not group.custom_input_placeholder:
                    config_issues.append("缺少占位符")
                
                if config_issues:
                    print(f"     ⚠️  配置问题: {', '.join(config_issues)}")
                else:
                    print(f"     ✅ 配置完整")
                    
        return True
        
    except Exception as e:
        print(f"❌ 属性组检查失败: {str(e)}")
        return False

def check_existing_orders():
    """检查现有订单中的自定义数据"""
    print("\n🔍 检查现有订单中的自定义数据...")
    
    try:
        from app import create_app
        from models.order import OrderItem
        
        app = create_app()
        with app.app_context():
            # 查找包含自定义属性的订单项
            all_items = OrderItem.query.all()
            custom_items = [item for item in all_items if item.has_custom_attributes()]
            
            print(f"📊 统计信息:")
            print(f"   • 总订单项数量: {len(all_items)}")
            print(f"   • 包含自定义属性的订单项: {len(custom_items)}")
            
            if custom_items:
                print(f"\n✅ 找到 {len(custom_items)} 个包含自定义属性的订单项:")
                
                for i, item in enumerate(custom_items[:5]):  # 只显示前5个
                    print(f"   {i+1}. 订单 {item.order.order_no} - {item.product_name}")
                    print(f"      属性: {item.get_attributes_display()}")
                    
                    # 检查属性详情
                    attr_details = item.get_attributes_detail()
                    custom_attrs = [attr for attr in attr_details if attr['is_custom']]
                    print(f"      自定义属性数量: {len(custom_attrs)}")
                    
                    for attr in custom_attrs:
                        print(f"        - {attr['name']}: {attr['display_value']}")
                
                if len(custom_items) > 5:
                    print(f"   ... 还有 {len(custom_items) - 5} 个订单项")
            else:
                print("⚠️  没有找到包含自定义属性的订单")
                print("   建议：创建一个测试订单来验证功能")
                
        return True
        
    except Exception as e:
        print(f"❌ 订单检查失败: {str(e)}")
        return False

def test_custom_data_processing():
    """测试自定义数据处理逻辑"""
    print("\n🧪 测试自定义数据处理逻辑...")
    
    try:
        from app import create_app
        from models.product import AttributeGroup
        from collections import OrderedDict
        
        app = create_app()
        with app.app_context():
            # 模拟测试数据
            test_data = {
                'selected_attributes': ['custom_尺寸', 'custom_材质'],
                'attribute_quantities': {
                    'custom_group_尺寸': '300x400mm',
                    'custom_group_材质': '特殊纸张'
                }
            }
            
            print(f"📝 测试数据: {test_data}")
            
            # 模拟处理逻辑
            product_attributes = OrderedDict()
            custom_groups_found = []
            
            for custom_attr_id in test_data['selected_attributes']:
                if custom_attr_id.startswith('custom_'):
                    group_key = custom_attr_id.replace('custom_', '')
                    custom_value_key = f'custom_group_{group_key}'
                    custom_value = test_data['attribute_quantities'].get(custom_value_key)
                    
                    if custom_value:
                        group_name = group_key.replace('_', ' ')
                        group = AttributeGroup.query.filter_by(name=group_name).first()
                        
                        if group and group.allow_custom_input:
                            display_value = f"{custom_value} (自定义)"
                            product_attributes[group_name] = display_value
                            custom_groups_found.append({
                                'group_name': group_name,
                                'custom_value': custom_value,
                                'display_value': display_value
                            })
                            print(f"✅ 成功处理: {group_name} = {display_value}")
                        else:
                            print(f"❌ 属性组不存在或不支持自定义: {group_name}")
                    else:
                        print(f"❌ 自定义值为空: {custom_value_key}")
            
            print(f"\n📊 处理结果:")
            print(f"   • 处理的自定义属性数量: {len(custom_groups_found)}")
            print(f"   • 最终属性数据: {dict(product_attributes)}")
            
            return len(custom_groups_found) > 0
        
    except Exception as e:
        print(f"❌ 数据处理测试失败: {str(e)}")
        return False

def check_api_endpoints():
    """检查API端点"""
    print("\n🔍 检查API端点...")
    
    try:
        from app import create_app
        
        app = create_app()
        
        # 检查路由
        required_routes = [
            '/api/validate-custom-input',
            '/api/debug/order-attributes/<int:order_id>',
            '/api/test/custom-data-processing',
            '/test/multiple-custom-attributes'
        ]
        
        with app.app_context():
            available_routes = []
            for rule in app.url_map.iter_rules():
                available_routes.append(rule.rule)
            
            for route in required_routes:
                # 简化检查，去掉参数部分
                route_pattern = route.replace('<int:order_id>', '').replace('<', '').replace('>', '')
                found = any(route_pattern in available_route for available_route in available_routes)
                
                if found:
                    print(f"✅ API端点存在: {route}")
                else:
                    print(f"❌ API端点缺失: {route}")
            
        return True
        
    except Exception as e:
        print(f"❌ API端点检查失败: {str(e)}")
        return False

def generate_recommendations():
    """生成建议"""
    print("\n💡 建议和下一步:")
    print("1. 如果没有自定义属性组，请在后台管理中创建并配置")
    print("2. 访问 /test/multiple-custom-attributes 进行功能测试")
    print("3. 创建一个包含自定义属性的测试订单")
    print("4. 在订单详情页面检查自定义数据显示")
    print("5. 在后台管理中查看订单的自定义属性")
    
    print("\n🔗 有用的链接:")
    print("   • 多属性测试: /test/multiple-custom-attributes")
    print("   • 功能测试: /test/custom-attributes")
    print("   • 显示修复验证: /test/display-fix")
    print("   • 后台属性管理: /admin/attribute-groups")
    print("   • 后台自定义报告: /admin/custom-attributes-report")

def main():
    """主函数"""
    print("🔧 自定义属性功能诊断")
    print("=" * 50)
    
    checks = [
        ("数据库模式", check_database_schema),
        ("自定义属性组配置", check_custom_attribute_groups),
        ("现有订单数据", check_existing_orders),
        ("数据处理逻辑", test_custom_data_processing),
        ("API端点", check_api_endpoints)
    ]
    
    results = []
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name} 检查异常: {str(e)}")
            results.append((check_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 诊断总结:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   • {check_name}: {status}")
    
    print(f"\n总体状态: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("🎉 所有检查都通过！自定义属性功能应该正常工作。")
    else:
        print("⚠️  存在一些问题，请根据上述信息进行修复。")
    
    generate_recommendations()

if __name__ == '__main__':
    main()
