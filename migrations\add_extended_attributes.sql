-- 扩展属性系统数据库迁移脚本
-- 添加新的属性类型和字段支持

-- 1. 扩展 input_type 枚举类型
ALTER TABLE attributes MODIFY COLUMN input_type ENUM(
    'select',           -- 下拉选择
    'radio',            -- 单选按钮
    'checkbox',         -- 多选框
    'text_input',       -- 文本输入
    'number_input',     -- 数字输入
    'textarea',         -- 长文本输入
    'file_upload',      -- 文件上传
    'quantity_select',  -- 数量选择器
    'date_input',       -- 日期输入
    'color_picker'      -- 颜色选择器
) DEFAULT 'select';

-- 2. 添加属性分类字段
ALTER TABLE attributes ADD COLUMN attribute_category ENUM('standard', 'custom') DEFAULT 'standard' AFTER input_type;

-- 3. 添加扩展验证规则字段
ALTER TABLE attributes ADD COLUMN validation_rules JSON AFTER validation_message;

-- 4. 添加扩展价格计算规则字段
ALTER TABLE attributes ADD COLUMN price_calculation_rules JSON AFTER validation_rules;

-- 5. 添加文件上传相关字段
ALTER TABLE attributes ADD COLUMN file_accept_types VARCHAR(200) AFTER price_calculation_rules;
ALTER TABLE attributes ADD COLUMN file_max_size INT AFTER file_accept_types;

-- 6. 添加数字输入相关字段
ALTER TABLE attributes ADD COLUMN number_min DECIMAL(15,5) AFTER file_max_size;
ALTER TABLE attributes ADD COLUMN number_max DECIMAL(15,5) AFTER number_min;
ALTER TABLE attributes ADD COLUMN number_step DECIMAL(15,5) AFTER number_max;

-- 7. 添加多选相关字段
ALTER TABLE attributes ADD COLUMN is_multiple BOOLEAN DEFAULT FALSE AFTER number_step;
ALTER TABLE attributes ADD COLUMN max_selections INT AFTER is_multiple;

-- 8. 添加索引以提高查询性能
ALTER TABLE attributes ADD INDEX idx_attribute_category (attribute_category);
ALTER TABLE attributes ADD INDEX idx_input_type (input_type);

-- 9. 创建属性值存储表（用于存储用户的自定义输入值）
CREATE TABLE IF NOT EXISTS attribute_values (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_item_id INT NOT NULL,
    attribute_id INT NOT NULL,
    attribute_value TEXT,
    custom_quantity INT DEFAULT 1,
    file_path VARCHAR(500),
    file_size INT,
    file_type VARCHAR(50),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (attribute_id) REFERENCES attributes(id) ON DELETE CASCADE,
    INDEX idx_order_item_id (order_item_id),
    INDEX idx_attribute_id (attribute_id)
);

-- 10. 创建属性组合价格表（用于特殊的组合定价）
CREATE TABLE IF NOT EXISTS attribute_combination_prices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    attribute_combination JSON NOT NULL,  -- 存储属性组合，如: {"size": "A4", "material": "coated", "quantity": 1000}
    price_modifier DECIMAL(15,5) DEFAULT 0,
    price_modifier_type ENUM('fixed', 'percentage') DEFAULT 'fixed',
    min_quantity INT DEFAULT 1,
    max_quantity INT DEFAULT 999999,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    INDEX idx_product_id (product_id),
    INDEX idx_is_active (is_active)
);

-- 11. 更新现有数据，设置默认的属性分类
UPDATE attributes SET attribute_category = 'standard' WHERE attribute_category IS NULL;

-- 12. 为现有的自定义输入属性设置正确的分类
UPDATE attributes SET attribute_category = 'custom' WHERE is_custom_input = TRUE;

-- 13. 添加一些示例验证规则（可选）
-- 为文本输入类型添加默认验证规则
UPDATE attributes SET validation_rules = JSON_OBJECT(
    'required', FALSE,
    'min_length', 1,
    'max_length', 100
) WHERE input_type = 'text_input' AND validation_rules IS NULL;

-- 为数字输入类型添加默认验证规则
UPDATE attributes SET validation_rules = JSON_OBJECT(
    'required', FALSE,
    'min', 1,
    'max', 999999
) WHERE input_type = 'number_input' AND validation_rules IS NULL;

-- 14. 添加一些示例价格计算规则（可选）
-- 为按字符计费的属性添加价格规则
UPDATE attributes SET price_calculation_rules = JSON_OBJECT(
    'type', 'per_character',
    'price_per_char', 0.1
) WHERE input_type = 'text_input' AND price_formula IS NULL AND price_calculation_rules IS NULL;

-- 15. 创建属性模板表（用于快速创建常用属性组合）
CREATE TABLE IF NOT EXISTS attribute_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    category_id INT,
    template_data JSON NOT NULL,  -- 存储属性模板配置
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
    INDEX idx_category_id (category_id),
    INDEX idx_is_active (is_active)
);

-- 16. 插入一些常用的属性模板示例
INSERT INTO attribute_templates (name, description, template_data) VALUES
('名片标准属性', '名片印刷的标准属性组合', JSON_OBJECT(
    'groups', JSON_ARRAY(
        JSON_OBJECT(
            'name', '尺寸',
            'attributes', JSON_ARRAY(
                JSON_OBJECT('name', '标准名片', 'value', '90x54mm', 'price_modifier', 0),
                JSON_OBJECT('name', '欧式名片', 'value', '85x54mm', 'price_modifier', 2.0)
            )
        ),
        JSON_OBJECT(
            'name', '材质',
            'attributes', JSON_ARRAY(
                JSON_OBJECT('name', '300g铜版纸', 'value', '300g_coated', 'price_modifier', 0),
                JSON_OBJECT('name', '350g艺术纸', 'value', '350g_art', 'price_modifier', 5.0)
            )
        )
    )
)),
('海报自定义属性', '海报印刷的自定义属性组合', JSON_OBJECT(
    'groups', JSON_ARRAY(
        JSON_OBJECT(
            'name', '自定义尺寸',
            'attributes', JSON_ARRAY(
                JSON_OBJECT('name', '宽度', 'input_type', 'number_input', 'unit', 'cm', 'min', 10, 'max', 200),
                JSON_OBJECT('name', '高度', 'input_type', 'number_input', 'unit', 'cm', 'min', 10, 'max', 300)
            )
        ),
        JSON_OBJECT(
            'name', '设计文件',
            'attributes', JSON_ARRAY(
                JSON_OBJECT('name', '上传设计', 'input_type', 'file_upload', 'accept', '.pdf,.ai,.psd', 'max_size', 52428800)
            )
        )
    )
));

-- 17. 提交更改
COMMIT;
