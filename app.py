import os
from flask import Flask, render_template, request
from flask_wtf.csrf import CSRFProtect, generate_csrf
from config import config
from models import init_app as init_models

def create_app(config_name=None):
    """应用工厂函数"""
    if config_name is None:
        config_name = os.environ.get('FLASK_CONFIG') or 'default'
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])

    # 初始化扩展
    init_models(app)

    # 重新启用CSRF保护
    csrf = CSRFProtect(app)
    
    # 注册蓝图
    from views.auth import auth_bp
    from views.main import main_bp
    from views.admin import admin_bp
    from views.attribute_api import attribute_api

    app.register_blueprint(auth_bp)
    app.register_blueprint(main_bp)
    app.register_blueprint(admin_bp)
    app.register_blueprint(attribute_api)
    
    # 错误处理
    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return render_template('errors/500.html'), 500
    
    # 为特定API端点免除CSRF保护（在所有蓝图注册后）
    try:
        # 使用应用级别的view_functions
        csrf.exempt(app.view_functions['main.calculate_price'])
    except KeyError:
        # 如果函数不存在，跳过免除
        pass

    # 免除属性API的CSRF保护
    csrf.exempt(attribute_api)
    
    # 模板全局变量
    @app.context_processor
    def inject_globals():
        from models.system import SystemConfig
        from models.product import Category, Attribute
        from flask_login import current_user
        from utils.timezone_helper import format_china_time, format_china_date, format_china_datetime_short
        
        # 网站配置
        site_config = {
            'site_name': SystemConfig.get_config('site_name', '印刷品商城'),
            'site_description': SystemConfig.get_config('site_description', '专业的印刷品在线销售平台'),
            'contact_phone': SystemConfig.get_config('contact_phone', '************'),
            'contact_email': SystemConfig.get_config('contact_email', '<EMAIL>'),
            'company_name': SystemConfig.get_config('company_name', '三联纸印刷有限公司'),
            'company_phone': SystemConfig.get_config('company_phone', '************'),
            'company_address': SystemConfig.get_config('company_address', '广东省深圳市南山区')
        }
        
        # 主要分类（用于导航）
        main_categories = Category.query.filter_by(
            is_active=True,
            parent_id=None
        ).order_by(Category.sort_order).limit(10).all()
        
        # 购物车信息（已移除购物车功能）
        cart_info = {'count': 0, 'total': 0}
        
        # 辅助函数
        def get_attribute_by_id(attr_id):
            """根据ID获取属性"""
            try:
                return Attribute.query.get(attr_id)
            except:
                return None
        
        return {
            'site_config': site_config,
            'main_categories': main_categories,
            'cart_info': cart_info,
            'csrf_token': generate_csrf,
            'get_attribute_by_id': get_attribute_by_id,
            # 时区转换函数
            'format_china_time': format_china_time,
            'format_china_date': format_china_date,
            'format_china_datetime_short': format_china_datetime_short
        }
    
    return app

if __name__ == '__main__':
    app = create_app()
    
    # 创建数据库表
    with app.app_context():
        from models import db
        db.create_all()
        
        # 创建默认管理员用户
        from models.user import User
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                real_name='系统管理员',
                is_admin=True
            )
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print("默认管理员账户已创建: admin/admin123")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
