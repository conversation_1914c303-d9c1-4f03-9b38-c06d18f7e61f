/* 现代化管理后台样式 */

:root {
    --admin-primary: #667eea;
    --admin-primary-dark: #5a67d8;
    --admin-secondary: #764ba2;
    --admin-success: #48bb78;
    --admin-warning: #ed8936;
    --admin-danger: #f56565;
    --admin-info: #4299e1;
    
    --admin-bg-light: #f8fafc;
    --admin-bg-dark: #1a202c;
    --admin-surface-light: rgba(255, 255, 255, 0.95);
    --admin-surface-dark: rgba(45, 55, 72, 0.95);
    
    --admin-text-light: #2d3748;
    --admin-text-dark: #e2e8f0;
    --admin-text-muted-light: #718096;
    --admin-text-muted-dark: #a0aec0;
    
    --admin-border-light: rgba(0, 0, 0, 0.1);
    --admin-border-dark: rgba(255, 255, 255, 0.1);
    
    --admin-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --admin-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --admin-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    --admin-radius: 12px;
    --admin-radius-lg: 16px;
    --admin-radius-xl: 20px;
    
    --admin-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 基础样式 */
.admin-body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: linear-gradient(135deg, var(--admin-bg-light) 0%, #e2e8f0 100%);
    min-height: 100vh;
    color: var(--admin-text-light);
}

[data-bs-theme="dark"] .admin-body {
    background: linear-gradient(135deg, var(--admin-bg-dark) 0%, #2d3748 100%);
    color: var(--admin-text-dark);
}

/* 现代化导航栏 */
.admin-navbar {
    background: var(--admin-surface-light) !important;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--admin-border-light);
    box-shadow: var(--admin-shadow);
    padding: 1rem 0;
}

[data-bs-theme="dark"] .admin-navbar {
    background: var(--admin-surface-dark) !important;
    border-bottom-color: var(--admin-border-dark);
}

.admin-brand {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
    font-size: 1.5rem;
    background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-secondary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-decoration: none;
}

.admin-user-menu {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: var(--admin-radius);
    transition: var(--admin-transition);
    text-decoration: none;
    color: var(--admin-text-light) !important;
}

[data-bs-theme="dark"] .admin-user-menu {
    color: var(--admin-text-dark) !important;
}

.admin-user-menu:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-secondary) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
}

/* 现代化侧边栏 */
.admin-sidebar {
    position: fixed;
    top: 80px;
    bottom: 0;
    left: 0;
    z-index: 100;
    background: var(--admin-surface-light);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-right: 1px solid var(--admin-border-light);
    box-shadow: var(--admin-shadow-lg);
}

[data-bs-theme="dark"] .admin-sidebar {
    background: var(--admin-surface-dark);
    border-right-color: var(--admin-border-dark);
}

.sidebar-content {
    height: 100%;
    overflow-y: auto;
    padding: 1.5rem 0;
}

.sidebar-content::-webkit-scrollbar {
    width: 4px;
}

.sidebar-content::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb {
    background: var(--admin-border-light);
    border-radius: 2px;
}

[data-bs-theme="dark"] .sidebar-content::-webkit-scrollbar-thumb {
    background: var(--admin-border-dark);
}

.menu-section {
    margin-bottom: 2rem;
}

.menu-section-title {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--admin-text-muted-light);
    padding: 0 1.5rem;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
}

[data-bs-theme="dark"] .menu-section-title {
    color: var(--admin-text-muted-dark);
}

.menu-item {
    margin: 0.25rem 1rem;
}

.menu-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    border-radius: var(--admin-radius);
    text-decoration: none;
    color: var(--admin-text-light);
    transition: var(--admin-transition);
    position: relative;
    overflow: hidden;
}

[data-bs-theme="dark"] .menu-link {
    color: var(--admin-text-dark);
}

.menu-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-secondary) 100%);
    opacity: 0;
    transition: var(--admin-transition);
    z-index: -1;
}

.menu-link:hover::before,
.menu-link.active::before {
    opacity: 0.1;
}

.menu-link:hover,
.menu-link.active {
    color: var(--admin-primary);
    transform: translateX(4px);
}

.menu-link.active {
    font-weight: 600;
    box-shadow: var(--admin-shadow);
}

.menu-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    font-size: 0.875rem;
}

.menu-text {
    font-size: 0.875rem;
    font-weight: 500;
}

/* 主要内容区域 */
.admin-main {
    margin-left: 240px;
    padding-top: 80px;
    min-height: 100vh;
}

.admin-content {
    padding: 2rem;
}

@media (max-width: 767.98px) {
    .admin-sidebar {
        transform: translateX(-100%);
        transition: var(--admin-transition);
    }
    
    .admin-main {
        margin-left: 0;
        padding-top: 80px;
    }
    
    .admin-content {
        padding: 1rem;
    }
}

/* 现代化警告框 */
.alerts-container {
    margin-bottom: 2rem;
}

.alert-modern {
    border: none;
    border-radius: var(--admin-radius-lg);
    box-shadow: var(--admin-shadow);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-left: 4px solid;
    padding: 1rem 1.5rem;
}

.alert-content {
    display: flex;
    align-items: center;
}

.alert-icon {
    margin-right: 0.75rem;
    font-size: 1.125rem;
}

.alert-message {
    flex: 1;
    font-weight: 500;
}

.alert-success.alert-modern {
    background: rgba(72, 187, 120, 0.1);
    border-left-color: var(--admin-success);
    color: #2f855a;
}

.alert-danger.alert-modern {
    background: rgba(245, 101, 101, 0.1);
    border-left-color: var(--admin-danger);
    color: #c53030;
}

.alert-info.alert-modern {
    background: rgba(66, 153, 225, 0.1);
    border-left-color: var(--admin-info);
    color: #2c5282;
}

.alert-warning.alert-modern {
    background: rgba(237, 137, 54, 0.1);
    border-left-color: var(--admin-warning);
    color: #c05621;
}

/* 现代化下拉菜单 */
.dropdown-menu-modern {
    border: none;
    border-radius: var(--admin-radius-lg);
    box-shadow: var(--admin-shadow-xl);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: var(--admin-surface-light);
    padding: 0.5rem;
    border: 1px solid var(--admin-border-light);
    min-width: 200px;
}

[data-bs-theme="dark"] .dropdown-menu-modern {
    background: var(--admin-surface-dark);
    border-color: var(--admin-border-dark);
}

.dropdown-item-modern {
    border-radius: var(--admin-radius);
    margin-bottom: 0.25rem;
    transition: var(--admin-transition);
    color: var(--admin-text-light);
    font-weight: 500;
    padding: 0.5rem 0.75rem;
}

[data-bs-theme="dark"] .dropdown-item-modern {
    color: var(--admin-text-dark);
}

.dropdown-item-modern:hover {
    background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-secondary) 100%);
    color: white;
    transform: translateX(2px);
}

.dropdown-item-modern.text-danger:hover {
    background: var(--admin-danger);
    color: white;
}

/* 页面进入动画 */
.page-enter {
    animation: pageEnter 0.6s ease-out;
}

@keyframes pageEnter {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 现代化卡片 */
.card-modern {
    border: none;
    border-radius: var(--admin-radius-lg);
    box-shadow: var(--admin-shadow);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    background: var(--admin-surface-light);
    transition: var(--admin-transition);
}

[data-bs-theme="dark"] .card-modern {
    background: var(--admin-surface-dark);
}

.card-modern:hover {
    transform: translateY(-2px);
    box-shadow: var(--admin-shadow-lg);
}

/* 现代化按钮 */
.btn-modern {
    border-radius: var(--admin-radius);
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    transition: var(--admin-transition);
    border: none;
}

.btn-modern:hover {
    transform: translateY(-1px);
    box-shadow: var(--admin-shadow);
}

.btn-primary.btn-modern {
    background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-secondary) 100%);
}

.btn-success.btn-modern {
    background: var(--admin-success);
}

.btn-warning.btn-modern {
    background: var(--admin-warning);
}

.btn-danger.btn-modern {
    background: var(--admin-danger);
}

.btn-info.btn-modern {
    background: var(--admin-info);
}

/* 页面头部样式 */
.page-header {
    margin-bottom: 2rem;
    padding: 1.5rem 0;
}

.page-title h1 {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
    color: var(--admin-text-light);
    margin-bottom: 0.5rem;
}

[data-bs-theme="dark"] .page-title h1 {
    color: var(--admin-text-dark);
}

.page-title p {
    font-size: 0.875rem;
    color: var(--admin-text-muted-light);
    margin: 0;
}

[data-bs-theme="dark"] .page-title p {
    color: var(--admin-text-muted-dark);
}

.page-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

/* 筛选区域样式 */
.filter-section {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.filter-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--admin-text-light);
    margin: 0;
    white-space: nowrap;
}

[data-bs-theme="dark"] .filter-label {
    color: var(--admin-text-dark);
}

.form-select-modern {
    border-radius: var(--admin-radius);
    border: 1px solid var(--admin-border-light);
    background: var(--admin-surface-light);
    color: var(--admin-text-light);
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
    min-width: 200px;
    transition: var(--admin-transition);
}

[data-bs-theme="dark"] .form-select-modern {
    border-color: var(--admin-border-dark);
    background: var(--admin-surface-dark);
    color: var(--admin-text-dark);
}

.form-select-modern:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
}

.empty-state-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-secondary) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.empty-state-title {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    color: var(--admin-text-light);
    margin-bottom: 0.75rem;
}

[data-bs-theme="dark"] .empty-state-title {
    color: var(--admin-text-dark);
}

.empty-state-description {
    color: var(--admin-text-muted-light);
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
}

[data-bs-theme="dark"] .empty-state-description {
    color: var(--admin-text-muted-dark);
}

.empty-state-tip {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1rem;
    background: rgba(255, 193, 7, 0.1);
    border-radius: var(--admin-radius);
    color: var(--admin-warning);
    font-size: 0.8rem;
    margin-bottom: 1.5rem;
}

.empty-state-action {
    margin-top: 1rem;
}

/* 数据头部样式 */
.data-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--admin-border-light);
}

[data-bs-theme="dark"] .data-header {
    border-bottom-color: var(--admin-border-dark);
}

.data-title h6 {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    color: var(--admin-text-light);
}

[data-bs-theme="dark"] .data-title h6 {
    color: var(--admin-text-dark);
}

.data-stats {
    display: flex;
    gap: 0.5rem;
}

.badge-modern {
    border-radius: var(--admin-radius);
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
}

/* 现代化表格样式 */
.table-container {
    border-radius: var(--admin-radius-lg);
    overflow: hidden;
    box-shadow: var(--admin-shadow);
}

.table-modern {
    margin: 0;
    background: var(--admin-surface-light);
    border-radius: var(--admin-radius-lg);
}

[data-bs-theme="dark"] .table-modern {
    background: var(--admin-surface-dark);
}

.table-modern thead th {
    background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-secondary) 100%);
    color: white;
    border: none;
    font-weight: 600;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 1rem 0.75rem;
}

.table-modern tbody tr {
    border-bottom: 1px solid var(--admin-border-light);
    transition: var(--admin-transition);
}

[data-bs-theme="dark"] .table-modern tbody tr {
    border-bottom-color: var(--admin-border-dark);
}

.table-modern tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: translateY(-1px);
}

.table-modern td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border: none;
    color: var(--admin-text-light);
}

[data-bs-theme="dark"] .table-modern td {
    color: var(--admin-text-dark);
}

/* 表格单元格样式 */
.table-id {
    width: 80px;
}

.id-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    background: rgba(102, 126, 234, 0.1);
    color: var(--admin-primary);
    border-radius: var(--admin-radius);
    font-size: 0.75rem;
    font-weight: 600;
}

.name-cell {
    display: flex;
    align-items: center;
}

.name-text {
    font-weight: 500;
    color: var(--admin-text-light);
}

[data-bs-theme="dark"] .name-text {
    color: var(--admin-text-dark);
}

.desc-text {
    font-size: 0.875rem;
    color: var(--admin-text-muted-light);
}

[data-bs-theme="dark"] .desc-text {
    color: var(--admin-text-muted-dark);
}

.count-cell {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.sort-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    background: rgba(0, 0, 0, 0.05);
    border-radius: var(--admin-radius);
    font-size: 0.75rem;
    font-weight: 500;
}

[data-bs-theme="dark"] .sort-badge {
    background: rgba(255, 255, 255, 0.1);
}

.date-text {
    font-size: 0.875rem;
    color: var(--admin-text-muted-light);
}

[data-bs-theme="dark"] .date-text {
    color: var(--admin-text-muted-dark);
}

.action-buttons {
    display: flex;
    gap: 0.25rem;
}

.action-buttons .btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
}

/* 现代化模态框样式 */
.modal-modern .modal-content {
    border: none;
    border-radius: var(--admin-radius-xl);
    box-shadow: var(--admin-shadow-xl);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: var(--admin-surface-light);
}

[data-bs-theme="dark"] .modal-modern .modal-content {
    background: var(--admin-surface-dark);
}

.modal-header-modern {
    border-bottom: 1px solid var(--admin-border-light);
    padding: 1.5rem;
}

[data-bs-theme="dark"] .modal-header-modern {
    border-bottom-color: var(--admin-border-dark);
}

.modal-title-wrapper {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.modal-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 193, 7, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
}

.modal-body-modern {
    padding: 1.5rem;
}

.delete-confirmation {
    text-align: center;
}

.confirmation-text {
    font-size: 1rem;
    color: var(--admin-text-light);
    margin-bottom: 1rem;
}

[data-bs-theme="dark"] .confirmation-text {
    color: var(--admin-text-dark);
}

.warning-box {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1rem;
    background: rgba(245, 101, 101, 0.1);
    border-radius: var(--admin-radius);
    color: var(--admin-danger);
    font-size: 0.875rem;
}

.modal-footer-modern {
    border-top: 1px solid var(--admin-border-light);
    padding: 1.5rem;
    gap: 0.75rem;
}

[data-bs-theme="dark"] .modal-footer-modern {
    border-top-color: var(--admin-border-dark);
}

.btn-close-modern {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--admin-text-muted-light);
    transition: var(--admin-transition);
}

[data-bs-theme="dark"] .btn-close-modern {
    color: var(--admin-text-muted-dark);
}

.btn-close-modern:hover {
    color: var(--admin-danger);
    transform: scale(1.1);
}
