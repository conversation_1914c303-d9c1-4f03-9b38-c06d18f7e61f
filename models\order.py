import json
from datetime import datetime
from . import db

class ProductAttribute(db.Model):
    __tablename__ = 'product_attributes'
    
    id = db.Column(db.Integer, primary_key=True)
    product_id = db.Column(db.<PERSON>te<PERSON>, db.<PERSON><PERSON>ey('products.id'), nullable=False, index=True)
    attribute_id = db.Column(db.Integer, db.Foreign<PERSON>ey('attributes.id'), nullable=False, index=True)
    is_required = db.Column(db.<PERSON>, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    __table_args__ = (db.UniqueConstraint('product_id', 'attribute_id', name='unique_product_attribute'),)
    
    def __repr__(self):
        return f'<ProductAttribute {self.product_id}-{self.attribute_id}>'

class QuantityDiscount(db.Model):
    __tablename__ = 'quantity_discounts'
    
    id = db.Column(db.Integer, primary_key=True)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), index=True)
    category_id = db.Column(db.Integer, db.ForeignKey('categories.id'), index=True)
    min_quantity = db.Column(db.Integer, nullable=False, index=True)
    max_quantity = db.Column(db.Integer)
    discount_type = db.Column(db.Enum('fixed', 'percentage'), nullable=False)
    discount_value = db.Column(db.Numeric(15, 5), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def get_discount_display(self):
        """获取折扣显示文本"""
        if self.discount_type == 'percentage':
            return f"{self.discount_value:.5f}%"
        else:
            return f"¥{self.discount_value:.5f}"
    
    def __repr__(self):
        return f'<QuantityDiscount {self.min_quantity}+ -> {self.get_discount_display()}>'

class Order(db.Model):
    __tablename__ = 'orders'
    
    id = db.Column(db.Integer, primary_key=True)
    order_no = db.Column(db.String(50), unique=True, nullable=False, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    total_amount = db.Column(db.Numeric(15, 5), nullable=False)
    discount_amount = db.Column(db.Numeric(15, 5), default=0.00000)
    final_amount = db.Column(db.Numeric(15, 5), nullable=False)
    status = db.Column(db.Enum('pending', 'paid', 'processing', 'shipped', 'delivered', 'cancelled'), 
                      default='pending', index=True)
    payment_method = db.Column(db.String(50))
    payment_status = db.Column(db.Enum('pending', 'paid', 'failed', 'refunded'), default='pending')
    
    # 现有数据库的地址字段（简化版）
    shipping_address = db.Column(db.Text)
    shipping_phone = db.Column(db.String(20))
    shipping_name = db.Column(db.String(50))
    
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    order_items = db.relationship('OrderItem', backref='order', lazy='dynamic', cascade='all, delete-orphan')
    
    # 兼容新地址格式的属性
    @property
    def recipient_name(self):
        return self.shipping_name
    
    @recipient_name.setter
    def recipient_name(self, value):
        self.shipping_name = value
    
    @property
    def recipient_phone(self):
        return self.shipping_phone
    
    @recipient_phone.setter
    def recipient_phone(self, value):
        self.shipping_phone = value
    
    def set_full_address(self, province, city, district, address, postal_code=None):
        """设置完整地址"""
        address_parts = []
        if province:
            address_parts.append(province)
        if city:
            address_parts.append(city)
        if district:
            address_parts.append(district)
        if address:
            address_parts.append(address)
        if postal_code:
            address_parts.append(f"邮编: {postal_code}")
        
        self.shipping_address = " ".join(address_parts)
    
    def get_address_parts(self):
        """解析地址为各个部分（用于显示）"""
        if not self.shipping_address:
            return {
                'province': '',
                'city': '',
                'district': '',
                'address': '',
                'postal_code': ''
            }

        # 检查是否是地区编码格式（如：360000 361700 112121）
        if self.shipping_address and len(self.shipping_address.split()) >= 3:
            parts = self.shipping_address.split()
            # 检查前三个部分是否都是数字（地区编码）
            if all(part.isdigit() for part in parts[:3]):
                return self._parse_region_codes(parts)

        # 简单的地址解析，实际项目中可能需要更复杂的逻辑
        parts = self.shipping_address.split()
        return {
            'full_address': self.shipping_address,
            'province': parts[0] if len(parts) > 0 else '',
            'city': parts[1] if len(parts) > 1 else '',
            'district': parts[2] if len(parts) > 2 else '',
            'address': ' '.join(parts[3:]) if len(parts) > 3 else '',
            'postal_code': ''
        }

    def _parse_region_codes(self, parts):
        """解析地区编码为汉字名称"""
        from models.system import Region

        province_code = parts[0] if len(parts) > 0 else ''
        city_code = parts[1] if len(parts) > 1 else ''
        district_code = parts[2] if len(parts) > 2 else ''
        detail_address = ' '.join(parts[3:]) if len(parts) > 3 else ''

        # 查询地区名称
        province_name = ''
        city_name = ''
        district_name = ''

        try:
            if province_code:
                province = Region.query.filter_by(id=province_code).first()
                if province:
                    province_name = province.name

            if city_code:
                city = Region.query.filter_by(id=city_code).first()
                if city:
                    city_name = city.name

            if district_code:
                district = Region.query.filter_by(id=district_code).first()
                if district:
                    district_name = district.name
        except Exception as e:
            print(f"解析地区编码失败: {e}")

        # 构建完整地址
        address_parts = []
        if province_name:
            address_parts.append(province_name)
        if city_name:
            address_parts.append(city_name)
        if district_name:
            address_parts.append(district_name)
        if detail_address:
            address_parts.append(detail_address)

        full_address = ' '.join(address_parts) if address_parts else self.shipping_address

        return {
            'full_address': full_address,
            'province': province_name,
            'city': city_name,
            'district': district_name,
            'address': detail_address,
            'postal_code': ''
        }
    
    def get_status_display(self):
        """获取状态显示文本"""
        status_map = {
            'pending': '待付款',
            'paid': '已付款',
            'processing': '处理中',
            'shipped': '已发货',
            'delivered': '已送达',
            'cancelled': '已取消'
        }
        return status_map.get(self.status, self.status)
    
    def get_payment_status_display(self):
        """获取支付状态显示文本"""
        status_map = {
            'pending': '待支付',
            'paid': '已支付',
            'failed': '支付失败',
            'refunded': '已退款'
        }
        return status_map.get(self.payment_status, self.payment_status)
    
    def can_cancel(self):
        """是否可以取消"""
        return self.status in ['pending', 'paid']
    
    def can_pay(self):
        """是否可以支付"""
        return self.status == 'pending' and self.payment_status == 'pending'
    
    def __repr__(self):
        return f'<Order {self.order_no}>'

class OrderItem(db.Model):
    __tablename__ = 'order_items'

    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False, index=True)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False, index=True)
    product_name = db.Column(db.String(200), nullable=False)
    product_attributes = db.Column(db.JSON)
    quantity = db.Column(db.Integer, nullable=False)
    unit_price = db.Column(db.Numeric(15, 5), nullable=False)
    total_price = db.Column(db.Numeric(15, 5), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def get_attributes_display(self):
        """获取属性显示文本"""
        if not self.product_attributes:
            return ""

        if isinstance(self.product_attributes, str):
            try:
                attrs = json.loads(self.product_attributes)
            except:
                return self.product_attributes
        else:
            attrs = self.product_attributes

        # 定义常见的属性组顺序 - 可根据业务需求调整
        attribute_order = {
            "规格": 0,
            "尺寸": 1,
            "颜色": 2,
            "材质": 3,
            "联数": 4,
            "工艺": 5,
            "纸张": 6,
            "每本张数": 7,
            "数量": 8
        }

        # 对属性组按预定义顺序排序，未定义的放在后面
        sorted_attrs = sorted(
            attrs.items(),
            key=lambda x: attribute_order.get(x[0], 999)
        )

        display_list = []
        for attr_name, attr_value in sorted_attrs:
            # 特殊处理自定义输入，使其更加醒目
            if "(自定义)" in str(attr_value):
                display_list.append(f"【{attr_name}】: {attr_value}")
            else:
                display_list.append(f"{attr_name}: {attr_value}")

        return "; ".join(display_list)

    def get_attributes_detail(self):
        """获取属性详细信息（用于管理后台或详细显示）"""
        if not self.product_attributes:
            return []

        if isinstance(self.product_attributes, str):
            try:
                attrs = json.loads(self.product_attributes)
            except:
                return []
        else:
            attrs = self.product_attributes

        detail_list = []
        for attr_name, attr_value in attrs.items():
            is_custom = "(自定义)" in str(attr_value)
            detail_list.append({
                'name': attr_name,
                'value': attr_value,
                'is_custom': is_custom,
                'display_value': attr_value.replace(" (自定义)", "") if is_custom else attr_value
            })

        return detail_list

    def has_custom_attributes(self):
        """检查是否包含自定义属性"""
        if not self.product_attributes:
            return False

        if isinstance(self.product_attributes, str):
            return "(自定义)" in self.product_attributes
        else:
            for value in self.product_attributes.values():
                if "(自定义)" in str(value):
                    return True
        return False

    def __repr__(self):
        return f'<OrderItem {self.product_name} x{self.quantity}>'
