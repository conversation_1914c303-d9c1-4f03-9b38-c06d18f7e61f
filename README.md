# 印刷品商城系统

一个基于Flask的现代化印刷品在线销售平台，支持灵活的商品属性配置和自定义输入功能。

## 🚀 功能特点

- **商品管理**: 支持多分类商品管理，灵活的属性配置
- **自定义属性**: 支持标准属性和用户自定义输入
- **价格计算**: 智能价格计算引擎，支持复杂定价规则
- **订单管理**: 完整的订单流程和状态管理
- **用户系统**: 用户注册、登录、地址管理
- **管理后台**: 功能完善的管理界面
- **响应式设计**: 支持桌面端和移动端

## 📁 项目结构

```
print/
├── app.py                 # 主应用入口
├── config.py             # 配置文件
├── requirements.txt      # 依赖包列表
├── print_service.db     # SQLite数据库文件
│
├── docs/                # 文档目录
│   ├── attribute_system_design.md      # 属性系统设计文档
│   └── attribute_system_test_report.md # 测试报告
│
├── models/              # 数据模型
│   ├── __init__.py
│   ├── user.py         # 用户模型
│   ├── product.py      # 商品模型
│   ├── order.py        # 订单模型
│   └── system.py       # 系统模型
│
├── views/               # 视图控制器
│   ├── __init__.py
│   ├── main.py         # 主页面视图
│   ├── auth.py         # 认证视图
│   ├── admin.py        # 管理后台视图
│   └── attribute_api.py # 属性API视图
│
├── forms/               # 表单定义
│   ├── __init__.py
│   ├── auth.py         # 认证表单
│   └── admin.py        # 管理表单
│
├── templates/           # 模板文件
│   ├── base.html       # 基础模板
│   ├── base_modern.html # 现代化基础模板
│   ├── main/           # 主页面模板
│   ├── auth/           # 认证页面模板
│   ├── admin/          # 管理后台模板
│   ├── user/           # 用户页面模板
│   └── errors/         # 错误页面模板
│
├── static/              # 静态资源
│   ├── css/            # 样式文件
│   ├── js/             # JavaScript文件
│   ├── images/         # 图片资源
│   ├── uploads/        # 上传文件
│   └── vendor/         # 第三方库
│
├── services/            # 服务层
│   └── payment.py      # 支付服务
│
├── utils/               # 工具函数
│   ├── __init__.py
│   ├── file_upload.py  # 文件上传工具
│   ├── import_regions.py # 地区导入工具
│   ├── logger.py       # 日志工具
│   └── timezone_helper.py # 时区工具
│
├── migrations/          # 数据库迁移文件
│   ├── add_category_attribute_groups.sql
│   ├── add_custom_input_attributes.sql
│   ├── add_custom_input_to_attribute_groups.sql
│   ├── add_display_type_to_attribute_groups.sql
│   ├── add_extended_attributes.sql
│   ├── add_formula_to_attributes.sql
│   └── update_price_precision.sql
│
├── sql/                 # SQL脚本
│   ├── create_database.sql    # 数据库创建脚本
│   ├── create_tables_part2.sql # 表结构补充
│   └── init_database.sql      # 数据库初始化脚本
│
└── logs/                # 日志目录
```

## 🛠️ 安装和运行

### 1. 环境要求
- Python 3.8+
- SQLite 3

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 初始化数据库
```bash
# 使用初始化脚本（推荐）
python -c "
import sqlite3
with open('sql/init_database.sql', 'r', encoding='utf-8') as f:
    sql = f.read()
conn = sqlite3.connect('print_service.db')
conn.executescript(sql)
conn.close()
print('数据库初始化完成')
"
```

### 4. 运行应用
```bash
python app.py
```

访问 http://127.0.0.1:5000

## 🔧 核心功能

### 商品属性系统
- **标准属性**: 单选、多选、下拉框等预定义选项
- **自定义输入**: 文本、数字、文件上传等用户输入
- **价格计算**: 支持固定价格、百分比、公式计算
- **实时验证**: 前端实时验证用户输入

### 管理后台
- **商品管理**: 商品的增删改查和属性配置
- **分类管理**: 商品分类的层级管理
- **属性管理**: 属性组和属性的配置管理
- **订单管理**: 订单状态跟踪和处理

### 用户功能
- **商品浏览**: 分类浏览和搜索功能
- **购物下单**: 属性选择和价格计算
- **订单跟踪**: 订单状态查看和管理
- **地址管理**: 收货地址的管理

## 📚 技术栈

- **后端**: Flask + SQLAlchemy + SQLite
- **前端**: Bootstrap 5 + jQuery + 原生JavaScript
- **数据库**: SQLite（可扩展到MySQL/PostgreSQL）
- **样式**: CSS3 + 响应式设计
- **部署**: 支持Docker部署

## 🎯 特色功能

1. **灵活的属性系统**: 支持10种不同的属性输入类型
2. **智能价格计算**: 复杂的价格计算引擎
3. **现代化UI**: 响应式设计，支持移动端
4. **完整的管理后台**: 功能齐全的管理界面
5. **扩展性强**: 模块化设计，易于扩展

## 📝 开发说明

- 主要配置在 `config.py` 中
- 数据库模型在 `models/` 目录下
- 视图控制器在 `views/` 目录下
- 前端模板在 `templates/` 目录下
- 静态资源在 `static/` 目录下

## 🔗 相关文档

- [属性系统设计文档](docs/attribute_system_design.md)
- [测试报告](docs/attribute_system_test_report.md)

## 📄 许可证

MIT License
