// 全局JavaScript功能

$(document).ready(function() {
    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // 初始化弹出框
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // 启用导航栏滚动隐藏
    let lastScrollTop = 0;
    const navbar = $('.navbar');
    
    $(window).scroll(function() {
        let scrollTop = $(this).scrollTop();
        
        if (scrollTop > lastScrollTop && scrollTop > 100) {
            // 向下滚动，隐藏导航栏
            navbar.addClass('navbar-hidden');
        } else {
            // 向上滚动，显示导航栏
            navbar.removeClass('navbar-hidden');
        }
        
        lastScrollTop = scrollTop;
    });

    // 自动关闭警告框
    $('.alert').each(function() {
        const alert = $(this);
        setTimeout(function() {
            alert.fadeOut();
        }, 5000);
    });
});

// 商品价格计算器
class PriceCalculator {
    constructor(productId) {
        this.productId = productId;
        this.selectedAttributes = [];
        this.quantity = 1;
        console.log('PriceCalculator initialized for product:', productId);
        this.init();
    }

    init() {
        this.bindEvents();
        this.initializeDefaultSelections();
        this.updatePrice();
    }

    initializeDefaultSelections() {
        // 为每个属性组选择第一个选项作为默认选项
        $('.attribute-group').each((index, group) => {
            const $group = $(group);
            const $firstOption = $group.find('.attribute-option').first();
            if ($firstOption.length && !$group.find('.attribute-option.selected').length) {
                $firstOption.addClass('selected');
                const attrId = $firstOption.data('id');
                if (attrId) {
                    this.selectedAttributes.push(attrId);
                    console.log('Default attribute selected:', attrId);
                }
            }
        });

        // 初始化数量
        const quantityInput = $('.quantity-input');
        if (quantityInput.length) {
            this.quantity = parseInt(quantityInput.val()) || 1;
        }

        console.log('Initial state - Quantity:', this.quantity, 'Attributes:', this.selectedAttributes);
    }
    
    bindEvents() {
        // 属性选择 - 优化事件处理，确保一次点击生效
        $(document).on('click', '.attribute-option, .spec-option', (e) => {
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation(); // 阻止其他事件监听器

            const $option = $(e.currentTarget);
            const groupName = $option.data('group');
            const attrId = $option.data('id');

            console.log('Attribute clicked:', {groupName, attrId, hasClass: $option.hasClass('selected')});

            // 如果已经选中，不做任何操作
            if ($option.hasClass('selected')) {
                console.log('Option already selected, ignoring');
                return false;
            }

            // 单选逻辑：移除同组其他选项的选中状态
            $(`.attribute-option[data-group="${groupName}"], .spec-option[data-group="${groupName}"]`).removeClass('selected');

            // 选中当前选项
            $option.addClass('selected');

            // 更新选中的属性：先移除同组的其他属性
            this.selectedAttributes = this.selectedAttributes.filter(id => {
                const $existingOption = $(`[data-id="${id}"]`);
                return $existingOption.data('group') !== groupName;
            });

            // 添加新选中的属性
            if (attrId) {
                this.selectedAttributes.push(attrId);
                console.log('Updated selected attributes:', this.selectedAttributes);
            }

            // 立即更新价格
            this.updatePrice();

            return false; // 确保事件不会继续传播
        });
        
        // 数量变更
        $(document).on('click', '.quantity-btn', (e) => {
            const $btn = $(e.target);
            const $input = $btn.siblings('.quantity-input');
            const action = $btn.data('action');
            let quantity = parseInt($input.val()) || 1;

            console.log('Quantity button clicked:', action, 'current:', quantity);

            if (action === 'increase') {
                quantity++;
            } else if (action === 'decrease' && quantity > 1) {
                quantity--;
            }

            $input.val(quantity);
            this.quantity = quantity;
            console.log('Quantity updated to:', this.quantity);
            this.updatePrice();
        });

        $(document).on('input', '.quantity-input', (e) => {
            const quantity = parseInt($(e.target).val()) || 1;
            this.quantity = Math.max(1, quantity);
            $(e.target).val(this.quantity);
            console.log('Quantity input changed to:', this.quantity);
            this.updatePrice();
        });
    }
    
    updatePrice() {
        const data = {
            product_id: this.productId,
            quantity: this.quantity,
            selected_attributes: this.selectedAttributes
        };

        console.log('Sending price calculation request:', data);

        $.ajax({
            url: '/api/calculate-price',
            method: 'POST',
            contentType: 'application/json',
            headers: {
                'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
            },
            data: JSON.stringify(data),
            success: (response) => {
                console.log('Price calculation response:', response);
                this.displayPrice(response);
            },
            error: (xhr) => {
                console.error('价格计算失败:', xhr.responseJSON || xhr.responseText);
                console.error('Status:', xhr.status);
            }
        });
    }
    
    displayPrice(priceData) {
        console.log('Displaying price data:', priceData);

        // 更新主价格数值（只更新数字部分）
        if ($('.price-value').length) {
            $('.price-value').text(priceData.total_price.toFixed(2));
            console.log('Updated .price-value to:', priceData.total_price.toFixed(2));
        }

        // 更新单价信息
        if ($('.price-info').length) {
            $('.price-info').html(`<small class="text-muted">单价: ¥${priceData.unit_price.toFixed(2)}</small>`);
            console.log('Updated .price-info to:', `单价: ¥${priceData.unit_price.toFixed(2)}`);
        }

        // 显示折扣信息
        if (priceData.discount) {
            const discountText = priceData.discount.type === 'percentage'
                ? `优惠 ${priceData.discount.value}%`
                : `优惠 ¥${priceData.discount.value}`;
            $('.discount-info').text(discountText).show();
            console.log('Showing discount:', discountText);
        } else {
            $('.discount-info').hide();
        }

        // 添加价格变化动画效果
        $('.price-value, .price-info').addClass('price-updated');
        setTimeout(() => {
            $('.price-value, .price-info').removeClass('price-updated');
        }, 300);
    }
}

// 商品搜索功能
class ProductSearch {
    constructor() {
        this.init();
    }
    
    init() {
        // 搜索建议
        $('#search-input').on('input', this.debounce((e) => {
            const query = $(e.target).val();
            if (query.length >= 2) {
                this.getSuggestions(query);
            } else {
                this.hideSuggestions();
            }
        }, 300));
        
        // 点击外部隐藏建议
        $(document).on('click', (e) => {
            if (!$(e.target).closest('.search-container').length) {
                this.hideSuggestions();
            }
        });
    }
    
    getSuggestions(query) {
        $.ajax({
            url: '/api/search-suggestions',
            method: 'GET',
            data: { q: query },
            success: (suggestions) => {
                this.displaySuggestions(suggestions);
            },
            error: () => {
                this.hideSuggestions();
            }
        });
    }
    
    displaySuggestions(suggestions) {
        if (suggestions.length === 0) {
            this.hideSuggestions();
            return;
        }
        
        const $container = $('.search-suggestions');
        if ($container.length === 0) {
            $('#search-input').after('<div class="search-suggestions"></div>');
        }
        
        const html = suggestions.map(item => 
            `<div class="suggestion-item" data-value="${item.name}">${item.name}</div>`
        ).join('');
        
        $('.search-suggestions').html(html).show();
        
        // 绑定点击事件
        $('.suggestion-item').on('click', (e) => {
            const value = $(e.target).data('value');
            $('#search-input').val(value);
            this.hideSuggestions();
            // 可以直接跳转到搜索结果
            window.location.href = `/products?search=${encodeURIComponent(value)}`;
        });
    }
    
    hideSuggestions() {
        $('.search-suggestions').hide();
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// 图片懒加载
class LazyLoader {
    constructor() {
        this.init();
    }
    
    init() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        } else {
            // 降级处理
            document.querySelectorAll('img[data-src]').forEach(img => {
                img.src = img.dataset.src;
            });
        }
    }
}

// 表单验证增强
class FormValidator {
    static validateForm(formSelector) {
        const $form = $(formSelector);
        let isValid = true;
        
        $form.find('[required]').each(function() {
            const $field = $(this);
            const value = $field.val().trim();
            
            if (!value) {
                FormValidator.showFieldError($field, '此字段为必填项');
                isValid = false;
            } else {
                FormValidator.clearFieldError($field);
            }
        });
        
        // 邮箱验证
        $form.find('input[type="email"]').each(function() {
            const $field = $(this);
            const email = $field.val().trim();
            
            if (email && !FormValidator.isValidEmail(email)) {
                FormValidator.showFieldError($field, '请输入有效的邮箱地址');
                isValid = false;
            }
        });
        
        return isValid;
    }
    
    static showFieldError($field, message) {
        $field.addClass('is-invalid');
        $field.siblings('.invalid-feedback').remove();
        $field.after(`<div class="invalid-feedback">${message}</div>`);
    }
    
    static clearFieldError($field) {
        $field.removeClass('is-invalid');
        $field.siblings('.invalid-feedback').remove();
    }
    
    static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
}

// 页面加载完成后初始化
$(document).ready(function() {
    console.log('Document ready, initializing...');

    // 初始化搜索功能
    new ProductSearch();

    // 初始化懒加载
    new LazyLoader();

    // 商品详情页价格计算器
    console.log('Checking for product detail page:', $('.product-detail').length, 'productId:', window.productId);
    if ($('.product-detail').length && window.productId) {
        console.log('Initializing PriceCalculator for product:', window.productId);
        window.priceCalculator = new PriceCalculator(window.productId);
    }
});

// 全局函数
window.addToCart = function(productId) {
    const quantity = parseInt($('.quantity-input').val()) || 1;
    const selectedAttributes = [];

    // 更新选择器以匹配新的CSS类名
    $('.spec-option.selected').each(function() {
        selectedAttributes.push($(this).data('id'));
    });

    // 如果没有选择属性，也检查旧的选择器
    if (selectedAttributes.length === 0) {
        $('.attribute-option.selected').each(function() {
            selectedAttributes.push($(this).data('id'));
        });
    }

    console.log('Adding to cart:', {
        productId: productId,
        quantity: quantity,
        selectedAttributes: selectedAttributes
    });

    ShoppingCart.addToCart(productId, quantity, selectedAttributes);
};

window.validateAndSubmit = function(formSelector) {
    if (FormValidator.validateForm(formSelector)) {
        $(formSelector).submit();
    }
};
